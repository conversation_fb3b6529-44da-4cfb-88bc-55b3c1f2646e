{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "PromptHub", "version": "1.0.0", "identifier": "com.prompthub.desktop", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devUrl": "http://localhost:1420", "frontendDist": "../dist"}, "app": {"windows": [{"title": "PromptHub", "width": 800, "height": 600, "minWidth": 700, "minHeight": 500, "resizable": true, "fullscreen": false, "decorations": false, "transparent": true, "alwaysOnTop": true, "center": true, "skipTaskbar": false, "visible": true, "focus": true}], "security": {"csp": null}, "macOSPrivateApi": true}, "plugins": {"globalShortcut": {"shortcuts": {"CmdOrCtrl+Shift+P": "toggle-app"}}}}
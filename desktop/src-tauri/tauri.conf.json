{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "PromptHub", "version": "1.0.0", "identifier": "com.prompthub.desktop", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devUrl": "http://localhost:1420", "frontendDist": "../dist"}, "app": {"windows": [{"title": "PromptHub", "width": 900, "height": 800, "minWidth": 800, "minHeight": 700, "resizable": true, "fullscreen": false, "decorations": false, "transparent": true, "alwaysOnTop": true, "center": true, "skipTaskbar": true, "visible": false, "focus": false}], "trayIcon": {"iconPath": "icons/icon.png", "iconAsTemplate": true, "menuOnLeftClick": true, "tooltip": "PromptHub - AI提示词管理工具"}, "security": {"csp": null}, "macOSPrivateApi": true}, "plugins": {"globalShortcut": {"shortcuts": {"CmdOrCtrl+Shift+P": "toggle-app"}}, "shell": {"open": true}}}
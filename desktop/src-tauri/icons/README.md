# 图标文件

这个目录应该包含以下图标文件：

- `32x32.png` - 32x32 像素的 PNG 图标
- `128x128.png` - 128x128 像素的 PNG 图标  
- `<EMAIL>` - 256x256 像素的 PNG 图标（高分辨率）
- `icon.icns` - macOS 图标文件
- `icon.ico` - Windows 图标文件

## 临时解决方案

如果没有图标文件，可以：

1. 从 Tauri 示例项目复制图标文件
2. 或者暂时注释掉 tauri.conf.json 中的 icon 配置
3. 使用在线工具生成图标文件

## 生成图标

可以使用以下工具生成图标：
- [Tauri Icon Generator](https://tauri.app/v1/guides/features/icons)
- [App Icon Generator](https://appicon.co/)
- [IconKitchen](https://icon.kitchen/)

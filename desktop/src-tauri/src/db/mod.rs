use sqlx::{sqlite::SqlitePool, Pool, Sqlite};
use std::sync::OnceLock;

static DB_POOL: OnceLock<Pool<Sqlite>> = OnceLock::new();

pub async fn init_database() -> Result<(), sqlx::Error> {
    // 创建数据库连接池，使用内存数据库进行开发测试
    let database_url = "sqlite::memory:";
    let pool = SqlitePool::connect(database_url).await?;
    
    // 运行数据库迁移
    run_migrations(&pool).await?;
    
    // 存储连接池
    DB_POOL.set(pool).map_err(|_| sqlx::Error::Configuration("Failed to set database pool".into()))?;
    
    Ok(())
}

pub fn get_db_pool() -> &'static Pool<Sqlite> {
    DB_POOL.get().expect("Database pool not initialized")
}

async fn run_migrations(pool: &Pool<Sqlite>) -> Result<(), sqlx::Error> {
    // 创建认证令牌表
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS auth_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            access_token TEXT NOT NULL,
            refresh_token TEXT,
            expires_at INTEGER NOT NULL,
            created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
            updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
        )
        "#,
    )
    .execute(pool)
    .await?;
    
    // 创建提示词缓存表
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS cached_prompts (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            description TEXT,
            category TEXT NOT NULL,
            tags TEXT NOT NULL, -- JSON array
            user_id TEXT NOT NULL,
            is_public BOOLEAN NOT NULL DEFAULT 0,
            likes INTEGER NOT NULL DEFAULT 0,
            downloads INTEGER NOT NULL DEFAULT 0,
            views INTEGER NOT NULL DEFAULT 0,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL,
            synced_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
        )
        "#,
    )
    .execute(pool)
    .await?;
    
    // 创建应用配置表
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS app_settings (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL,
            updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
        )
        "#,
    )
    .execute(pool)
    .await?;
    
    // 创建使用历史表
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS usage_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            prompt_id TEXT NOT NULL,
            user_input TEXT NOT NULL,
            used_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
        )
        "#,
    )
    .execute(pool)
    .await?;
    
    println!("Database migrations completed successfully");
    Ok(())
}

use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

pub struct ApiClient {
    client: Client,
    base_url: String,
}

impl ApiClient {
    pub fn new(base_url: String) -> Self {
        Self {
            client: Client::new(),
            base_url,
        }
    }
    
    pub async fn get<T>(&self, endpoint: &str, token: Option<&str>) -> Result<T, reqwest::Error>
    where
        T: for<'de> Deserialize<'de>,
    {
        let url = format!("{}{}", self.base_url, endpoint);
        let mut request = self.client.get(&url);
        
        if let Some(token) = token {
            request = request.header("Authorization", format!("Bearer {}", token));
        }
        
        let response = request.send().await?;
        response.json::<T>().await
    }
    
    pub async fn post<T, U>(&self, endpoint: &str, body: &T, token: Option<&str>) -> Result<U, reqwest::Error>
    where
        T: Serialize,
        U: for<'de> Deserialize<'de>,
    {
        let url = format!("{}{}", self.base_url, endpoint);
        let mut request = self.client.post(&url).json(body);
        
        if let Some(token) = token {
            request = request.header("Authorization", format!("Bearer {}", token));
        }
        
        let response = request.send().await?;
        response.json::<U>().await
    }
}

// 全局API客户端实例
lazy_static::lazy_static! {
    pub static ref API_CLIENT: ApiClient = ApiClient::new(
        std::env::var("API_BASE_URL")
            .unwrap_or_else(|_| "http://localhost:4000/api".to_string())
    );
}

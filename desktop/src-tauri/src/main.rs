// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod commands;
mod db;
mod services;
mod utils;

use tauri::{Manager, Emitter};
use tauri_plugin_global_shortcut::{GlobalShortcutExt, ShortcutState};

#[tokio::main]
async fn main() {
    // 初始化数据库
    db::init_database().await.expect("Failed to initialize database");

    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            commands::auth::login,
            commands::auth::logout,
            commands::auth::verify_token,
            commands::search::search_prompts,
            commands::prompts::get_prompt_detail,
            commands::prompts::record_usage,
            commands::system::inject_text,
            commands::system::copy_to_clipboard,
            commands::system::show_window,
            commands::system::hide_window,
        ])
        .setup(|app| {
            // 强制显示主窗口
            if let Some(window) = app.get_webview_window("main") {
                let _ = window.show();
                let _ = window.unminimize();
                let _ = window.set_focus();
                let _ = window.center();

                // 不要总是置顶，只在需要时置顶
                let _ = window.set_always_on_top(false);

                println!("Window should be visible now");
            }

            // 初始化全局快捷键插件
            #[cfg(desktop)]
            {
                use tauri_plugin_global_shortcut::{Code, Modifiers, Shortcut, ShortcutState};

                let app_handle = app.handle().clone();
                app.handle().plugin(
                    tauri_plugin_global_shortcut::Builder::new().with_handler(move |_app, shortcut, event| {
                        println!("Global shortcut triggered: {:?}", shortcut);
                        if event.state() == ShortcutState::Pressed {
                            println!("Global shortcut pressed!");
                            let _ = app_handle.emit("global-shortcut-pressed", ());
                        }
                    })
                    .build(),
                )?;

                // 注册快捷键 - 在macOS上使用Cmd+Shift+P，其他平台使用Ctrl+Shift+P
                #[cfg(target_os = "macos")]
                let shortcut = Shortcut::new(Some(Modifiers::META | Modifiers::SHIFT), Code::KeyP);
                #[cfg(not(target_os = "macos"))]
                let shortcut = Shortcut::new(Some(Modifiers::CONTROL | Modifiers::SHIFT), Code::KeyP);
                match app.global_shortcut().register(shortcut) {
                    Ok(_) => println!("Global shortcut registered successfully"),
                    Err(e) => println!("Failed to register global shortcut: {}", e),
                }
            }

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}



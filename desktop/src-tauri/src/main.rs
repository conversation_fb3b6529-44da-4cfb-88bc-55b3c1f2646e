// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod commands;
mod db;
mod services;
mod utils;

use tauri::{Manager, Emitter, AppHandle, WindowEvent};
use tauri_plugin_global_shortcut::{GlobalShortcutExt, ShortcutState};
use std::collections::HashMap;
use std::sync::Mutex;

#[tokio::main]
async fn main() {
    // 初始化数据库
    db::init_database().await.expect("Failed to initialize database");

    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            commands::auth::login,
            commands::auth::logout,
            commands::auth::verify_token,
            commands::auth::get_current_user,
            commands::search::search_prompts,
            commands::prompts::get_prompt_detail,
            commands::prompts::record_usage,
            commands::system::inject_text,
            commands::system::copy_to_clipboard,
            commands::system::show_window,
            commands::system::hide_window,
            commands::http::http_request,
            commands::storage::get_storage_item,
            commands::storage::set_storage_item,
            commands::storage::remove_storage_item,
            commands::storage::clear_storage,
            commands::storage::get_all_storage_keys,
        ])
        .manage(commands::storage::Storage::new(HashMap::new()))
        .setup(|app| {
            // 设置系统托盘
            let app_handle = app.handle().clone();

            // 创建托盘菜单
            let user_info_item = tauri::menu::MenuItemBuilder::with_id("user_info", "Eric (<EMAIL>)")
                .enabled(false)
                .build(app)?;
            let show_item = tauri::menu::MenuItemBuilder::with_id("show", "显示 PromptHub").build(app)?;
            let hide_item = tauri::menu::MenuItemBuilder::with_id("hide", "隐藏窗口 ⌘⇧P").build(app)?;
            let website_item = tauri::menu::MenuItemBuilder::with_id("website", "访问网站").build(app)?;
            let settings_item = tauri::menu::MenuItemBuilder::with_id("settings", "设置").build(app)?;
            let logout_item = tauri::menu::MenuItemBuilder::with_id("logout", "退出登录").build(app)?;
            let quit_item = tauri::menu::MenuItemBuilder::with_id("quit", "退出应用").build(app)?;

            let tray_menu = tauri::menu::MenuBuilder::new(app)
                .item(&user_info_item)
                .separator()
                .item(&show_item)
                .item(&hide_item)
                .separator()
                .item(&website_item)
                .item(&settings_item)
                .separator()
                .item(&logout_item)
                .item(&quit_item)
                .build()?;

            // 创建系统托盘
            let _tray = tauri::tray::TrayIconBuilder::new()
                .menu(&tray_menu)
                .on_menu_event(move |app, event| {
                    match event.id().as_ref() {
                        "show" => {
                            if let Some(window) = app.get_webview_window("main") {
                                let _ = window.show();
                                let _ = window.unminimize();
                                let _ = window.set_focus();
                                let _ = window.center();
                            }
                        }
                        "hide" => {
                            if let Some(window) = app.get_webview_window("main") {
                                let _ = window.hide();
                            }
                        }
                        "website" => {
                            // 打开 PromptHub 网站
                            let _ = std::process::Command::new("open")
                                .arg("https://prompthub.xin")
                                .spawn();
                        }
                        "settings" => {
                            // 显示窗口并切换到设置页面
                            if let Some(window) = app.get_webview_window("main") {
                                let _ = window.show();
                                let _ = window.unminimize();
                                let _ = window.set_focus();
                                let _ = window.center();
                                // 可以发送事件到前端切换到设置页面
                                let _ = window.emit("navigate-to-settings", ());
                            }
                        }
                        "logout" => {
                            // 发送退出登录事件到前端
                            if let Some(window) = app.get_webview_window("main") {
                                let _ = window.emit("logout-requested", ());
                            }
                        }
                        "quit" => {
                            app.exit(0);
                        }
                        _ => {}
                    }
                })
                .on_tray_icon_event(|tray, event| {
                    if let tauri::tray::TrayIconEvent::Click {
                        button: tauri::tray::MouseButton::Left,
                        button_state: tauri::tray::MouseButtonState::Up,
                        ..
                    } = event {
                        let app = tray.app_handle();
                        if let Some(window) = app.get_webview_window("main") {
                            if window.is_visible().unwrap_or(false) {
                                let _ = window.hide();
                            } else {
                                let _ = window.show();
                                let _ = window.unminimize();
                                let _ = window.set_focus();
                                let _ = window.center();
                            }
                        }
                    }
                })
                .build(app)?;

            // 初始状态：隐藏窗口
            if let Some(window) = app.get_webview_window("main") {
                let _ = window.hide();
                println!("Window hidden, app running in system tray");
            }

            // 初始化全局快捷键插件
            #[cfg(desktop)]
            {
                use tauri_plugin_global_shortcut::{Code, Modifiers, Shortcut, ShortcutState};

                let app_handle = app.handle().clone();
                app.handle().plugin(
                    tauri_plugin_global_shortcut::Builder::new().with_handler(move |_app, shortcut, event| {
                        println!("Global shortcut triggered: {:?}", shortcut);
                        if event.state() == ShortcutState::Pressed {
                            println!("Global shortcut pressed!");

                            // 切换窗口显示/隐藏
                            if let Some(window) = app_handle.get_webview_window("main") {
                                if window.is_visible().unwrap_or(false) {
                                    let _ = window.hide();
                                } else {
                                    let _ = window.show();
                                    let _ = window.unminimize();
                                    let _ = window.set_focus();
                                    let _ = window.center();
                                }
                            }

                            let _ = app_handle.emit("global-shortcut-pressed", ());
                        }
                    })
                    .build(),
                )?;

                // 注册快捷键 - 在macOS上使用Cmd+Shift+P，其他平台使用Ctrl+Shift+P
                #[cfg(target_os = "macos")]
                let shortcut = Shortcut::new(Some(Modifiers::META | Modifiers::SHIFT), Code::KeyP);
                #[cfg(not(target_os = "macos"))]
                let shortcut = Shortcut::new(Some(Modifiers::CONTROL | Modifiers::SHIFT), Code::KeyP);
                match app.global_shortcut().register(shortcut) {
                    Ok(_) => println!("Global shortcut registered successfully"),
                    Err(e) => println!("Failed to register global shortcut: {}", e),
                }
            }

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}



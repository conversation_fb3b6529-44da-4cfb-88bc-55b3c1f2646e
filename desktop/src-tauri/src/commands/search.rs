use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchRequest {
    pub query: String,
    pub limit: Option<i32>,
    pub offset: Option<i32>,
    pub category: Option<String>,
    pub user_only: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchResponse {
    pub success: bool,
    pub data: Option<SearchData>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchData {
    pub prompts: Vec<Prompt>,
    pub total: i32,
    pub has_more: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Prompt {
    pub id: String,
    pub title: String,
    pub content: String,
    pub description: Option<String>,
    pub category: String,
    pub tags: Vec<String>,
    pub user_id: String,
    pub is_public: bool,
    pub likes: i32,
    pub downloads: i32,
    pub views: i32,
    pub created_at: String,
    pub updated_at: String,
}

#[tauri::command]
pub async fn search_prompts(request: SearchRequest) -> Result<SearchResponse, String> {
    // TODO: 实现搜索逻辑
    // 1. 调用后端API进行搜索
    // 2. 缓存搜索结果到本地数据库
    // 3. 返回搜索结果
    
    println!("Searching for: {}", request.query);
    
    // 丰富的模拟数据
    let mock_prompts = vec![
        Prompt {
            id: "1".to_string(),
            title: "GPT写作助手".to_string(),
            content: "你是一个专业的写作助手。请帮我写一篇关于{{主题}}的文章，要求：\n1. 结构清晰，逻辑严谨\n2. 语言流畅，表达准确\n3. 内容丰富，观点新颖\n4. 字数控制在{{字数}}字左右\n\n请开始写作：".to_string(),
            description: Some("专业的写作助手，帮助用户创作高质量文章".to_string()),
            category: "写作".to_string(),
            tags: vec!["写作".to_string(), "文章".to_string(), "创作".to_string(), "助手".to_string()],
            user_id: "user1".to_string(),
            is_public: true,
            likes: 156,
            downloads: 89,
            views: 234,
            created_at: "2024-01-15T10:30:00Z".to_string(),
            updated_at: "2024-01-15T10:30:00Z".to_string(),
        },
        Prompt {
            id: "2".to_string(),
            title: "代码审查专家".to_string(),
            content: "你是一个资深的代码审查专家。请仔细审查以下{{编程语言}}代码，并提供详细的反馈：\n\n```{{编程语言}}\n{{代码内容}}\n```\n\n请从以下几个方面进行审查：\n1. 代码质量和可读性\n2. 性能优化建议\n3. 安全性问题\n4. 最佳实践建议\n5. 潜在的bug或问题\n\n请提供具体的改进建议和修改后的代码示例。".to_string(),
            description: Some("专业代码审查，提供详细的代码质量分析和改进建议".to_string()),
            category: "编程".to_string(),
            tags: vec!["代码审查".to_string(), "编程".to_string(), "优化".to_string(), "最佳实践".to_string()],
            user_id: "user2".to_string(),
            is_public: true,
            likes: 203,
            downloads: 145,
            views: 387,
            created_at: "2024-01-14T14:20:00Z".to_string(),
            updated_at: "2024-01-14T14:20:00Z".to_string(),
        },
        Prompt {
            id: "3".to_string(),
            title: "营销文案生成器".to_string(),
            content: "你是一个创意营销专家。请为{{产品名称}}创作吸引人的营销文案：\n\n产品信息：\n- 产品名称：{{产品名称}}\n- 目标用户：{{目标用户}}\n- 核心卖点：{{核心卖点}}\n- 使用场景：{{使用场景}}\n\n请创作以下类型的文案：\n1. 朋友圈文案（简洁有趣）\n2. 小红书文案（种草风格）\n3. 微博文案（话题性强）\n4. 产品详情页文案（详细介绍）\n\n要求文案有创意、有感染力，能够激发用户的购买欲望。".to_string(),
            description: Some("专业营销文案生成，适用于各种社交媒体平台".to_string()),
            category: "营销".to_string(),
            tags: vec!["营销".to_string(), "文案".to_string(), "创意".to_string(), "社交媒体".to_string()],
            user_id: "user1".to_string(),
            is_public: true,
            likes: 178,
            downloads: 92,
            views: 298,
            created_at: "2024-01-13T09:15:00Z".to_string(),
            updated_at: "2024-01-13T09:15:00Z".to_string(),
        },
        Prompt {
            id: "4".to_string(),
            title: "英语学习导师".to_string(),
            content: "你是一位经验丰富的英语学习导师。学生想要学习关于{{学习主题}}的英语内容，当前英语水平是{{英语水平}}。\n\n请提供：\n1. 相关的核心词汇（10-15个）\n2. 常用句型和表达（5-8个）\n3. 实用对话示例（2-3个场景）\n4. 语法要点解释\n5. 练习建议和学习方法\n\n请用中英文对照的方式呈现，确保学生能够理解并实际运用。".to_string(),
            description: Some("个性化英语学习指导，提供词汇、语法、对话等全方位学习内容".to_string()),
            category: "教育".to_string(),
            tags: vec!["英语学习".to_string(), "教育".to_string(), "语言".to_string(), "导师".to_string()],
            user_id: "user3".to_string(),
            is_public: true,
            likes: 134,
            downloads: 76,
            views: 189,
            created_at: "2024-01-12T16:45:00Z".to_string(),
            updated_at: "2024-01-12T16:45:00Z".to_string(),
        },
        Prompt {
            id: "5".to_string(),
            title: "数据分析师".to_string(),
            content: "你是一个专业的数据分析师。请分析以下数据并提供洞察：\n\n数据背景：{{数据背景}}\n数据类型：{{数据类型}}\n分析目标：{{分析目标}}\n\n数据内容：\n{{数据内容}}\n\n请提供：\n1. 数据概览和基本统计\n2. 关键趋势和模式识别\n3. 异常值分析\n4. 相关性分析\n5. 业务洞察和建议\n6. 可视化建议\n\n请用专业但易懂的语言解释分析结果，并提供可执行的建议。".to_string(),
            description: Some("专业数据分析，提供深度洞察和业务建议".to_string()),
            category: "数据分析".to_string(),
            tags: vec!["数据分析".to_string(), "统计".to_string(), "洞察".to_string(), "可视化".to_string()],
            user_id: "user2".to_string(),
            is_public: true,
            likes: 167,
            downloads: 103,
            views: 256,
            created_at: "2024-01-11T11:30:00Z".to_string(),
            updated_at: "2024-01-11T11:30:00Z".to_string(),
        },
        Prompt {
            id: "6".to_string(),
            title: "心理咨询师".to_string(),
            content: "你是一位温和、专业的心理咨询师。来访者想要咨询关于{{咨询主题}}的问题。\n\n来访者的情况：\n{{具体情况描述}}\n\n请以专业、温暖、非评判的态度提供帮助：\n1. 首先表达理解和共情\n2. 分析可能的原因和影响因素\n3. 提供实用的应对策略\n4. 推荐适合的心理技巧或练习\n5. 给出积极的鼓励和支持\n\n请注意保持专业边界，如果问题严重建议寻求专业帮助。".to_string(),
            description: Some("专业心理咨询支持，提供温暖的倾听和实用的建议".to_string()),
            category: "心理健康".to_string(),
            tags: vec!["心理咨询".to_string(), "情感支持".to_string(), "心理健康".to_string(), "倾听".to_string()],
            user_id: "user4".to_string(),
            is_public: true,
            likes: 145,
            downloads: 67,
            views: 201,
            created_at: "2024-01-10T13:20:00Z".to_string(),
            updated_at: "2024-01-10T13:20:00Z".to_string(),
        },
        Prompt {
            id: "7".to_string(),
            title: "产品经理助手".to_string(),
            content: "你是一个经验丰富的产品经理。请帮助分析和规划以下产品需求：\n\n产品信息：\n- 产品类型：{{产品类型}}\n- 目标用户：{{目标用户}}\n- 核心问题：{{要解决的问题}}\n- 竞争对手：{{主要竞争对手}}\n\n请提供：\n1. 用户需求分析\n2. 功能优先级排序\n3. 产品路线图建议\n4. 关键指标定义\n5. 风险评估\n6. 上线策略建议\n\n请用产品思维分析，提供可执行的产品方案。".to_string(),
            description: Some("专业产品管理支持，从需求分析到产品规划的全流程指导".to_string()),
            category: "产品管理".to_string(),
            tags: vec!["产品管理".to_string(), "需求分析".to_string(), "产品规划".to_string(), "策略".to_string()],
            user_id: "user1".to_string(),
            is_public: true,
            likes: 189,
            downloads: 112,
            views: 334,
            created_at: "2024-01-09T15:10:00Z".to_string(),
            updated_at: "2024-01-09T15:10:00Z".to_string(),
        },
        Prompt {
            id: "8".to_string(),
            title: "创意设计师".to_string(),
            content: "你是一个富有创意的设计师。请为{{设计项目}}提供创意设计方案：\n\n项目需求：\n- 项目类型：{{项目类型}}\n- 设计风格：{{期望风格}}\n- 目标受众：{{目标受众}}\n- 使用场景：{{使用场景}}\n- 品牌调性：{{品牌调性}}\n\n请提供：\n1. 设计理念和创意思路\n2. 色彩搭配建议\n3. 字体和排版建议\n4. 视觉元素建议\n5. 设计规范和标准\n6. 多个设计方案对比\n\n请用专业的设计语言描述，并解释设计决策的理由。".to_string(),
            description: Some("专业创意设计指导，提供全面的设计方案和创意思路".to_string()),
            category: "设计".to_string(),
            tags: vec!["创意设计".to_string(), "视觉设计".to_string(), "品牌设计".to_string(), "用户体验".to_string()],
            user_id: "user3".to_string(),
            is_public: true,
            likes: 156,
            downloads: 84,
            views: 267,
            created_at: "2024-01-08T10:25:00Z".to_string(),
            updated_at: "2024-01-08T10:25:00Z".to_string(),
        },
        Prompt {
            id: "9".to_string(),
            title: "法律顾问助手".to_string(),
            content: "你是一位专业的法律顾问。请就{{法律问题类型}}提供专业的法律建议：\n\n案例情况：\n{{具体情况描述}}\n\n相关法律领域：{{相关法律领域}}\n争议焦点：{{争议焦点}}\n\n请提供：\n1. 相关法律条文和规定\n2. 案例分析和法律适用\n3. 可能的法律风险\n4. 建议的解决方案\n5. 预防措施建议\n6. 后续行动建议\n\n请注意：此建议仅供参考，具体案件请咨询专业律师。".to_string(),
            description: Some("专业法律咨询支持，提供法律分析和建议（仅供参考）".to_string()),
            category: "法律".to_string(),
            tags: vec!["法律咨询".to_string(), "法律分析".to_string(), "风险评估".to_string(), "合规".to_string()],
            user_id: "user4".to_string(),
            is_public: true,
            likes: 123,
            downloads: 58,
            views: 178,
            created_at: "2024-01-07T14:40:00Z".to_string(),
            updated_at: "2024-01-07T14:40:00Z".to_string(),
        },
        Prompt {
            id: "10".to_string(),
            title: "健身教练".to_string(),
            content: "你是一位专业的健身教练。请为{{健身目标}}制定个性化的健身计划：\n\n个人信息：\n- 年龄：{{年龄}}\n- 性别：{{性别}}\n- 身高体重：{{身高体重}}\n- 运动基础：{{运动基础}}\n- 可用时间：{{每周可用时间}}\n- 健身环境：{{健身环境}}\n\n请提供：\n1. 训练计划（周计划）\n2. 具体动作指导\n3. 强度和组数建议\n4. 饮食建议\n5. 注意事项和安全提醒\n6. 进度跟踪方法\n\n请确保计划科学合理，适合个人情况。".to_string(),
            description: Some("专业健身指导，提供个性化训练计划和健康建议".to_string()),
            category: "健康".to_string(),
            tags: vec!["健身".to_string(), "运动".to_string(), "健康".to_string(), "训练计划".to_string()],
            user_id: "user2".to_string(),
            is_public: true,
            likes: 198,
            downloads: 127,
            views: 312,
            created_at: "2024-01-06T08:15:00Z".to_string(),
            updated_at: "2024-01-06T08:15:00Z".to_string(),
        },
    ];
    
    // 智能搜索匹配
    let query_lower = request.query.to_lowercase();
    let mut filtered_prompts: Vec<Prompt> = if query_lower.trim().is_empty() {
        // 如果搜索为空，返回所有提示词
        mock_prompts
    } else {
        // 关键词匹配
        mock_prompts
            .into_iter()
            .filter(|prompt| {
                let title_match = prompt.title.to_lowercase().contains(&query_lower);
                let content_match = prompt.content.to_lowercase().contains(&query_lower);
                let description_match = prompt.description.as_ref()
                    .map(|desc| desc.to_lowercase().contains(&query_lower))
                    .unwrap_or(false);
                let category_match = prompt.category.to_lowercase().contains(&query_lower);
                let tags_match = prompt.tags.iter()
                    .any(|tag| tag.to_lowercase().contains(&query_lower));

                title_match || content_match || description_match || category_match || tags_match
            })
            .collect()
    };

    // 按相关性排序（标题匹配优先）
    filtered_prompts.sort_by(|a, b| {
        let a_title_match = a.title.to_lowercase().contains(&query_lower);
        let b_title_match = b.title.to_lowercase().contains(&query_lower);

        match (a_title_match, b_title_match) {
            (true, false) => std::cmp::Ordering::Less,
            (false, true) => std::cmp::Ordering::Greater,
            _ => b.likes.cmp(&a.likes), // 按点赞数排序
        }
    });

    // 应用分页
    let limit = request.limit.unwrap_or(10) as usize;
    let offset = request.offset.unwrap_or(0) as usize;
    let total = filtered_prompts.len();
    let has_more = offset + limit < total;

    let paginated_prompts = filtered_prompts
        .into_iter()
        .skip(offset)
        .take(limit)
        .collect();
    
    Ok(SearchResponse {
        success: true,
        data: Some(SearchData {
            total: total as i32,
            has_more,
            prompts: paginated_prompts,
        }),
        error: None,
    })
}

use serde_json;
use std::collections::HashMap;
use std::sync::Mutex;
use tauri::State;

// 简单的内存存储，实际应用中可以使用文件或数据库
pub type Storage = Mutex<HashMap<String, String>>;

#[tauri::command]
pub async fn get_storage_item(
    key: String,
    storage: State<'_, Storage>,
) -> Result<Option<String>, String> {
    let storage = storage.lock().map_err(|e| format!("Storage lock error: {}", e))?;
    Ok(storage.get(&key).cloned())
}

#[tauri::command]
pub async fn set_storage_item(
    key: String,
    value: String,
    storage: State<'_, Storage>,
) -> Result<(), String> {
    let mut storage = storage.lock().map_err(|e| format!("Storage lock error: {}", e))?;
    storage.insert(key, value);
    Ok(())
}

#[tauri::command]
pub async fn remove_storage_item(
    key: String,
    storage: State<'_, Storage>,
) -> Result<(), String> {
    let mut storage = storage.lock().map_err(|e| format!("Storage lock error: {}", e))?;
    storage.remove(&key);
    Ok(())
}

#[tauri::command]
pub async fn clear_storage(storage: State<'_, Storage>) -> Result<(), String> {
    let mut storage = storage.lock().map_err(|e| format!("Storage lock error: {}", e))?;
    storage.clear();
    Ok(())
}

#[tauri::command]
pub async fn get_all_storage_keys(storage: State<'_, Storage>) -> Result<Vec<String>, String> {
    let storage = storage.lock().map_err(|e| format!("Storage lock error: {}", e))?;
    Ok(storage.keys().cloned().collect())
}

use serde::{Deserialize, Serialize};
use crate::commands::search::Prompt;

#[derive(Debug, Serialize, Deserialize)]
pub struct PromptDetailResponse {
    pub success: bool,
    pub data: Option<Prompt>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UsageRequest {
    pub prompt_id: String,
    pub user_input: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UsageResponse {
    pub success: bool,
    pub message: String,
}

#[tauri::command]
pub async fn get_prompt_detail(prompt_id: String) -> Result<PromptDetailResponse, String> {
    // TODO: 实现获取提示词详情逻辑
    // 1. 先从本地缓存查找
    // 2. 如果没有，调用后端API获取
    // 3. 缓存到本地数据库
    
    println!("Getting prompt detail for: {}", prompt_id);
    
    // 临时返回模拟数据
    let mock_prompt = Prompt {
        id: prompt_id.clone(),
        title: "函数文档生成".to_string(),
        content: "你是一个专业的代码文档生成助手，请为以下函数生成详细的文档注释。".to_string(),
        description: Some("自动生成标准化的函数文档注释".to_string()),
        category: "编程开发".to_string(),
        tags: vec!["文档".to_string(), "代码".to_string(), "注释".to_string()],
        user_id: "user_123".to_string(),
        is_public: true,
        likes: 15,
        downloads: 89,
        views: 234,
        created_at: "2025-07-01T10:00:00Z".to_string(),
        updated_at: "2025-07-10T15:30:00Z".to_string(),
    };
    
    Ok(PromptDetailResponse {
        success: true,
        data: Some(mock_prompt),
        error: None,
    })
}

#[tauri::command]
pub async fn record_usage(request: UsageRequest) -> Result<UsageResponse, String> {
    // TODO: 实现使用记录逻辑
    // 1. 记录到本地数据库
    // 2. 异步同步到后端API
    
    println!("Recording usage for prompt: {} with input: {}", request.prompt_id, request.user_input);
    
    Ok(UsageResponse {
        success: true,
        message: "使用记录已保存".to_string(),
    })
}

use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use arboard::Clipboard;

#[tauri::command]
pub async fn inject_text(app_handle: AppHandle, text: String) -> Result<bool, String> {
    // TODO: 实现文本注入逻辑
    // 1. 保存当前剪贴板内容
    // 2. 将文本复制到剪贴板
    // 3. 模拟粘贴操作
    // 4. 恢复原剪贴板内容
    
    println!("Injecting text: {}", text);
    
    // TODO: 实现剪贴板操作和粘贴模拟
    println!("Text to inject: {}", text);
    Ok(true)
}

#[tauri::command]
pub async fn copy_to_clipboard(_app_handle: AppHandle, text: String) -> Result<bool, String> {
    println!("Copying to clipboard: {}", text);

    match Clipboard::new() {
        Ok(mut clipboard) => {
            match clipboard.set_text(text) {
                Ok(_) => {
                    println!("Successfully copied text to clipboard");
                    Ok(true)
                }
                Err(e) => {
                    eprintln!("Failed to copy text to clipboard: {}", e);
                    Err(format!("Failed to copy to clipboard: {}", e))
                }
            }
        }
        Err(e) => {
            eprintln!("Failed to create clipboard instance: {}", e);
            Err(format!("Failed to access clipboard: {}", e))
        }
    }
}

#[tauri::command]
pub async fn show_window(app_handle: AppHandle) -> Result<bool, String> {
    if let Some(window) = app_handle.get_webview_window("main") {
        match window.show() {
            Ok(_) => {
                // 临时置顶以确保窗口可见
                let _ = window.set_always_on_top(true);
                let _ = window.unminimize();
                let _ = window.set_focus();
                let _ = window.center();

                // 延迟取消置顶
                let window_clone = window.clone();
                tokio::spawn(async move {
                    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
                    let _ = window_clone.set_always_on_top(false);
                });

                Ok(true)
            }
            Err(e) => Err(format!("Failed to show window: {}", e)),
        }
    } else {
        Err("Main window not found".to_string())
    }
}

#[tauri::command]
pub async fn hide_window(app_handle: AppHandle) -> Result<bool, String> {
    if let Some(window) = app_handle.get_webview_window("main") {
        match window.hide() {
            Ok(_) => Ok(true),
            Err(e) => Err(format!("Failed to hide window: {}", e)),
        }
    } else {
        Err("Main window not found".to_string())
    }
}

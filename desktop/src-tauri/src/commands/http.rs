use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::Duration;

#[derive(Debug, Serialize, Deserialize)]
pub struct HttpRequest {
    pub method: String,
    pub url: String,
    pub headers: HashMap<String, String>,
    pub body: Option<String>,
    pub timeout: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HttpResponse {
    pub status: u16,
    pub headers: HashMap<String, String>,
    pub data: Option<String>,
}

#[tauri::command]
pub async fn http_request(request: HttpRequest) -> Result<HttpResponse, String> {
    println!("🌐 HTTP Request: {} {}", request.method, request.url);
    println!("📋 Headers: {:?}", request.headers);
    if let Some(ref body) = request.body {
        println!("📦 Body: {}", body);
    }

    let client = reqwest::Client::new();
    
    // 构建请求
    let mut req_builder = match request.method.to_uppercase().as_str() {
        "GET" => client.get(&request.url),
        "POST" => client.post(&request.url),
        "PUT" => client.put(&request.url),
        "DELETE" => client.delete(&request.url),
        "PATCH" => client.patch(&request.url),
        _ => return Err("Unsupported HTTP method".to_string()),
    };

    // 添加headers
    for (key, value) in request.headers {
        req_builder = req_builder.header(&key, &value);
    }

    // 添加body
    if let Some(body) = request.body {
        req_builder = req_builder.body(body);
    }

    // 设置超时
    if let Some(timeout_ms) = request.timeout {
        req_builder = req_builder.timeout(Duration::from_millis(timeout_ms));
    }

    // 发送请求
    match req_builder.send().await {
        Ok(response) => {
            let status = response.status().as_u16();
            println!("📡 Response Status: {}", status);

            // 获取响应头
            let mut headers = HashMap::new();
            for (key, value) in response.headers() {
                if let Ok(value_str) = value.to_str() {
                    headers.insert(key.to_string(), value_str.to_string());
                }
            }

            // 获取响应体
            let data = match response.text().await {
                Ok(text) => {
                    println!("📄 Response Body: {}", text);
                    Some(text)
                },
                Err(e) => {
                    println!("❌ Failed to read response body: {}", e);
                    None
                }
            };

            Ok(HttpResponse {
                status,
                headers,
                data,
            })
        }
        Err(e) => {
            println!("❌ HTTP request failed: {}", e);
            Err(format!("HTTP request failed: {}", e))
        }
    }
}

# PromptHub 桌面客户端产品需求文档 (PRD)

**版本**: 1.1  
**日期**: 2025年7月12日  
**状态**: 已确认

## 1. 项目概述

### 1.1 项目使命

PromptHub 桌面客户端是 PromptHub 生态系统中的"效率终端"，旨在让用户能够在任何应用程序中快速搜索、填充并使用他们的提示词，大幅提升工作效率。

### 1.2 核心理念

遵循"主应用（Web）+ 伴侣应用（Desktop）"的设计哲学，将复杂的"管理"功能与高频的"使用"功能分离，为用户在不同场景下提供最优化的交互体验：

- **Web端**：管理与探索中心
- **桌面端**：高效工作终端

## 2. 产品定位与目标

### 2.1 Web 端 (管理与探索中心)

**定位**: 功能最全面的管理后台和社区门户。

**核心职责**:
- 用户账户的全生命周期管理
- 提示词的创建、编辑、删除、分类、打标等（CRUD）
- 发现、分享和收藏来自社区的公共提示词

### 2.2 桌面客户端 (高效工作终端)

**定位**: 一个轻量、快速、专注的生产力工具。

**核心职责**:
- 提供全局、即时的提示词访问入口
- 支持动态变量填充，实现提示词的个性化
- 将最终生成的文本无缝注入到任何第三方应用中

**核心原则**: 用完即走，不打扰，不中断。

## 3. 用户画像与核心场景

### 3.1 目标用户

**AI Power User**: 
- 每日频繁与大语言模型交互的开发者、营销人员、内容创作者、研究员等
- 痛点: 在不同应用和AI工具间切换、复制、粘贴提示词的过程繁琐、低效，打断心流
- 诉求: 追求极致的速度和效率，偏爱键盘驱动的操作，希望工具能深度整合进自己的工作流

### 3.2 核心使用场景

**场景示例**: 开发者在编辑器中生成代码文档

**流程**:
1. 在代码编辑器中，按下全局快捷键 Cmd/Ctrl+Shift+P
2. PromptHub 的命令面板浮现在屏幕中央
3. 输入 "doc" 进行搜索，选中 "函数文档生成" 模板
4. 面板内动态出现 "函数名"、"参数"、"返回值" 等输入框
5. 开发者快速填写表单，点击"使用"按钮
6. 命令面板消失，一段完整、格式化的文档注释被瞬间粘贴到编辑器的光标所在处

## 4. 功能性需求 (FRD)

### 4.1 后端 API 需求

- **认证接口**: 提供基于 JWT 的用户登录认证
- **搜索接口**: 提供实时模糊搜索接口，例如 GET /api/search?q={keyword}
- **列表接口**: 提供获取用户全量提示词列表的接口 GET /api/prompts，用于无搜索词时的默认展示及离线模式的数据基础

### 4.2 桌面客户端功能需求

#### 4.2.1 激活与界面

- **全局激活**: 支持用户自定义全局快捷键（默认 Cmd/Ctrl+Shift+P），可在任何应用中唤醒程序
- **命令面板UI**: 激活后，以无边框、悬浮的命令面板形态出现，UI风格与操作系统深度融合

#### 4.2.2 搜索功能

- **实时API搜索**: 用户在搜索框中输入文本时，客户端通过防抖（debounce）机制调用后端API进行实时搜索
- **本地搜索回退**: 在网络不可用时，使用本地缓存数据进行搜索

#### 4.2.3 核心工作流：提示词使用

1. 用户通过键盘或鼠标从搜索结果列表中选中一个提示词
2. 应用显示一个包含两部分的界面：
   - 上半部分：系统提示词（来自PromptHub，可编辑）
   - 下半部分：用户输入框（供用户输入自己的需求）
3. **注入/粘贴**: 用户填写完内容并点击"使用"按钮，应用将组合好的最终文本内容（系统提示词 + 用户输入），自动粘贴到之前拥有焦点的应用程序的输入框中。随后，命令面板窗口自动关闭

#### 4.2.4 辅助功能

- **复制到剪贴板**: 提供次级快捷键（如 Ctrl/Cmd+Enter），用户可将组合好的内容仅复制到剪贴板而不执行粘贴
- **收藏/历史记录**: 显示最近使用和收藏的提示词

#### 4.2.5 数据同步

- **在线模式**: 应用通过网络实时获取数据
- **本地缓存**: 缓存最近使用的提示词以提升性能

#### 4.2.6 系统集成

- **状态栏/托盘图标**: 在macOS菜单栏和Windows系统托盘中提供常驻图标
- **托盘菜单**: 提供"手动同步"、"设置"、"检查更新"、"退出"等快捷操作
- **开机自启**: 在设置中提供开机自启动选项

#### 4.2.7 用户认证

- 提供一次性登录界面，登录成功后安全地在本地存储认证凭据（JWT）
- 支持自动刷新凭据以维持登录状态

## 5. 非功能性需求 (NFR)

- **性能**: 应用启动快速（< 2秒），空闲状态下内存占用低（< 100MB）
- **用户体验**: 遵循各平台设计规范，动效自然，交互符合直觉，完全支持键盘操作
- **可靠性**: 内置自动更新机制。能优雅地处理网络错误和API异常，并提供清晰的用户提示
- **安全性**: 客户端与服务器之间所有通信必须使用HTTPS。本地存储的用户凭据必须加密

## 6. 技术架构与选型

### 6.1 整体架构
C/S架构，所有客户端通过统一的RESTful API与后端通信。

### 6.2 技术栈选择

- **后端服务**: Node.js + Express.js, 腾讯云MySQL, JWT
- **Web客户端**: Next.js + React + TypeScript
- **桌面客户端**:
  - **框架**: Tauri v2
  - **UI层**: React + TypeScript + Tailwind CSS
  - **状态管理**: Zustand
  - **本地存储**: SQLite (via tauri-plugin-sql)
  - **系统交互层**: Rust

## 7. 开发计划与优先级

### 7.1 MVP功能范围
- 全局激活
- 实时API搜索
- 变量填充与表单
- 核心粘贴功能
- 用户登录和数据同步

### 7.2 平台开发策略
同时进行macOS和Windows开发，保持核心功能的跨平台一致性。

## 8. 附录

### 8.1 界面模式
采用系统提示词 + 用户输入的双输入框模式，参考浏览器插件实现

### 8.2 默认快捷键
`Cmd/Ctrl + Shift + P`

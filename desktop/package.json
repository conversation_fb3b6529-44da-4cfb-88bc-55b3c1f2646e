{"name": "prompthub-desktop", "version": "1.0.0", "description": "PromptHub 桌面客户端 - 高效的AI提示词管理与使用工具", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"@tauri-apps/api": "^2.0.0", "@tauri-apps/plugin-clipboard-manager": "^2.0.0", "@tauri-apps/plugin-global-shortcut": "^2.0.0", "@tauri-apps/plugin-shell": "^2.3.0", "@tauri-apps/plugin-sql": "^2.0.0", "@tauri-apps/plugin-store": "^2.0.0", "@tauri-apps/plugin-window-state": "^2.0.0", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.2.0", "zustand": "^4.4.7"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.0.8"}, "keywords": ["prompthub", "ai", "prompt", "desktop", "tauri", "react", "typescript"], "author": "PromptHub Team", "license": "MIT"}
# PromptHub 桌面客户端 API 需求文档

**版本**: 1.0  
**日期**: 2025年7月12日

## 📋 概述

本文档定义了PromptHub桌面客户端所需的后端API接口。这些接口将扩展现有的Web端API，以支持桌面客户端的特殊需求。

## 🔐 认证机制

所有API请求都需要在Header中携带JWT令牌：
```
Authorization: Bearer <jwt_token>
```

## 📡 新增API接口

### 1. 搜索接口

#### 1.1 实时搜索提示词
```http
GET /api/search
```

**查询参数**:
- `q` (string, required): 搜索关键词
- `limit` (number, optional): 返回结果数量限制，默认20，最大100
- `offset` (number, optional): 分页偏移量，默认0
- `category` (string, optional): 分类筛选
- `tags` (string, optional): 标签筛选，多个标签用逗号分隔
- `user_only` (boolean, optional): 是否只搜索用户自己的提示词，默认false

**响应示例**:
```json
{
  "success": true,
  "data": {
    "prompts": [
      {
        "id": "prompt_123",
        "title": "函数文档生成",
        "content": "为以下函数生成详细的文档注释：\n\n函数名：{{function_name}}\n参数：{{parameters}}\n返回值：{{return_value}}\n功能描述：{{description}}",
        "description": "自动生成标准化的函数文档注释",
        "category": "编程开发",
        "tags": ["文档", "代码", "注释"],

        "userId": "user_456",
        "isPublic": true,
        "likes": 15,
        "downloads": 89,
        "views": 234,
        "createdAt": "2025-07-01T10:00:00Z",
        "updatedAt": "2025-07-10T15:30:00Z"
      }
    ],
    "total": 1,
    "hasMore": false
  }
}
```

#### 1.2 搜索建议
```http
GET /api/search/suggestions
```

**查询参数**:
- `q` (string, required): 搜索关键词前缀
- `limit` (number, optional): 建议数量，默认5

**响应示例**:
```json
{
  "success": true,
  "data": {
    "suggestions": [
      "函数文档",
      "函数注释",
      "代码文档生成"
    ]
  }
}
```

### 2. 提示词管理接口

#### 2.1 获取用户提示词列表（支持离线缓存）
```http
GET /api/prompts/sync
```

**查询参数**:
- `last_sync` (string, optional): 上次同步时间戳，ISO 8601格式
- `limit` (number, optional): 返回结果数量限制，默认100
- `offset` (number, optional): 分页偏移量，默认0

**响应示例**:
```json
{
  "success": true,
  "data": {
    "prompts": [
      // 提示词对象数组，格式同搜索接口
    ],
    "deleted_ids": ["prompt_789"], // 已删除的提示词ID列表
    "total": 50,
    "sync_timestamp": "2025-07-12T10:00:00Z"
  }
}
```

#### 2.2 获取单个提示词详情
```http
GET /api/prompts/{id}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "prompt": {
      // 完整的提示词对象
    }
  }
}
```

#### 2.3 记录提示词使用
```http
POST /api/prompts/{id}/usage
```

**请求体**:
```json
{
  "user_input": "请为这个函数生成文档：function calculateSum(a, b) { return a + b; }",
  "client_type": "desktop"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "使用记录已保存"
}
```

### 3. 用户认证接口扩展

#### 3.1 桌面客户端登录
```http
POST /api/auth/desktop/login
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "device_name": "MacBook Pro",
  "device_id": "unique_device_identifier"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600,
    "user": {
      "id": "user_456",
      "username": "john_doe",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatar.jpg",
      "subscription_type": "premium"
    }
  }
}
```

#### 3.2 刷新令牌
```http
POST /api/auth/refresh
```

**请求体**:
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600
  }
}
```

#### 3.3 验证令牌
```http
GET /api/auth/verify
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_456",
      "username": "john_doe",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatar.jpg",
      "subscription_type": "premium"
    }
  }
}
```

### 4. 用户偏好设置接口

#### 4.1 获取用户设置
```http
GET /api/user/settings
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "settings": {
      "hotkey": "Cmd+Shift+P",
      "auto_paste": true,
      "show_notifications": true,
      "theme": "system",
      "search_history_enabled": true,
      "max_search_history": 50
    }
  }
}
```

#### 4.2 更新用户设置
```http
PUT /api/user/settings
```

**请求体**:
```json
{
  "hotkey": "Cmd+Shift+P",
  "auto_paste": true,
  "show_notifications": false
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "设置已更新"
}
```

### 5. 使用统计接口

#### 5.1 获取使用历史
```http
GET /api/user/usage-history
```

**查询参数**:
- `limit` (number, optional): 返回结果数量，默认20
- `offset` (number, optional): 分页偏移量，默认0

**响应示例**:
```json
{
  "success": true,
  "data": {
    "history": [
      {
        "prompt_id": "prompt_123",
        "prompt_title": "函数文档生成",
        "used_at": "2025-07-12T09:30:00Z",
        "user_input": "请为这个函数生成文档：function calculateSum(a, b) { return a + b; }"
      }
    ],
    "total": 100
  }
}
```

#### 5.2 获取收藏的提示词
```http
GET /api/user/favorites
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "favorites": [
      // 提示词对象数组
    ]
  }
}
```

## 🔄 现有API需要的修改

### 1. 提示词对象扩展

现有的提示词对象需要添加以下字段：

```json
{
  "usage_count": 0, // 使用次数
  "last_used": "2025-07-12T09:30:00Z" // 最后使用时间
}
```

### 2. 错误处理标准化

所有API都应该遵循统一的错误响应格式：

```json
{
  "success": false,
  "error": {
    "code": "INVALID_TOKEN",
    "message": "令牌无效或已过期",
    "details": {}
  }
}
```

常见错误码：
- `INVALID_TOKEN`: 令牌无效
- `TOKEN_EXPIRED`: 令牌过期
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `PROMPT_NOT_FOUND`: 提示词不存在
- `INSUFFICIENT_PERMISSIONS`: 权限不足
- `VALIDATION_ERROR`: 请求参数验证失败

### 3. 分页响应标准化

所有分页接口都应该包含以下元数据：

```json
{
  "success": true,
  "data": {
    // 实际数据
  },
  "pagination": {
    "current_page": 1,
    "per_page": 20,
    "total": 100,
    "total_pages": 5,
    "has_next": true,
    "has_prev": false
  }
}
```

## 🚀 性能要求

- **搜索接口响应时间**: < 500ms
- **同步接口响应时间**: < 1s
- **认证接口响应时间**: < 300ms
- **并发支持**: 至少支持1000个并发连接
- **缓存策略**: 搜索结果缓存5分钟，用户数据缓存1小时

## 🔒 安全要求

- 所有接口必须使用HTTPS
- JWT令牌有效期不超过1小时
- 刷新令牌有效期不超过30天
- 实现请求频率限制（每分钟最多100次请求）
- 敏感操作需要额外验证

## 📊 监控和日志

- 记录所有API调用的响应时间
- 记录错误率和错误类型
- 监控搜索关键词和使用模式
- 记录用户活跃度和功能使用情况

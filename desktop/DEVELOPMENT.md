# PromptHub 桌面客户端开发指南

## 🚀 快速开始

### 环境要求

- **Node.js**: >= 18.0.0
- **Rust**: >= 1.70.0
- **Tauri CLI**: >= 2.0.0

### 安装依赖

```bash
# 安装前端依赖
npm install

# 安装 Tauri CLI (如果还没有安装)
npm install -g @tauri-apps/cli@next
```

### 开发模式

```bash
# 启动开发服务器
npm run tauri:dev
```

这将同时启动：
- Vite 开发服务器 (端口 1420)
- Tauri 应用程序

### 构建应用

```bash
# 构建生产版本
npm run tauri:build
```

## 📁 项目结构

```
desktop/
├── src/                  # React 前端代码
│   ├── components/       # UI组件
│   │   ├── CommandPalette.tsx    # 命令面板主组件
│   │   ├── SearchView.tsx        # 搜索视图
│   │   ├── PromptDetailView.tsx  # 提示词详情视图
│   │   ├── PromptCard.tsx        # 提示词卡片
│   │   └── LoginModal.tsx        # 登录模态框
│   ├── store/            # Zustand状态管理
│   │   ├── authStore.ts          # 认证状态
│   │   ├── appStore.ts           # 应用状态
│   │   └── searchStore.ts        # 搜索状态
│   ├── types/            # TypeScript类型定义
│   ├── utils/            # 工具函数
│   ├── styles/           # 样式文件
│   ├── App.tsx           # 主应用组件
│   └── main.tsx          # 应用入口
├── src-tauri/            # Rust 后端代码
│   ├── src/
│   │   ├── commands/     # Tauri命令
│   │   │   ├── auth.rs           # 认证相关命令
│   │   │   ├── search.rs         # 搜索相关命令
│   │   │   ├── prompts.rs        # 提示词相关命令
│   │   │   └── system.rs         # 系统集成命令
│   │   ├── db/           # 数据库操作
│   │   ├── services/     # 业务逻辑服务
│   │   ├── utils/        # 工具函数
│   │   └── main.rs       # Rust主入口
│   ├── Cargo.toml        # Rust依赖配置
│   └── tauri.conf.json   # Tauri配置
└── package.json          # 前端依赖配置
```

## 🔧 开发说明

### 状态管理

使用 Zustand 进行状态管理，主要包含：

- **authStore**: 用户认证状态
- **appStore**: 应用显示状态和视图切换
- **searchStore**: 搜索结果和状态

### Tauri 命令

前端通过 `invoke` 调用 Rust 后端命令：

```typescript
import { invoke } from "@tauri-apps/api/core";

// 搜索提示词
const response = await invoke("search_prompts", {
  request: { query: "搜索关键词" }
});
```

### 全局快捷键

默认快捷键：`Ctrl/Cmd + Shift + P`

在 `src-tauri/src/main.rs` 中配置。

### 数据库

使用 SQLite 存储本地数据：
- 认证令牌
- 提示词缓存
- 应用配置
- 使用历史

## 🎨 UI 设计

### 设计系统

- 基于 Tailwind CSS
- 使用 CSS 变量实现主题切换
- 响应式设计
- 支持深色/浅色模式

### 组件规范

- 使用 TypeScript 严格类型检查
- 组件 Props 接口定义
- 统一的错误处理
- 加载状态指示器

## 🔍 调试

### 前端调试

- 使用浏览器开发者工具
- React DevTools
- Zustand DevTools

### 后端调试

```bash
# 查看 Rust 日志
RUST_LOG=debug npm run tauri:dev
```

### 数据库调试

SQLite 数据库文件位置：
- 开发环境：项目根目录 `prompthub.db`
- 生产环境：用户数据目录

## 📝 代码规范

### TypeScript

- 严格模式
- 明确的类型定义
- 避免使用 `any`

### Rust

- 使用 `clippy` 进行代码检查
- 错误处理使用 `Result<T, E>`
- 异步操作使用 `async/await`

### 提交规范

使用 Conventional Commits：

```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建过程或辅助工具的变动
```

## 🚨 常见问题

### 1. Tauri 命令调用失败

检查：
- 命令是否在 `main.rs` 中注册
- 参数类型是否匹配
- 错误处理是否正确

### 2. 全局快捷键不工作

检查：
- 快捷键是否被其他应用占用
- 权限设置是否正确
- 平台兼容性

### 3. 数据库连接失败

检查：
- SQLite 文件权限
- 数据库迁移是否执行
- 连接字符串是否正确

## 📚 参考资料

- [Tauri 官方文档](https://tauri.app/)
- [React 官方文档](https://react.dev/)
- [Zustand 文档](https://github.com/pmndrs/zustand)
- [Tailwind CSS 文档](https://tailwindcss.com/)
- [SQLx 文档](https://github.com/launchbadge/sqlx)

/**
 * PromptHub 桌面客户端 API 配置
 * 统一管理API地址、认证和请求配置
 */

// 环境检测
const isDevelopment = process.env.NODE_ENV === 'development'
const isProduction = process.env.NODE_ENV === 'production'

// API 配置
export const API_CONFIG = {
  // 基础配置
  BASE_URL: isDevelopment
    ? 'http://localhost:4000/api'  // 开发环境使用本地后端
    : 'https://prompthub.xin/api', // 生产环境
  
  TIMEOUT: 10000, // 10秒超时
  RETRY_COUNT: 3, // 重试次数
  
  // 认证配置
  AUTH: {
    TOKEN_KEY: 'prompthub_token',
    REFRESH_TOKEN_KEY: 'prompthub_refresh_token',
    USER_KEY: 'prompthub_user',
    TOKEN_HEADER: 'Authorization',
    TOKEN_PREFIX: 'Bearer ',
  },
  
  // 端点配置
  ENDPOINTS: {
    // 认证相关
    AUTH: {
      LOGIN: '/auth/login',
      REGISTER: '/auth/register',
      ME: '/auth/me',
      LOGOUT: '/auth/logout',
      REFRESH: '/auth/refresh',
    },
    
    // 提示词相关
    PROMPTS: {
      PUBLIC: '/prompts/public',
      LIST: '/prompts',
      MY_LIST: '/prompts/my',
      DETAIL: (id: string) => `/prompts/${id}`,
      CREATE: '/prompts',
      UPDATE: (id: string) => `/prompts/${id}`,
      DELETE: (id: string) => `/prompts/${id}`,
      LIKE: (id: string) => `/prompts/${id}/like`,
      COPY: (id: string) => `/prompts/${id}/copy`,
      SEARCH: '/prompts/public', // 使用 public 端点进行搜索
    },
    
    // 分类相关
    CATEGORIES: {
      LIST: '/categories',
      STATS: '/categories/stats',
    },
    
    // 用户相关
    USER: {
      PROFILE: '/user/profile',
      UPDATE_PROFILE: '/user/profile',
      CHANGE_PASSWORD: '/user/change-password',
    },
  },
  
  // 请求头配置
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Client': 'desktop',
    'X-Client-Version': '1.0.0',
    'x-requested-with': 'extension', // 让后端返回 token
  },
  
  // 分页配置
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 6,
    MAX_PAGE_SIZE: 50,
  },
  
  // 缓存配置
  CACHE: {
    PROMPTS_TTL: 5 * 60 * 1000, // 5分钟
    CATEGORIES_TTL: 30 * 60 * 1000, // 30分钟
    USER_TTL: 10 * 60 * 1000, // 10分钟
  },
}

// 构建完整的API URL
export const buildApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint}`
}

// 获取认证头
export const getAuthHeaders = (token?: string): Record<string, string> => {
  const headers = { ...API_CONFIG.HEADERS }
  
  if (token) {
    headers[API_CONFIG.AUTH.TOKEN_HEADER] = `${API_CONFIG.AUTH.TOKEN_PREFIX}${token}`
  }
  
  return headers
}

// API 响应类型定义
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// 错误类型定义
export interface ApiError {
  code: string
  message: string
  details?: any
}

// 请求配置类型
export interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  body?: any
  timeout?: number
  retries?: number
  cache?: boolean
  requireAuth?: boolean
}

// 分页参数类型
export interface PaginationParams {
  page?: number
  limit?: number
  sortBy?: string
  search?: string
  category?: string
  tags?: string
}

// 导出配置常量
export const {
  BASE_URL,
  TIMEOUT,
  RETRY_COUNT,
  ENDPOINTS,
  HEADERS,
  PAGINATION,
  CACHE,
  AUTH,
} = API_CONFIG

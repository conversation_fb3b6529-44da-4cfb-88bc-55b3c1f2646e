/**
 * PromptHub 桌面客户端 API 服务层
 * 封装所有API调用，提供类型安全的接口
 */

import { apiClient } from './api-client'
import { API_CONFIG, PaginationParams, ApiResponse } from './api-config'

// 数据类型定义
export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  role: string
  createdAt: string
  updatedAt: string
}

export interface Prompt {
  id: string
  title: string
  description?: string
  content: string
  category: string
  tags: string[]
  userId: string
  authorName?: string
  authorAvatar?: string
  status: 'draft' | 'published' | 'archived'
  isPrivate: boolean
  likes: number
  downloads: number
  views: number
  isLiked?: boolean
  createdAt: string
  updatedAt: string
}

export interface Category {
  id: string
  name: string
  description: string
  icon?: string
  color?: string
  promptCount: number
}

export interface LoginRequest {
  identifier: string // 用户名、邮箱或手机号
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  phone?: string
}

export interface AuthResponse {
  user: User
  token?: string
  refreshToken?: string
}

export interface SearchParams extends PaginationParams {
  q?: string
  category?: string
  tags?: string
  userOnly?: boolean
}

/**
 * 认证服务
 */
export class AuthService {
  /**
   * 用户登录
   */
  static async login(data: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await apiClient.post<AuthResponse>(
      API_CONFIG.ENDPOINTS.AUTH.LOGIN,
      data,
      false // 登录不需要认证
    )

    // 如果登录成功，保存认证信息
    if (response.success && response.data) {
      const { token, refreshToken, user } = response.data
      if (token) {
        await apiClient.setAuth(token, refreshToken, user)
      }
    }

    return response
  }

  /**
   * 用户注册
   */
  static async register(data: RegisterRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await apiClient.post<AuthResponse>(
      API_CONFIG.ENDPOINTS.AUTH.REGISTER,
      data,
      false // 注册不需要认证
    )

    // 如果注册成功，保存认证信息
    if (response.success && response.data) {
      const { token, refreshToken, user } = response.data
      if (token) {
        await apiClient.setAuth(token, refreshToken, user)
      }
    }

    return response
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<ApiResponse<User>> {
    return apiClient.get<User>(API_CONFIG.ENDPOINTS.AUTH.ME, undefined, true)
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    try {
      await apiClient.post(API_CONFIG.ENDPOINTS.AUTH.LOGOUT, undefined, true)
    } catch (error) {
      console.warn('Logout API call failed:', error)
    } finally {
      await apiClient.logout()
    }
  }
}

/**
 * 提示词服务
 */
export class PromptService {
  /**
   * 获取公开提示词列表
   */
  static async getPublicPrompts(params: PaginationParams = {}): Promise<ApiResponse<Prompt[]>> {
    return apiClient.get<Prompt[]>(API_CONFIG.ENDPOINTS.PROMPTS.PUBLIC, params, false)
  }

  /**
   * 搜索提示词
   */
  static async searchPrompts(params: SearchParams = {}): Promise<ApiResponse<Prompt[]>> {
    const { q, userOnly, ...otherParams } = params

    // 根据 userOnly 选择不同的端点
    const endpoint = userOnly ? API_CONFIG.ENDPOINTS.PROMPTS.MY_LIST : API_CONFIG.ENDPOINTS.PROMPTS.PUBLIC

    // 转换参数格式以匹配后端 API
    const apiParams = {
      ...otherParams,
      search: q, // 将 q 转换为 search
    }

    return apiClient.get<Prompt[]>(endpoint, apiParams, userOnly) // 我的提示词需要认证
  }

  /**
   * 获取提示词详情
   */
  static async getPromptDetail(id: string): Promise<ApiResponse<Prompt>> {
    return apiClient.get<Prompt>(API_CONFIG.ENDPOINTS.PROMPTS.DETAIL(id), undefined, true)
  }

  /**
   * 获取我的提示词列表
   */
  static async getMyPrompts(params: PaginationParams = {}): Promise<ApiResponse<Prompt[]>> {
    return apiClient.get<Prompt[]>(API_CONFIG.ENDPOINTS.PROMPTS.MY_LIST, params, true)
  }

  /**
   * 创建提示词
   */
  static async createPrompt(data: Partial<Prompt>): Promise<ApiResponse<{ id: string }>> {
    return apiClient.post<{ id: string }>(API_CONFIG.ENDPOINTS.PROMPTS.CREATE, data, true)
  }

  /**
   * 更新提示词
   */
  static async updatePrompt(id: string, data: Partial<Prompt>): Promise<ApiResponse<Prompt>> {
    return apiClient.put<Prompt>(API_CONFIG.ENDPOINTS.PROMPTS.UPDATE(id), data, true)
  }

  /**
   * 删除提示词
   */
  static async deletePrompt(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.PROMPTS.DELETE(id), true)
  }

  /**
   * 点赞/取消点赞提示词
   */
  static async toggleLike(id: string): Promise<ApiResponse<{ isLiked: boolean; likes: number }>> {
    return apiClient.post<{ isLiked: boolean; likes: number }>(
      API_CONFIG.ENDPOINTS.PROMPTS.LIKE(id),
      undefined,
      true
    )
  }

  /**
   * 记录提示词复制
   */
  static async recordCopy(id: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(API_CONFIG.ENDPOINTS.PROMPTS.COPY(id), undefined, false)
  }
}

/**
 * 分类服务
 */
export class CategoryService {
  /**
   * 获取所有分类
   */
  static async getCategories(): Promise<ApiResponse<Category[]>> {
    return apiClient.get<Category[]>(API_CONFIG.ENDPOINTS.CATEGORIES.LIST, undefined, false)
  }

  /**
   * 获取分类统计
   */
  static async getCategoryStats(): Promise<ApiResponse<Category[]>> {
    return apiClient.get<Category[]>(API_CONFIG.ENDPOINTS.CATEGORIES.STATS, undefined, false)
  }
}

/**
 * 用户服务
 */
export class UserService {
  /**
   * 更新用户资料
   */
  static async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    return apiClient.put<User>(API_CONFIG.ENDPOINTS.USER.UPDATE_PROFILE, data, true)
  }

  /**
   * 修改密码
   */
  static async changePassword(data: {
    currentPassword: string
    newPassword: string
  }): Promise<ApiResponse<void>> {
    return apiClient.post<void>(API_CONFIG.ENDPOINTS.USER.CHANGE_PASSWORD, data, true)
  }
}

// 导出所有服务
export const apiServices = {
  auth: AuthService,
  prompt: PromptService,
  category: CategoryService,
  user: UserService,
}

/**
 * PromptHub 桌面客户端 API 客户端
 * 统一处理所有API请求、认证、错误处理和重试机制
 */

import { invoke } from '@tauri-apps/api/core'
import {
  API_CONFIG,
  buildApiUrl,
  getAuthHeaders,
  ApiResponse,
  ApiError,
  RequestConfig,
  PaginationParams
} from './api-config'
import { globalCache } from './cache-manager'

export class ApiClient {
  private token: string | null = null
  private refreshToken: string | null = null

  constructor() {
    this.loadTokenFromStorage()
  }

  /**
   * 从本地存储加载token
   */
  private async loadTokenFromStorage() {
    try {
      const storedToken = await invoke('get_storage_item', { key: API_CONFIG.AUTH.TOKEN_KEY })
      const storedRefreshToken = await invoke('get_storage_item', { key: API_CONFIG.AUTH.REFRESH_TOKEN_KEY })
      
      this.token = storedToken as string || null
      this.refreshToken = storedRefreshToken as string || null
    } catch (error) {
      console.warn('Failed to load tokens from storage:', error)
    }
  }

  /**
   * 保存token到本地存储
   */
  private async saveTokenToStorage(token: string, refreshToken?: string) {
    try {
      await invoke('set_storage_item', { key: API_CONFIG.AUTH.TOKEN_KEY, value: token })
      if (refreshToken) {
        await invoke('set_storage_item', { key: API_CONFIG.AUTH.REFRESH_TOKEN_KEY, value: refreshToken })
      }
      this.token = token
      this.refreshToken = refreshToken || this.refreshToken
    } catch (error) {
      console.error('Failed to save tokens to storage:', error)
    }
  }

  /**
   * 清除认证信息
   */
  private async clearAuth() {
    try {
      await invoke('remove_storage_item', { key: API_CONFIG.AUTH.TOKEN_KEY })
      await invoke('remove_storage_item', { key: API_CONFIG.AUTH.REFRESH_TOKEN_KEY })
      await invoke('remove_storage_item', { key: API_CONFIG.AUTH.USER_KEY })
      this.token = null
      this.refreshToken = null
    } catch (error) {
      console.error('Failed to clear auth from storage:', error)
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(endpoint: string, method: string, body?: any): string {
    const bodyHash = body ? JSON.stringify(body) : '';
    return `${method}:${endpoint}:${bodyHash}`;
  }

  /**
   * 根据端点获取缓存TTL
   */
  private getCacheTTL(endpoint: string): number {
    if (endpoint.includes('/prompts/public') || endpoint.includes('/prompts/search')) {
      return API_CONFIG.CACHE.PROMPTS_TTL;
    }
    if (endpoint.includes('/categories')) {
      return API_CONFIG.CACHE.CATEGORIES_TTL;
    }
    if (endpoint.includes('/auth/me') || endpoint.includes('/user')) {
      return API_CONFIG.CACHE.USER_TTL;
    }
    // 默认缓存时间
    return 2 * 60 * 1000; // 2分钟
  }

  /**
   * 发送HTTP请求
   */
  private async request<T>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      body,
      timeout = API_CONFIG.TIMEOUT,
      retries = API_CONFIG.RETRY_COUNT,
      requireAuth = false,
      cache = method === 'GET', // 默认只缓存GET请求
    } = config

    // 检查缓存（仅对GET请求且启用缓存时）
    if (cache && method === 'GET') {
      const cacheKey = this.generateCacheKey(endpoint, method, body);
      const cachedResponse = globalCache.get<ApiResponse<T>>(cacheKey);

      if (cachedResponse) {
        console.log(`🎯 Cache hit for ${method} ${endpoint}`);
        return cachedResponse;
      }
    }

    const url = buildApiUrl(endpoint)
    const headers = getAuthHeaders(requireAuth ? this.token || undefined : undefined)

    // 合并自定义headers
    if (config.headers) {
      Object.assign(headers, config.headers)
    }

    const requestOptions = {
      method,
      url,
      headers,
      body: body ? JSON.stringify(body) : undefined,
      timeout,
    }

    let lastError: Error | null = null

    // 重试机制
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        console.log(`🌐 API Request [${method}] ${endpoint} (attempt ${attempt + 1})`)
        console.log('📋 Request Options:', JSON.stringify(requestOptions, null, 2))

        const response = await invoke('http_request', requestOptions) as any
        console.log('📡 Raw Response:', response)
        
        // 检查响应状态
        if (response.status >= 200 && response.status < 300) {
          const data = response.data ? JSON.parse(response.data) : null
          console.log(`✅ API Success [${method}] ${endpoint}`)

          // 缓存成功的GET请求响应
          if (cache && method === 'GET' && data) {
            const cacheKey = this.generateCacheKey(endpoint, method, body);
            const ttl = this.getCacheTTL(endpoint);
            globalCache.set(cacheKey, data, ttl);
            console.log(`💾 Cached response for ${method} ${endpoint} (TTL: ${ttl}ms)`);
          }

          return data
        }

        // 处理认证错误
        if (response.status === 401 && requireAuth && this.refreshToken) {
          console.log('🔄 Token expired, attempting refresh...')
          const refreshed = await this.refreshAuthToken()
          if (refreshed && attempt < retries) {
            continue // 重试请求
          }
        }

        // 处理其他HTTP错误
        const errorData = response.data ? JSON.parse(response.data) : null
        throw new Error(errorData?.message || `HTTP ${response.status}`)

      } catch (error) {
        lastError = error as Error
        console.error(`❌ API Error [${method}] ${endpoint} (attempt ${attempt + 1}):`, error)

        // 如果是最后一次尝试，抛出错误
        if (attempt === retries) {
          break
        }

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)))
      }
    }

    // 抛出最后的错误
    throw lastError || new Error('Request failed after all retries')
  }

  /**
   * 刷新认证token
   */
  private async refreshAuthToken(): Promise<boolean> {
    if (!this.refreshToken) {
      await this.clearAuth()
      return false
    }

    try {
      const response = await this.request<{ token: string; refreshToken?: string }>(
        API_CONFIG.ENDPOINTS.AUTH.REFRESH,
        {
          method: 'POST',
          body: { refreshToken: this.refreshToken },
        }
      )

      if (response.success && response.data) {
        await this.saveTokenToStorage(response.data.token, response.data.refreshToken)
        return true
      }
    } catch (error) {
      console.error('Failed to refresh token:', error)
    }

    await this.clearAuth()
    return false
  }

  /**
   * GET 请求
   */
  async get<T>(endpoint: string, params?: Record<string, any>, requireAuth = false): Promise<ApiResponse<T>> {
    let url = endpoint
    if (params) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      url += `?${searchParams.toString()}`
    }

    return this.request<T>(url, { method: 'GET', requireAuth })
  }

  /**
   * POST 请求
   */
  async post<T>(endpoint: string, body?: any, requireAuth = true): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'POST', body, requireAuth })
  }

  /**
   * PUT 请求
   */
  async put<T>(endpoint: string, body?: any, requireAuth = true): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PUT', body, requireAuth })
  }

  /**
   * DELETE 请求
   */
  async delete<T>(endpoint: string, requireAuth = true): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE', requireAuth })
  }

  /**
   * 设置认证token
   */
  async setAuth(token: string, refreshToken?: string, user?: any) {
    await this.saveTokenToStorage(token, refreshToken)
    if (user) {
      await invoke('set_storage_item', { key: API_CONFIG.AUTH.USER_KEY, value: JSON.stringify(user) })
    }
  }

  /**
   * 清除认证
   */
  async logout() {
    await this.clearAuth()
  }

  /**
   * 检查是否已认证
   */
  isAuthenticated(): boolean {
    return !!this.token
  }

  /**
   * 获取当前token
   */
  getToken(): string | null {
    return this.token
  }

  /**
   * 清除特定端点的缓存
   */
  clearCache(pattern?: string): void {
    if (pattern) {
      // 清除匹配模式的缓存
      const stats = globalCache.getStats();
      console.log(`🗑️ Clearing cache for pattern: ${pattern}`);
      // 这里可以实现更复杂的模式匹配逻辑
    } else {
      // 清除所有缓存
      globalCache.clear();
      console.log('🗑️ Cleared all cache');
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return globalCache.getStats();
  }
}

// 创建全局API客户端实例
export const apiClient = new ApiClient()

import { create } from "zustand";

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number; // 毫秒，0表示不自动消失
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface NotificationState {
  notifications: Notification[];
}

interface NotificationActions {
  addNotification: (notification: Omit<Notification, 'id'>) => string;
  removeNotification: (id: string) => void;
  clearAll: () => void;
  showSuccess: (title: string, message?: string, duration?: number) => string;
  showError: (title: string, message?: string, duration?: number) => string;
  showWarning: (title: string, message?: string, duration?: number) => string;
  showInfo: (title: string, message?: string, duration?: number) => string;
}

export const useNotificationStore = create<NotificationState & NotificationActions>((set, get) => ({
  // 状态
  notifications: [],

  // 添加通知
  addNotification: (notification) => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const newNotification: Notification = {
      id,
      duration: 3000, // 默认3秒
      ...notification,
    };

    set(state => ({
      notifications: [...state.notifications, newNotification]
    }));

    // 自动移除通知
    if (newNotification.duration && newNotification.duration > 0) {
      setTimeout(() => {
        get().removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  },

  // 移除通知
  removeNotification: (id) => {
    set(state => ({
      notifications: state.notifications.filter(n => n.id !== id)
    }));
  },

  // 清除所有通知
  clearAll: () => {
    set({ notifications: [] });
  },

  // 快捷方法
  showSuccess: (title, message, duration = 3000) => {
    return get().addNotification({
      type: 'success',
      title,
      message,
      duration,
    });
  },

  showError: (title, message, duration = 5000) => {
    return get().addNotification({
      type: 'error',
      title,
      message,
      duration,
    });
  },

  showWarning: (title, message, duration = 4000) => {
    return get().addNotification({
      type: 'warning',
      title,
      message,
      duration,
    });
  },

  showInfo: (title, message, duration = 3000) => {
    return get().addNotification({
      type: 'info',
      title,
      message,
      duration,
    });
  },
}));

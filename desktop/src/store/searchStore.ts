import { create } from "zustand";
import { apiServices, Prompt, SearchParams } from "../lib/api-services";

interface SearchState {
  query: string;
  results: Prompt[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  total: number;
}

interface SearchActions {
  setQuery: (query: string) => void;
  search: (params: SearchParams) => Promise<void>;
  clearResults: () => void;
  clearError: () => void;
}

export const useSearchStore = create<SearchState & SearchActions>((set, _get) => ({
  // 状态
  query: "",
  results: [],
  isLoading: false,
  error: null,
  hasMore: false,
  total: 0,

  // 设置搜索查询
  setQuery: (query) => set({ query }),

  // 执行搜索
  search: async (params: SearchParams) => {
    set({ isLoading: true, error: null });

    try {
      const response = await apiServices.prompt.searchPrompts(params);

      if (response.success && response.data) {
        const prompts = response.data;
        set({
          results: prompts,
          total: response.pagination?.total || prompts.length,
          hasMore: response.pagination ?
            (response.pagination.page * response.pagination.limit) < response.pagination.total :
            false,
          isLoading: false,
          error: null,
        });
      } else {
        set({
          results: [],
          total: 0,
          hasMore: false,
          isLoading: false,
          error: response.message || "搜索失败",
        });
      }
    } catch (error) {
      set({
        results: [],
        total: 0,
        hasMore: false,
        isLoading: false,
        error: error instanceof Error ? error.message : "搜索失败",
      });
    }
  },

  // 清除搜索结果
  clearResults: () => set({
    results: [],
    total: 0,
    hasMore: false,
    error: null,
  }),

  // 清除错误
  clearError: () => set({ error: null }),
}));

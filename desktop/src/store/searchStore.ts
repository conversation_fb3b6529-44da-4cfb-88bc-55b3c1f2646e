import { create } from "zustand";
import { invoke } from "@tauri-apps/api/core";
import { Prompt, SearchRequest, SearchResponse } from "../types";

interface SearchState {
  query: string;
  results: Prompt[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  total: number;
}

interface SearchActions {
  setQuery: (query: string) => void;
  search: (request: SearchRequest) => Promise<void>;
  clearResults: () => void;
  clearError: () => void;
}

export const useSearchStore = create<SearchState & SearchActions>((set, _get) => ({
  // 状态
  query: "",
  results: [],
  isLoading: false,
  error: null,
  hasMore: false,
  total: 0,

  // 设置搜索查询
  setQuery: (query) => set({ query }),

  // 执行搜索
  search: async (request: SearchRequest) => {
    set({ isLoading: true, error: null });
    
    try {
      const response: SearchResponse = await invoke("search_prompts", {
        request
      });
      
      if (response.success && response.data) {
        set({
          results: response.data.prompts,
          total: response.data.total,
          hasMore: response.data.has_more,
          isLoading: false,
          error: null,
        });
      } else {
        set({
          results: [],
          total: 0,
          hasMore: false,
          isLoading: false,
          error: response.error || "搜索失败",
        });
      }
    } catch (error) {
      set({
        results: [],
        total: 0,
        hasMore: false,
        isLoading: false,
        error: error instanceof Error ? error.message : "搜索失败",
      });
    }
  },

  // 清除搜索结果
  clearResults: () => set({
    results: [],
    total: 0,
    hasMore: false,
    error: null,
  }),

  // 清除错误
  clearError: () => set({ error: null }),
}));

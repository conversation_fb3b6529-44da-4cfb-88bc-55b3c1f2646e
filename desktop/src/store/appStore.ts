import { create } from "zustand";
import { invoke } from "@tauri-apps/api/core";
import { WindowManager, WindowSizeKey } from "../utils/windowManager";

export type AppPage = 'login' | 'main' | 'settings';

interface AppState {
  isVisible: boolean;
  currentView: "search" | "prompt-detail";
  selectedPromptId: string | null;
  currentPage: AppPage;
  isAuthenticated: boolean;
}

interface AppActions {
  showApp: () => void;
  hideApp: () => void;
  setCurrentView: (view: "search" | "prompt-detail") => void;
  setSelectedPrompt: (promptId: string | null) => void;
  setCurrentPage: (page: AppPage) => void;
  login: () => void;
  logout: () => void;
}

export const useAppStore = create<AppState & AppActions>((set, get) => ({
  // 状态
  isVisible: true, // 启动时默认显示
  currentView: "search",
  selectedPromptId: null,
  currentPage: "login", // 默认显示登录页面
  isAuthenticated: false,

  // 显示应用
  showApp: async () => {
    try {
      await invoke("show_window");
      set({
        isVisible: true,
        currentView: "search",
        selectedPromptId: null
      });
    } catch (error) {
      console.error("Failed to show window:", error);
    }
  },

  // 隐藏应用
  hideApp: async () => {
    try {
      await invoke("hide_window");
      set({
        isVisible: false,
        currentView: "search",
        selectedPromptId: null
      });
    } catch (error) {
      console.error("Failed to hide window:", error);
    }
  },

  // 设置当前视图
  setCurrentView: (view) => set({ currentView: view }),

  // 设置选中的提示词
  setSelectedPrompt: (promptId) => set({
    selectedPromptId: promptId,
    currentView: promptId ? "prompt-detail" : "search"
  }),

  // 设置当前页面
  setCurrentPage: async (page: AppPage) => {
    set({ currentPage: page });
    console.log(`📄 Page changed to: ${page}`);
  },

  // 登录成功
  login: async () => {
    set({ isAuthenticated: true });
    console.log('✅ Login successful, switching to main page');
    await get().setCurrentPage('main');
  },

  // 退出登录
  logout: async () => {
    set({
      isAuthenticated: false,
      currentView: "search",
      selectedPromptId: null
    });
    console.log('🚪 Logout, switching to login page');
    await get().setCurrentPage('login');
  },
}));

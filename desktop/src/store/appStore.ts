import { create } from "zustand";
import { invoke } from "@tauri-apps/api/core";

interface AppState {
  isVisible: boolean;
  currentView: "search" | "prompt-detail";
  selectedPromptId: string | null;
}

interface AppActions {
  showApp: () => void;
  hideApp: () => void;
  setCurrentView: (view: "search" | "prompt-detail") => void;
  setSelectedPrompt: (promptId: string | null) => void;
}

export const useAppStore = create<AppState & AppActions>((set) => ({
  // 状态
  isVisible: true, // 启动时默认显示
  currentView: "search",
  selectedPromptId: null,

  // 显示应用
  showApp: async () => {
    try {
      await invoke("show_window");
      set({ isVisible: true });
    } catch (error) {
      console.error("Failed to show window:", error);
    }
  },

  // 隐藏应用
  hideApp: async () => {
    try {
      await invoke("hide_window");
      set({
        isVisible: false,
        currentView: "search",
        selectedPromptId: null
      });
    } catch (error) {
      console.error("Failed to hide window:", error);
    }
  },

  // 设置当前视图
  setCurrentView: (view) => set({ currentView: view }),

  // 设置选中的提示词
  setSelectedPrompt: (promptId) => set({ 
    selectedPromptId: promptId,
    currentView: promptId ? "prompt-detail" : "search"
  }),
}));

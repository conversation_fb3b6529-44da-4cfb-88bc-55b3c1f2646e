import { create } from "zustand";
import { apiServices } from "../lib/api-services";

interface UserInteractionState {
  likedPrompts: Set<string>;
  copiedPrompts: Set<string>;
  isLoading: boolean;
  error: string | null;
}

interface UserInteractionActions {
  toggleLike: (promptId: string) => Promise<{ isLiked: boolean; likes: number } | null>;
  recordCopy: (promptId: string) => Promise<void>;
  isPromptLiked: (promptId: string) => boolean;
  isPromptCopied: (promptId: string) => boolean;
  clearError: () => void;
}

export const useUserInteractionStore = create<UserInteractionState & UserInteractionActions>((set, get) => ({
  // 状态
  likedPrompts: new Set(),
  copiedPrompts: new Set(),
  isLoading: false,
  error: null,

  // 切换点赞状态
  toggleLike: async (promptId: string) => {
    const { likedPrompts } = get();
    set({ isLoading: true, error: null });

    try {
      const response = await apiServices.prompt.toggleLike(promptId);
      
      if (response.success && response.data) {
        const { isLiked, likes } = response.data;
        
        // 更新本地状态
        const newLikedPrompts = new Set(likedPrompts);
        if (isLiked) {
          newLikedPrompts.add(promptId);
        } else {
          newLikedPrompts.delete(promptId);
        }
        
        set({ 
          likedPrompts: newLikedPrompts,
          isLoading: false 
        });
        
        return { isLiked, likes };
      } else {
        set({ 
          error: response.message || '操作失败',
          isLoading: false 
        });
        return null;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '操作失败';
      set({ 
        error: errorMessage,
        isLoading: false 
      });
      return null;
    }
  },

  // 记录复制操作
  recordCopy: async (promptId: string) => {
    const { copiedPrompts } = get();
    
    try {
      await apiServices.prompt.recordCopy(promptId);
      
      // 更新本地状态
      const newCopiedPrompts = new Set(copiedPrompts);
      newCopiedPrompts.add(promptId);
      
      set({ copiedPrompts: newCopiedPrompts });
    } catch (error) {
      console.warn('Failed to record copy:', error);
      // 复制记录失败不影响用户体验，只记录警告
    }
  },

  // 检查提示词是否已点赞
  isPromptLiked: (promptId: string) => {
    return get().likedPrompts.has(promptId);
  },

  // 检查提示词是否已复制
  isPromptCopied: (promptId: string) => {
    return get().copiedPrompts.has(promptId);
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },
}));

import { create } from "zustand";
import { apiServices, User, LoginRequest } from "../lib/api-services";
import { apiClient } from "../lib/api-client";

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (data: LoginRequest) => Promise<boolean>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  clearError: () => void;
}

export const useAuthStore = create<AuthState & AuthActions>((set, _get) => ({
  // 状态
  isAuthenticated: false,
  user: null,
  token: null,
  isLoading: false,
  error: null,

  // 登录
  login: async (data: LoginRequest) => {
    console.log("🔐 Starting login with data:", data);
    set({ isLoading: true, error: null });

    try {
      console.log("📞 Calling apiServices.auth.login...");
      const response = await apiServices.auth.login(data);

      console.log("✅ Login response received:", response);

      if (response.success && response.data) {
        console.log("Login successful, setting auth state");
        set({
          isAuthenticated: true,
          user: response.data.user,
          token: response.data.token || null,
          isLoading: false,
          error: null,
        });
        return true;
      } else {
        console.log("Login failed:", response.message);
        set({
          isLoading: false,
          error: response.message || "登录失败",
        });
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "登录失败";
      console.error("Login error:", error);
      set({
        isLoading: false,
        error: errorMessage,
      });
      return false;
    }
  },

  // 登出
  logout: async () => {
    try {
      await apiServices.auth.logout();
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      set({
        isAuthenticated: false,
        user: null,
        token: null,
        error: null,
      });
    }
  },

  // 检查认证状态
  checkAuth: async () => {
    // 检查是否有token
    if (!apiClient.isAuthenticated()) {
      set({
        isAuthenticated: false,
        user: null,
        token: null
      });
      return;
    }

    try {
      const response = await apiServices.auth.getCurrentUser();

      if (response.success && response.data) {
        set({
          isAuthenticated: true,
          user: response.data,
          token: apiClient.getToken(),
        });
      } else {
        // Token可能已过期，清除认证状态
        await apiClient.logout();
        set({
          isAuthenticated: false,
          user: null,
          token: null,
        });
      }
    } catch (error) {
      console.error("Auth check error:", error);
      await apiClient.logout();
      set({
        isAuthenticated: false,
        user: null,
        token: null,
      });
    }
  },

  // 清除错误
  clearError: () => set({ error: null }),
}));

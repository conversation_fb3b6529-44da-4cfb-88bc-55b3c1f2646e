import { create } from "zustand";
import { invoke } from "@tauri-apps/api/core";
import { User, AuthResponse } from "../types";

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (emailOrUsername: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  clearError: () => void;
}

export const useAuthStore = create<AuthState & AuthActions>((set, _get) => ({
  // 状态
  isAuthenticated: false,
  user: null,
  token: null,
  isLoading: false,
  error: null,

  // 登录
  login: async (emailOrUsername: string, password: string) => {
    set({ isLoading: true, error: null });

    try {
      const response: AuthResponse = await invoke("login", {
        request: { email_or_username: emailOrUsername, password }
      });
      
      console.log("Login response:", response);

      if (response.success && response.data) {
        console.log("Login successful, setting auth state");
        set({
          isAuthenticated: true,
          user: response.data.user,
          token: response.data.access_token,
          isLoading: false,
          error: null,
        });
        return true;
      } else {
        console.log("Login failed:", response.error);
        set({
          isLoading: false,
          error: response.error || "登录失败",
        });
        return false;
      }
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : "登录失败",
      });
      return false;
    }
  },

  // 登出
  logout: async () => {
    try {
      await invoke("logout");
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      set({
        isAuthenticated: false,
        user: null,
        token: null,
        error: null,
      });
    }
  },

  // 检查认证状态
  checkAuth: async () => {
    try {
      const response: AuthResponse = await invoke("verify_token");
      
      if (response.success && response.data) {
        set({
          isAuthenticated: true,
          user: response.data.user,
          token: response.data.access_token,
        });
      } else {
        set({
          isAuthenticated: false,
          user: null,
          token: null,
        });
      }
    } catch (error) {
      console.error("Auth check error:", error);
      set({
        isAuthenticated: false,
        user: null,
        token: null,
      });
    }
  },

  // 清除错误
  clearError: () => set({ error: null }),
}));

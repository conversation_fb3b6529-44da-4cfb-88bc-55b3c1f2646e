// 用户类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  subscription_type: string;
}

// 认证数据类型
export interface AuthData {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  user: User;
}

// 认证响应类型
export interface AuthResponse {
  success: boolean;
  data?: AuthData;
  error?: string;
}

// 提示词类型
export interface Prompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  category: string;
  tags: string[];
  user_id: string;
  is_public: boolean;
  likes: number;
  downloads: number;
  views: number;
  created_at: string;
  updated_at: string;
}

// 搜索请求类型
export interface SearchRequest {
  query: string;
  limit?: number;
  offset?: number;
  category?: string;
  user_only?: boolean;
}

// 搜索响应类型
export interface SearchResponse {
  success: boolean;
  data?: {
    prompts: Prompt[];
    total: number;
    has_more: boolean;
  };
  error?: string;
}

// 使用记录类型
export interface UsageRecord {
  prompt_id: string;
  user_input: string;
}

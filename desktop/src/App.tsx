import { useEffect } from "react";
import { listen } from "@tauri-apps/api/event";
import { invoke } from "@tauri-apps/api/core";
import CommandPalette from "./components/CommandPalette";
import NotificationContainer from "./components/NotificationContainer";
import PerformanceMonitor from "./components/PerformanceMonitor";
import { useAppStore } from "./store/appStore";

function App() {
  const { isVisible, showApp, hideApp } = useAppStore();

  useEffect(() => {
    // 监听全局快捷键事件
    const unlistenShortcut = listen("global-shortcut-pressed", () => {
      console.log("Global shortcut event received, current isVisible:", isVisible);
      if (isVisible) {
        console.log("Hiding app...");
        hideApp();
      } else {
        console.log("Showing app...");
        showApp();
      }
    });

    return () => {
      unlistenShortcut.then((fn) => fn());
    };
  }, [isVisible, showApp, hideApp]);

  // 显示/隐藏窗口
  useEffect(() => {
    if (isVisible) {
      invoke("show_window");
    } else {
      invoke("hide_window");
    }
  }, [isVisible]);

  return (
    <div className="min-h-screen bg-transparent">
      {/* 主内容 */}
      <div className="relative">
        <CommandPalette />

        {/* 全局通知容器 */}
        <NotificationContainer />

        {/* 性能监控（仅开发环境） */}
        <PerformanceMonitor />
      </div>
    </div>
  );
}

export default App;

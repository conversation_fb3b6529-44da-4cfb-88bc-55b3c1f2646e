import { useEffect } from "react";
import { listen } from "@tauri-apps/api/event";
import { invoke } from "@tauri-apps/api/core";
import CommandPalette from "./components/CommandPalette";
import AuthModal from "./components/AuthModal";
import NotificationContainer from "./components/NotificationContainer";
import PerformanceMonitor from "./components/PerformanceMonitor";
import { useAuthStore } from "./store/authStore";
import { useAppStore } from "./store/appStore";

function App() {
  const { isAuthenticated, checkAuth } = useAuthStore();
  const { isVisible, showApp, hideApp } = useAppStore();

  // 添加调试日志
  console.log("🔍 App render - isAuthenticated:", isAuthenticated);

  useEffect(() => {
    // 检查认证状态
    checkAuth();

    // 监听全局快捷键事件
    const unlistenShortcut = listen("global-shortcut-pressed", () => {
      console.log("Global shortcut event received, current isVisible:", isVisible);
      if (isVisible) {
        console.log("Hiding app...");
        hideApp();
      } else {
        console.log("Showing app...");
        showApp();
      }
    });

    // 监听窗口失去焦点事件（仅在已登录时自动隐藏）
    const unlistenBlur = listen("tauri://blur", () => {
      if (isAuthenticated) {
        hideApp();
      }
    });

    return () => {
      unlistenShortcut.then((fn) => fn());
      unlistenBlur.then((fn) => fn());
    };
  }, [isVisible, showApp, hideApp, checkAuth, isAuthenticated]);

  // 显示/隐藏窗口
  useEffect(() => {
    if (isVisible) {
      invoke("show_window");
    } else {
      invoke("hide_window");
    }
  }, [isVisible]);

  return (
    <div className="min-h-screen bg-transparent">
      {/* 主内容 */}
      <div className="relative">
        {isAuthenticated ? (
          <CommandPalette />
        ) : (
          <AuthModal />
        )}

        {/* 全局通知容器 */}
        <NotificationContainer />

        {/* 性能监控（仅开发环境） */}
        <PerformanceMonitor />
      </div>
    </div>
  );
}

export default App;

import { useState, useEffect, useRef } from "react";
import { Co<PERSON>, Send, Loader2, X } from "lucide-react";
import { invoke } from "@tauri-apps/api/core";
import { useAppStore } from "../store/appStore";
import { Prompt } from "../types";

interface PromptDetailViewProps {
  onClose: () => void;
}

export default function PromptDetailView({ onClose }: PromptDetailViewProps) {
  const { selectedPromptId } = useAppStore();
  const modalRef = useRef<HTMLDivElement>(null);
  const [prompt, setPrompt] = useState<Prompt | null>(null);
  const [userInput, setUserInput] = useState("");
  const [systemPrompt, setSystemPrompt] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 获取提示词详情
  useEffect(() => {
    if (selectedPromptId) {
      setIsLoading(true);
      invoke("get_prompt_detail", { promptId: selectedPromptId })
        .then((response: any) => {
          if (response.success && response.data) {
            setPrompt(response.data);
            setSystemPrompt(response.data.content || '');
          }
        })
        .catch((error) => {
          console.error("Failed to get prompt detail:", error);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [selectedPromptId]);

  // 关闭详情窗体
  const handleClose = () => {
    onClose();
  };

  // 点击外部关闭
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  // 复制系统提示词
  const handleCopySystemPrompt = async () => {
    if (prompt) {
      try {
        await invoke("copy_to_clipboard", { text: systemPrompt });
        // TODO: 显示复制成功提示
      } catch (error) {
        console.error("Failed to copy:", error);
      }
    }
  };

  // 使用提示词（组合系统提示词和用户输入）
  const handleUsePrompt = async () => {
    if (!prompt || !userInput.trim()) return;

    setIsSubmitting(true);
    try {
      const combinedText = `${systemPrompt}\n\n${userInput.trim()}`;
      
      // 注入文本到其他应用
      await invoke("inject_text", { text: combinedText });
      
      // 记录使用
      await invoke("record_usage", {
        request: {
          prompt_id: prompt.id,
          user_input: userInput.trim(),
        }
      });

      // 关闭应用
      // TODO: 隐藏应用窗口
    } catch (error) {
      console.error("Failed to use prompt:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理键盘快捷键
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleUsePrompt();
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="w-8 h-8 animate-spin text-white/60" />
      </div>
    );
  }

  if (!prompt) {
    return (
      <div className="text-center py-12">
        <p className="text-white/60 text-lg">提示词不存在</p>
      </div>
    );
  }

  return (
    <div
      className="fixed inset-0 bg-transparent z-50 flex items-center justify-center p-8"
      onClick={handleBackdropClick}
    >
      <div
        ref={modalRef}
        className="bg-white rounded-2xl shadow-2xl border border-gray-200 w-[95%] max-w-5xl max-h-[90vh] overflow-hidden animate-scale-in"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 - 品牌区域 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/>
                <path d="M5 3v4"/>
                <path d="M19 17v4"/>
                <path d="M3 5h4"/>
                <path d="M17 19h4"/>
              </svg>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">PromptHub</h1>
              <p className="text-sm text-gray-500">AI提示词分享、创作平台</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-8 space-y-6 overflow-y-auto scrollable max-h-[calc(90vh-220px)]">
          {/* 系统提示词 */}
          <div className="space-y-3">
            <label className="block text-base font-medium text-gray-900">系统提示词</label>
            <textarea
              value={systemPrompt}
              onChange={(e) => setSystemPrompt(e.target.value)}
              className="w-full p-4 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-700 resize-none focus:outline-none font-mono leading-relaxed overflow-y-auto"
              style={{
                minHeight: `${Math.min(Math.max(systemPrompt.split('\n').length, 1), 5) * 1.5}rem`,
                maxHeight: '7.5rem' // 5行的高度
              }}
            />
          </div>

          {/* 用户输入 */}
          <div className="space-y-3">
            <label className="block text-base font-medium text-gray-900">你的需求</label>
            <textarea
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="请在这里输入你的具体需求..."
              className="w-full h-32 p-4 border-2 border-blue-200 rounded-lg resize-none focus:outline-none focus:border-blue-400 text-sm transition-colors"
              autoFocus
            />
          </div>
        </div>

        {/* 底部按钮区域 */}
        <div className="flex items-center justify-between p-5 border-t border-gray-100">
          <div className="flex items-center space-x-3">
            <button
              onClick={handleCopySystemPrompt}
              className="flex items-center space-x-1.5 px-3 py-1.5 bg-red-500 hover:bg-red-600 text-white rounded-md transition-colors text-sm"
            >
              <span>❤️</span>
              <span>已点赞</span>
            </button>

            <button
              onClick={handleCopySystemPrompt}
              className="flex items-center space-x-1.5 px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors text-sm"
            >
              <span>📋</span>
              <span>复制到我的提示词</span>
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={onClose}
              className="px-4 py-1.5 text-gray-600 hover:text-gray-800 transition-colors text-sm"
            >
              取消
            </button>

            <button
              onClick={handleUsePrompt}
              disabled={!userInput.trim() || isSubmitting}
              className="px-4 py-1.5 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 disabled:from-gray-300 disabled:to-gray-400 text-white rounded-md transition-all duration-200 flex items-center space-x-1.5 disabled:cursor-not-allowed text-sm"
            >
              {isSubmitting ? (
                <Loader2 className="w-3.5 h-3.5 animate-spin" />
              ) : null}
              <span>{isSubmitting ? "处理中..." : "复制到剪贴板"}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

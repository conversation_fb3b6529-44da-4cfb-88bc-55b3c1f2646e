import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Send, Loader2, <PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { invoke } from "@tauri-apps/api/core";
import { useAppStore } from "../store/appStore";
import { useUserInteractionStore } from "../store/userInteractionStore";
import { useNotificationStore } from "../store/notificationStore";
import { apiServices, Prompt } from "../lib/api-services";

interface PromptDetailViewProps {
  onClose: () => void;
}

export default function PromptDetailView({ onClose }: PromptDetailViewProps) {
  const { selectedPromptId, hideApp } = useAppStore();
  const { toggleLike, recordCopy, isPromptLiked } = useUserInteractionStore();
  const { showSuccess, showError } = useNotificationStore();
  const modalRef = useRef<HTMLDivElement>(null);
  const [prompt, setPrompt] = useState<Prompt | null>(null);
  const [userInput, setUserInput] = useState("");
  const [systemPrompt, setSystemPrompt] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 获取提示词详情
  useEffect(() => {
    if (selectedPromptId) {
      setIsLoading(true);
      apiServices.prompt.getPromptDetail(selectedPromptId)
        .then((response) => {
          if (response.success && response.data) {
            setPrompt(response.data);
            setSystemPrompt(response.data.content || '');
          } else {
            console.error("Failed to get prompt detail:", response.message);
          }
        })
        .catch((error) => {
          console.error("Failed to get prompt detail:", error);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [selectedPromptId]);

  // 关闭详情窗体
  const handleClose = () => {
    onClose();
  };

  // 点击外部关闭
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  // 复制系统提示词
  const handleCopySystemPrompt = async () => {
    if (prompt) {
      try {
        await invoke("copy_to_clipboard", { text: systemPrompt });
        // 记录复制操作
        await recordCopy(prompt.id);
        showSuccess("复制成功", "系统提示词已复制到剪贴板");

        // 复制成功后隐藏应用，hideApp会自动重置状态
        setTimeout(async () => {
          try {
            await hideApp();
          } catch (error) {
            console.error("Failed to hide app:", error);
          }
        }, 200); // 延迟200ms让用户看到成功提示
      } catch (error) {
        console.error("Failed to copy:", error);
        showError("复制失败", "无法复制到剪贴板，请重试");
      }
    }
  };

  // 点赞/取消点赞
  const handleToggleLike = async () => {
    if (!prompt) return;

    const result = await toggleLike(prompt.id);
    if (result) {
      // 更新本地状态
      setPrompt(prev => prev ? {
        ...prev,
        isLiked: result.isLiked,
        likes: result.likes
      } : null);

      // 显示成功提示
      if (result.isLiked) {
        showSuccess("点赞成功", "感谢您的支持！");
      } else {
        showSuccess("取消点赞", "已取消点赞");
      }
    } else {
      showError("操作失败", "点赞操作失败，请重试");
    }
  };

  // 使用提示词（组合系统提示词和用户输入，或仅复制系统提示词）
  const handleUsePrompt = async () => {
    if (!prompt) {
      showError("操作失败", "提示词数据不完整");
      return;
    }

    setIsSubmitting(true);
    try {
      // 如果有用户输入，组合系统提示词和用户输入；否则只使用系统提示词
      const textToCopy = userInput.trim()
        ? `${systemPrompt}\n\n${userInput.trim()}`
        : systemPrompt;

      // 复制到剪贴板
      await invoke("copy_to_clipboard", { text: textToCopy });

      // 记录复制操作
      await recordCopy(prompt.id);

      showSuccess("复制成功", "提示词已复制到剪贴板");

      // 复制成功后隐藏应用，hideApp会自动重置状态
      setTimeout(async () => {
        try {
          await hideApp();
        } catch (error) {
          console.error("Failed to hide app:", error);
        }
      }, 200); // 延迟200ms让用户看到成功提示
    } catch (error) {
      console.error("Failed to copy prompt:", error);
      showError("复制失败", "无法复制到剪贴板，请重试");
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理键盘快捷键
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleUsePrompt();
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="w-8 h-8 animate-spin text-white/60" />
      </div>
    );
  }

  if (!prompt) {
    return (
      <div className="text-center py-12">
        <p className="text-white/60 text-lg">提示词不存在</p>
      </div>
    );
  }

  return (
    <div
      className="fixed inset-0 bg-transparent z-50 flex items-center justify-center p-8"
      onClick={handleBackdropClick}
    >
      <div
        ref={modalRef}
        className="bg-white rounded-2xl shadow-2xl border border-gray-200 w-[95%] max-w-5xl h-[95vh] overflow-hidden animate-scale-in flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 - 品牌区域 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100 flex-shrink-0">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-violet-600 to-cyan-600 rounded-xl flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
            </div>
            <div>
              <span className="text-xl font-bold bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent">
                PromptHub
              </span>
              <p className="text-sm text-gray-500">AI提示词分享、创作平台</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="p-8 pb-4 space-y-4 flex-1 flex flex-col min-h-0">
            {/* 系统提示词 */}
            <div className="flex flex-col space-y-3 flex-shrink-0">
              <div className="flex items-center justify-between">
                <label className="block text-base font-medium text-gray-900">系统提示词</label>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleToggleLike}
                    className={`flex items-center space-x-1 px-2 py-1 rounded-md transition-colors text-xs ${
                      (prompt?.isLiked || (prompt && isPromptLiked(prompt.id)))
                        ? 'bg-red-50 hover:bg-red-100 text-red-600 border border-red-200 hover:border-red-300'
                        : 'bg-gray-50 hover:bg-gray-100 text-gray-600 border border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <span>{(prompt?.isLiked || (prompt && isPromptLiked(prompt.id))) ? '❤️' : '🤍'}</span>
                    <span>{(prompt?.isLiked || (prompt && isPromptLiked(prompt.id))) ? '已点赞' : '点赞'}</span>
                    {prompt?.likes ? <span>({prompt.likes})</span> : null}
                  </button>

                  <button
                    onClick={handleCopySystemPrompt}
                    className="flex items-center space-x-1 px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors text-xs"
                  >
                    <span>📋</span>
                    <span>复制到我的提示词</span>
                  </button>
                </div>
              </div>
              <textarea
                value={systemPrompt}
                onChange={(e) => setSystemPrompt(e.target.value)}
                className="w-full p-4 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-700 resize-none focus:outline-none font-mono leading-relaxed overflow-y-auto"
                style={{
                  height: '152px' // 固定5行高度 (5 * 24 + 32)
                }}
              />
            </div>

            {/* 用户输入 */}
            <div className="flex flex-col space-y-3 flex-shrink-0">
              <div className="flex items-center justify-between">
                <label className="block text-base font-medium text-gray-900">你的需求</label>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500">快速打开：</span>
                  <button
                    onClick={() => window.open('https://chat.openai.com', '_blank')}
                    className="px-2 py-1 bg-green-100 hover:bg-green-200 text-green-700 rounded-md transition-colors text-xs"
                  >
                    ChatGPT
                  </button>
                  <button
                    onClick={() => window.open('https://gemini.google.com', '_blank')}
                    className="px-2 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-md transition-colors text-xs"
                  >
                    Gemini
                  </button>
                  <button
                    onClick={() => window.open('https://claude.ai', '_blank')}
                    className="px-2 py-1 bg-purple-100 hover:bg-purple-200 text-purple-700 rounded-md transition-colors text-xs"
                  >
                    Claude
                  </button>
                </div>
              </div>
              <textarea
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="请在这里输入你的具体需求..."
                className="w-full p-4 border-2 border-blue-200 rounded-lg resize-none focus:outline-none focus:border-blue-400 text-sm transition-colors overflow-y-auto"
                style={{ height: '104px' }} // 3行高度 (3 * 24 + 32)
                autoFocus
              />
            </div>
          </div>
        </div>

        {/* 底部按钮区域 */}
        <div className="flex items-center justify-end p-5 border-t border-gray-100 flex-shrink-0">
          <div className="flex items-center space-x-2">
            <button
              onClick={onClose}
              className="px-4 py-1.5 text-gray-600 hover:text-gray-800 transition-colors text-sm"
            >
              取消
            </button>

            <button
              onClick={handleUsePrompt}
              disabled={isSubmitting}
              className="px-4 py-1.5 bg-gradient-to-r from-violet-600 to-cyan-500 hover:from-violet-700 hover:to-cyan-600 disabled:from-gray-300 disabled:to-gray-400 text-white rounded-md transition-all duration-200 flex items-center space-x-1.5 disabled:cursor-not-allowed text-sm hover:-translate-y-0.5 hover:shadow-lg"
            >
              {isSubmitting ? (
                <Loader2 className="w-3.5 h-3.5 animate-spin" />
              ) : null}
              <span>{isSubmitting ? "处理中..." : "复制到剪贴板"}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

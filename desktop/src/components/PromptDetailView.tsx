import { useState, useEffect, useRef } from "react";
import { Co<PERSON>, Send, Loader2, X } from "lucide-react";
import { invoke } from "@tauri-apps/api/core";
import { useAppStore } from "../store/appStore";
import { Prompt } from "../types";

interface PromptDetailViewProps {
  onClose: () => void;
}

export default function PromptDetailView({ onClose }: PromptDetailViewProps) {
  const { selectedPromptId } = useAppStore();
  const modalRef = useRef<HTMLDivElement>(null);
  const [prompt, setPrompt] = useState<Prompt | null>(null);
  const [userInput, setUserInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 获取提示词详情
  useEffect(() => {
    if (selectedPromptId) {
      setIsLoading(true);
      invoke("get_prompt_detail", { promptId: selectedPromptId })
        .then((response: any) => {
          if (response.success && response.data) {
            setPrompt(response.data);
          }
        })
        .catch((error) => {
          console.error("Failed to get prompt detail:", error);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [selectedPromptId]);

  // 关闭详情窗体
  const handleClose = () => {
    onClose();
  };

  // 点击外部关闭
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  // 复制系统提示词
  const handleCopySystemPrompt = async () => {
    if (prompt) {
      try {
        await invoke("copy_to_clipboard", { text: prompt.content });
        // TODO: 显示复制成功提示
      } catch (error) {
        console.error("Failed to copy:", error);
      }
    }
  };

  // 使用提示词（组合系统提示词和用户输入）
  const handleUsePrompt = async () => {
    if (!prompt || !userInput.trim()) return;

    setIsSubmitting(true);
    try {
      const combinedText = `${prompt.content}\n\n${userInput.trim()}`;
      
      // 注入文本到其他应用
      await invoke("inject_text", { text: combinedText });
      
      // 记录使用
      await invoke("record_usage", {
        request: {
          prompt_id: prompt.id,
          user_input: userInput.trim(),
        }
      });

      // 关闭应用
      // TODO: 隐藏应用窗口
    } catch (error) {
      console.error("Failed to use prompt:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理键盘快捷键
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleUsePrompt();
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="w-8 h-8 animate-spin text-white/60" />
      </div>
    );
  }

  if (!prompt) {
    return (
      <div className="text-center py-12">
        <p className="text-white/60 text-lg">提示词不存在</p>
      </div>
    );
  }

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      <div
        ref={modalRef}
        className="bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-2xl max-h-[90vh] overflow-y-auto animate-scale-in"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          {/* 头部 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-800 mb-1">{prompt.title}</h2>
              {prompt.description && (
                <p className="text-gray-600">{prompt.description}</p>
              )}
            </div>
            <button
              onClick={handleClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* 系统提示词 */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <label className="text-lg font-semibold text-gray-800">
                系统提示词
              </label>
              <button
                onClick={handleCopySystemPrompt}
                className="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 border border-gray-200 rounded-lg flex items-center gap-2 text-gray-700 hover:text-gray-900 transition-all"
              >
                <Copy className="w-4 h-4" />
                复制
              </button>
            </div>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-gray-800 max-h-40 overflow-y-auto leading-relaxed">
              {prompt.content}
            </div>
          </div>

          {/* 用户输入 */}
          <div className="mb-6">
            <label className="text-lg font-semibold text-gray-800 mb-3 block">
              用户输入
            </label>
            <textarea
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入您的具体需求..."
              className="w-full h-32 p-4 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none leading-relaxed transition-all"
              autoFocus
            />
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-4">
            <button
              onClick={handleUsePrompt}
              disabled={!userInput.trim() || isSubmitting}
              className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white p-4 rounded-lg flex items-center justify-center gap-3 font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all"
            >
              {isSubmitting ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Send className="w-5 h-5" />
              )}
              使用提示词
            </button>
            <button
              onClick={handleCopySystemPrompt}
              className="px-6 py-4 bg-gray-100 hover:bg-gray-200 border border-gray-200 rounded-lg text-gray-700 hover:text-gray-900 font-medium transition-all"
            >
              仅复制
            </button>
          </div>

          {/* 底部提示 */}
          <div className="mt-6 pt-4 border-t border-gray-200 text-center">
            <p className="text-gray-500 text-sm">
              <span className="text-gray-700 font-medium">Ctrl/Cmd + Enter</span> 快速使用，
              <span className="text-gray-700 font-medium">Esc</span> 关闭
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

import { useState, useEffect, useRef } from "react";
import { Co<PERSON>, Send, Loader2, X } from "lucide-react";
import { invoke } from "@tauri-apps/api/core";
import { useAppStore } from "../store/appStore";
import { Prompt } from "../types";

interface PromptDetailViewProps {
  onClose: () => void;
}

export default function PromptDetailView({ onClose }: PromptDetailViewProps) {
  const { selectedPromptId } = useAppStore();
  const modalRef = useRef<HTMLDivElement>(null);
  const [prompt, setPrompt] = useState<Prompt | null>(null);
  const [userInput, setUserInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 获取提示词详情
  useEffect(() => {
    if (selectedPromptId) {
      setIsLoading(true);
      invoke("get_prompt_detail", { promptId: selectedPromptId })
        .then((response: any) => {
          if (response.success && response.data) {
            setPrompt(response.data);
          }
        })
        .catch((error) => {
          console.error("Failed to get prompt detail:", error);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [selectedPromptId]);

  // 关闭详情窗体
  const handleClose = () => {
    onClose();
  };

  // 点击外部关闭
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  // 复制系统提示词
  const handleCopySystemPrompt = async () => {
    if (prompt) {
      try {
        await invoke("copy_to_clipboard", { text: prompt.content });
        // TODO: 显示复制成功提示
      } catch (error) {
        console.error("Failed to copy:", error);
      }
    }
  };

  // 使用提示词（组合系统提示词和用户输入）
  const handleUsePrompt = async () => {
    if (!prompt || !userInput.trim()) return;

    setIsSubmitting(true);
    try {
      const combinedText = `${prompt.content}\n\n${userInput.trim()}`;
      
      // 注入文本到其他应用
      await invoke("inject_text", { text: combinedText });
      
      // 记录使用
      await invoke("record_usage", {
        request: {
          prompt_id: prompt.id,
          user_input: userInput.trim(),
        }
      });

      // 关闭应用
      // TODO: 隐藏应用窗口
    } catch (error) {
      console.error("Failed to use prompt:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理键盘快捷键
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleUsePrompt();
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="w-8 h-8 animate-spin text-white/60" />
      </div>
    );
  }

  if (!prompt) {
    return (
      <div className="text-center py-12">
        <p className="text-white/60 text-lg">提示词不存在</p>
      </div>
    );
  }

  return (
    <div
      className="fixed inset-0 bg-white/10 backdrop-blur-lg z-50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      <div
        ref={modalRef}
        className="bg-white/90 backdrop-blur-2xl rounded-3xl shadow-xl border border-white/30 w-full max-w-2xl max-h-[90vh] overflow-hidden animate-scale-in"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 - 简化设计 */}
        <div className="flex items-start justify-between p-6 pb-4">
          <div className="flex-1 min-w-0">
            <h2 className="text-xl font-semibold text-gray-900 mb-1 truncate">{prompt.title}</h2>
            {prompt.description && (
              <p className="text-sm text-gray-500 line-clamp-2">{prompt.description}</p>
            )}
          </div>
          <button
            onClick={handleClose}
            className="ml-4 p-1.5 hover:bg-gray-100/80 rounded-full transition-colors flex-shrink-0"
          >
            <X className="w-4 h-4 text-gray-400" />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="px-6 pb-6 space-y-6 overflow-y-auto scrollable max-h-[calc(90vh-140px)]">

          {/* 系统提示词 - 苹果风格 */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-700">系统提示词</h3>
              <button
                onClick={handleCopySystemPrompt}
                className="px-3 py-1.5 bg-white/60 hover:bg-white/80 rounded-lg flex items-center gap-1.5 text-xs text-gray-600 hover:text-gray-800 transition-all backdrop-blur-sm border border-gray-200/30"
              >
                <Copy className="w-3.5 h-3.5" />
                复制
              </button>
            </div>
            <div className="bg-white/60 backdrop-blur-sm border border-gray-200/30 rounded-xl p-4 text-sm text-gray-800 leading-relaxed max-h-40 overflow-y-auto scrollable">
              <pre className="whitespace-pre-wrap font-mono">{prompt.content}</pre>
            </div>
          </div>

          {/* 用户输入 - 简化设计 */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">用户输入</h3>
            <textarea
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入您的具体需求..."
              className="w-full h-32 p-4 bg-white/70 backdrop-blur-sm border border-gray-200/30 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-300 text-sm text-gray-800 placeholder-gray-400 transition-all"
              autoFocus
            />
          </div>
        </div>

        {/* 底部按钮 - 简化为两个按钮 */}
        <div className="flex gap-3 p-6 pt-0">
          <button
            onClick={handleUsePrompt}
            disabled={!userInput.trim() || isSubmitting}
            className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:from-gray-300 disabled:to-gray-400 text-white font-medium py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center gap-2 disabled:cursor-not-allowed text-sm"
          >
            {isSubmitting ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
            {isSubmitting ? "处理中..." : "使用提示词"}
          </button>

          <button
            onClick={handleCopySystemPrompt}
            className="px-6 py-3 bg-white/60 hover:bg-white/80 text-gray-700 hover:text-gray-900 rounded-xl transition-all backdrop-blur-sm text-sm font-medium border border-gray-200/30"
          >
            仅复制
          </button>
        </div>
      </div>
    </div>
  );
}

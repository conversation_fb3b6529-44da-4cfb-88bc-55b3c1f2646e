import { useState, useEffect, useCallback } from "react";
import { Search, X } from "lucide-react";
import { useSearchStore } from "../store/searchStore";
import { useAppStore } from "../store/appStore";
import PromptCard from "./PromptCard";
import { debounce } from "../utils/debounce";

type TabType = 'public' | 'my';

export default function SearchView() {
  const [inputValue, setInputValue] = useState("");
  const [activeTab, setActiveTab] = useState<TabType>('public');
  const { query, results, isLoading, error, search, setQuery, clearResults } = useSearchStore();
  const { setSelectedPrompt } = useAppStore();

  // 防抖搜索
  const debouncedSearch = useCallback(
    debounce((searchQuery: string) => {
      if (searchQuery.trim()) {
        search({
          query: searchQuery.trim(),
          user_only: activeTab === 'my'
        });
      } else {
        clearResults();
      }
    }, 300),
    [search, clearResults, activeTab]
  );

  // 处理输入变化
  const handleInputChange = (value: string) => {
    setInputValue(value);
    setQuery(value);
    debouncedSearch(value);
  };

  // 当标签页切换时重新搜索
  useEffect(() => {
    if (query.trim()) {
      search({
        query: query.trim(),
        user_only: activeTab === 'my'
      });
    }
  }, [activeTab, query, search]);

  // 处理提示词选择
  const handlePromptSelect = (promptId: string) => {
    setSelectedPrompt(promptId);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      // 如果有搜索结果，选择第一个
      if (results.length > 0) {
        handlePromptSelect(results[0].id);
      }
    } else if (e.key === 'Escape') {
      handleClear();
    }
  };

  const handleClear = () => {
    setInputValue('');
    setQuery('');
    clearResults();
  };

  return (
    <div className="space-y-3">
      {/* 搜索框 - 始终显示 */}
      <div className="relative">
        {/* 背景光晕效果 */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 rounded-3xl blur-xl opacity-60"></div>
        
        {/* 主搜索容器 */}
        <div className="relative backdrop-blur-3xl bg-white/10 border border-white/20 rounded-3xl shadow-2xl p-6">
          <div className="relative">
            <svg className="absolute left-5 top-1/2 transform -translate-y-1/2 text-white/70 w-6 h-6 z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <input
              type="text"
              placeholder="搜索你AI提示词，如「GPT提示词」「抖音脚本」「cursor规则」「爆款文案提示词」..."
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-full pl-14 pr-14 py-4 bg-white/15 border border-white/30 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/40 focus:border-white/50 focus:bg-white/20 transition-all duration-300 text-lg backdrop-blur-sm font-medium shadow-inner"
              autoFocus
            />
            {isLoading ? (
              <div className="absolute right-5 top-1/2 transform -translate-y-1/2">
                <div className="w-6 h-6 border-2 border-white/30 border-t-white/80 rounded-full animate-spin"></div>
              </div>
            ) : inputValue && (
              <button
                onClick={handleClear}
                className="absolute right-5 top-1/2 transform -translate-y-1/2 w-6 h-6 text-white/60 hover:text-white/90 transition-all duration-200 hover:scale-110"
              >
                <X className="w-6 h-6" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 标签页 - 只在有搜索时显示 */}
      {query && (
        <div className="relative">
          {/* 标签页背景光晕 */}
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/15 via-purple-500/15 to-pink-500/15 rounded-t-3xl blur-lg opacity-50"></div>
          
          {/* 标签页容器 */}
          <div className="relative backdrop-blur-3xl bg-white/8 border border-white/15 border-b-0 rounded-t-3xl shadow-xl max-w-2xl mx-auto overflow-hidden">
            <div className="flex">
              <button
                onClick={() => setActiveTab('public')}
                className={`flex-1 py-4 px-6 text-base font-semibold text-center transition-all duration-300 relative ${
                  activeTab === 'public'
                    ? 'text-white bg-gradient-to-r from-blue-500/20 to-purple-500/20'
                    : 'text-white/60 hover:text-white/80 hover:bg-white/10'
                }`}
              >
                {activeTab === 'public' && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"></div>
                )}
                公共提示词
              </button>
              <div className="w-px bg-white/20"></div>
              <button
                onClick={() => setActiveTab('my')}
                className={`flex-1 py-4 px-6 text-base font-semibold text-center transition-all duration-300 relative ${
                  activeTab === 'my'
                    ? 'text-white bg-gradient-to-r from-purple-500/20 to-pink-500/20'
                    : 'text-white/60 hover:text-white/80 hover:bg-white/10'
                }`}
              >
                {activeTab === 'my' && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"></div>
                )}
                我的提示词
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 搜索结果窗体 - 只在有搜索时显示 */}
      {query && (
        <div className="relative">
          {/* 结果窗体背景光晕 */}
          <div className="absolute inset-0 bg-gradient-to-b from-purple-500/10 via-blue-500/10 to-indigo-500/10 rounded-b-3xl blur-lg opacity-60"></div>
          
          {/* 结果窗体容器 */}
          <div className="relative backdrop-blur-3xl bg-white/8 border border-white/15 border-t-0 rounded-b-3xl shadow-2xl max-w-2xl mx-auto animate-slide-in">
            <div className="p-6">
              {error && (
                <div className="text-center py-12">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-red-500/20 rounded-full mb-4 backdrop-blur-sm">
                    <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="text-red-400 text-lg font-semibold mb-2">搜索失败</p>
                  <p className="text-white/60 text-sm">{error}</p>
                </div>
              )}

              {!error && !isLoading && results.length === 0 && (
                <div className="text-center py-12">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full mb-4 backdrop-blur-sm">
                    <Search className="w-8 h-8 text-white/60" />
                  </div>
                  <p className="text-white text-lg font-semibold mb-2">未找到相关提示词</p>
                  <p className="text-white/60 text-sm">
                    {activeTab === 'public' ? '尝试使用其他关键词搜索' : '您还没有创建相关的提示词'}
                  </p>
                </div>
              )}

              {isLoading && (
                <div className="text-center py-12">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full mb-4 backdrop-blur-sm">
                    <div className="w-8 h-8 border-2 border-white/30 border-t-white/80 rounded-full animate-spin"></div>
                  </div>
                  <p className="text-white text-lg font-semibold">搜索中...</p>
                  <p className="text-white/60 text-sm mt-1">正在为您查找相关提示词</p>
                </div>
              )}

              {results.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"></div>
                      <h3 className="text-lg font-bold text-white">
                        {activeTab === 'public' ? '公共提示词' : '我的提示词'}
                      </h3>
                      <span className="px-3 py-1 bg-white/15 rounded-full text-white/80 text-sm font-medium backdrop-blur-sm">
                        {results.length}
                      </span>
                    </div>
                    <button
                      onClick={() => {
                        setInputValue('');
                        setQuery('');
                        clearResults();
                      }}
                      className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-xl text-white/70 hover:text-white text-sm font-medium transition-all duration-200 backdrop-blur-sm border border-white/20"
                    >
                      清除搜索
                    </button>
                  </div>
                  <div className="space-y-3 max-h-80 overflow-y-auto custom-scrollbar pr-2">
                    {results.map((prompt) => (
                      <PromptCard
                        key={prompt.id}
                        prompt={prompt}
                        onClick={() => handlePromptSelect(prompt.id)}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 底部提示 - 只在有搜索结果时显示 */}
      {query && results.length > 0 && (
        <div className="text-center">
          <div className="inline-flex items-center space-x-4 px-6 py-3 bg-white/5 rounded-2xl backdrop-blur-sm border border-white/10">
            <div className="flex items-center space-x-2">
              <kbd className="px-2 py-1 bg-white/15 rounded-lg text-white/80 text-xs font-mono">↑↓</kbd>
              <span className="text-white/60 text-sm">导航</span>
            </div>
            <div className="w-px h-4 bg-white/20"></div>
            <div className="flex items-center space-x-2">
              <kbd className="px-2 py-1 bg-white/15 rounded-lg text-white/80 text-xs font-mono">Enter</kbd>
              <span className="text-white/60 text-sm">选择</span>
            </div>
            <div className="w-px h-4 bg-white/20"></div>
            <div className="flex items-center space-x-2">
              <kbd className="px-2 py-1 bg-white/15 rounded-lg text-white/80 text-xs font-mono">Esc</kbd>
              <span className="text-white/60 text-sm">关闭</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

import { useState, useEffect, useCallback } from "react";
import { Search, Loader2 } from "lucide-react";
import { useSearchStore } from "../store/searchStore";
import { useAppStore } from "../store/appStore";
import PromptCard from "./PromptCard";
import { debounce } from "../utils/debounce";

export default function SearchView() {
  const [inputValue, setInputValue] = useState("");
  const { query, results, isLoading, error, search, setQuery, clearResults } = useSearchStore();
  const { setSelectedPrompt } = useAppStore();

  // 防抖搜索
  const debouncedSearch = useCallback(
    debounce((searchQuery: string) => {
      if (searchQuery.trim()) {
        search({ query: searchQuery.trim() });
      } else {
        clearResults();
      }
    }, 300),
    [search, clearResults]
  );

  // 处理输入变化
  const handleInputChange = (value: string) => {
    setInputValue(value);
    setQuery(value);
    debouncedSearch(value);
  };

  // 处理提示词选择
  const handlePromptSelect = (promptId: string) => {
    setSelectedPrompt(promptId);
  };

  // 处理键盘导航
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "ArrowDown" || event.key === "ArrowUp") {
      event.preventDefault();
      // TODO: 实现键盘导航逻辑
    } else if (event.key === "Enter") {
      event.preventDefault();
      // TODO: 选择当前高亮的提示词
    }
  };

  return (
    <div className="space-y-4">
      {/* 搜索框 - 始终显示 */}
      <div className="glass-container rounded-2xl shadow-2xl border border-white/40 p-4 backdrop-blur-xl bg-white/40">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-black w-5 h-5" />
          <input
            type="text"
            placeholder="搜索你AI提示词，如「GPT提示词」「抖音脚本」「cursor规则」「爆款文案提示词」..."
            value={inputValue}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full pl-12 pr-4 py-3 bg-white/40 border border-white/40 rounded-lg text-gray-900 placeholder-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 text-base backdrop-blur-sm font-medium"
            autoFocus
          />
          {isLoading && (
            <Loader2 className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-600 animate-spin" />
          )}
        </div>

        {/* 快捷标签 - 只在没有搜索时显示 */}
        {!query && (
          <div className="mt-3">
            <p className="text-sm text-white font-medium mb-2" style={{textShadow: '1px 1px 2px rgba(0,0,0,0.7)'}}>热门搜索：</p>
            <div className="flex flex-wrap gap-2">
              {['GPT提示词', '抖音脚本', 'Cursor规则', '爆款文案', '产品经理', '营销文案'].map((tag) => (
                <button
                  key={tag}
                  onClick={() => handleInputChange(tag)}
                  className="px-3 py-1.5 bg-white/40 hover:bg-white/50 border border-white/40 rounded-full text-gray-800 hover:text-gray-900 transition-all duration-200 text-sm backdrop-blur-sm font-medium"
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 搜索结果窗体 - 只在有搜索时显示 */}
      {query && (
        <div className="glass-container rounded-2xl shadow-2xl border border-black/20 animate-slide-in backdrop-blur-xl bg-black/60 max-w-2xl mx-auto">
          <div className="p-3">
            {error && (
              <div className="text-center py-8">
                <p className="text-red-400 text-base font-medium">{error}</p>
              </div>
            )}

            {!error && !isLoading && results.length === 0 && (
              <div className="text-center py-8">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-red-500/20 rounded-full mb-3">
                  <Search className="w-6 h-6 text-red-400" />
                </div>
                <p className="text-white text-base font-medium">未找到相关提示词</p>
                <p className="text-gray-300 text-sm mt-1">尝试使用其他关键词搜索</p>
              </div>
            )}

            {isLoading && (
              <div className="text-center py-8">
                <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                <p className="text-white mt-2 text-sm font-medium">搜索中...</p>
              </div>
            )}

            {results.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-base font-semibold text-white">
                    搜索结果 ({results.length})
                  </h3>
                  <button
                    onClick={() => {
                      setInputValue('');
                      setQuery('');
                      clearResults();
                    }}
                    className="text-white/60 hover:text-white/80 text-sm transition-colors"
                  >
                    清除搜索
                  </button>
                </div>
                <div className="space-y-2 max-h-60 overflow-y-auto custom-scrollbar">
                  {results.map((prompt) => (
                    <PromptCard
                      key={prompt.id}
                      prompt={prompt}
                      onClick={() => handlePromptSelect(prompt.id)}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 底部提示 - 只在有搜索结果时显示 */}
      {query && (
        <div className="text-center">
          <p className="text-gray-300 text-sm">
            使用 <span className="text-white font-medium">↑↓</span> 导航，
            <span className="text-white font-medium">Enter</span> 选择，
            <span className="text-white font-medium">Esc</span> 关闭
          </p>
        </div>
      )}
    </div>
  );
}

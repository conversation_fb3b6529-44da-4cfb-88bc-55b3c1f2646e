import { useState, useEffect, useCallback } from "react";
import { Search, X } from "lucide-react";
import { useSearchStore } from "../store/searchStore";
import { useAppStore } from "../store/appStore";
import PromptCard from "./PromptCard";
import { debounce } from "../utils/debounce";

type TabType = 'public' | 'my';

export default function SearchView() {
  const [inputValue, setInputValue] = useState("");
  const [activeTab, setActiveTab] = useState<TabType>('public');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const { query, results, isLoading, error, search, setQuery, clearResults } = useSearchStore();
  const { setSelectedPrompt } = useAppStore();

  // 执行搜索
  const performSearch = useCallback((searchQuery: string) => {
    if (searchQuery.trim()) {
      search({
        q: searchQuery.trim(),
        userOnly: activeTab === 'my',
        page: 1,
        limit: 20
      });
    } else {
      clearResults();
    }
  }, [search, clearResults, activeTab]);

  // 处理输入变化
  const handleInputChange = (value: string) => {
    setInputValue(value);
    setSelectedIndex(-1); // 重置选中索引
    // 不再自动搜索，只在回车时搜索
  };

  // 处理输入框键盘事件
  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      setQuery(inputValue); // 只在搜索时更新 store 中的 query
      performSearch(inputValue);
    }
  };

  // 当标签页切换时重新搜索（只在有结果时）
  useEffect(() => {
    if (inputValue.trim() && results.length > 0) {
      setQuery(inputValue); // 更新 store 中的 query
      performSearch(inputValue);
    }
    setSelectedIndex(-1); // 重置选中索引
  }, [activeTab, performSearch, inputValue, results.length]);

  // 全局键盘事件监听
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K 快捷键聚焦搜索框
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('input[type="text"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, []);

  // 处理提示词选择
  const handlePromptSelect = (promptId: string) => {
    setSelectedPrompt(promptId);
  };

  // 清除搜索
  const handleClear = () => {
    setInputValue('');
    setQuery('');
    setSelectedIndex(-1);
    clearResults();
  };

  // 处理全局键盘事件
  const handleGlobalKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClear();
      return;
    }

    if (results.length === 0) return;

    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev =>
        prev < results.length - 1 ? prev + 1 : 0
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev =>
        prev > 0 ? prev - 1 : results.length - 1
      );
    } else if (e.key === 'Enter' && selectedIndex >= 0) {
      e.preventDefault();
      handlePromptSelect(results[selectedIndex].id);
    } else if (e.key === 'Tab') {
      e.preventDefault();
      setActiveTab(prev => prev === 'public' ? 'my' : 'public');
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto overflow-hidden mt-5">
      {/* 搜索框 - 独立显示 */}
      <div className="mb-4">
        <div className="relative bg-white/95 backdrop-blur-xl border border-gray-200/50 rounded-xl shadow-sm">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="搜索提示词..."
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyDown={handleInputKeyDown}
              className="w-full pl-11 pr-11 py-3 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none text-sm"
              autoFocus
            />
            {isLoading ? (
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <div className="w-4 h-4 border-2 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
              </div>
            ) : inputValue && (
              <button
                onClick={handleClear}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 搜索结果 - 只在有搜索时显示 */}
      {query && (
        <div className="bg-white/95 backdrop-blur-xl border border-gray-200/50 rounded-xl shadow-sm animate-slide-in">
          {/* 标签页 */}
          <div className="border-b border-gray-100">
            <div className="flex">
              <button
                onClick={() => setActiveTab('public')}
                className={`flex-1 py-3 px-4 text-sm font-medium text-center transition-colors relative ${
                  activeTab === 'public'
                    ? 'text-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                公共提示词
                {activeTab === 'public' && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-blue-600 rounded-full"></div>
                )}
              </button>
              <div className="w-px bg-gray-200"></div>
              <button
                onClick={() => setActiveTab('my')}
                className={`flex-1 py-3 px-4 text-sm font-medium text-center transition-colors relative ${
                  activeTab === 'my'
                    ? 'text-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                我的提示词
                {activeTab === 'my' && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-blue-600 rounded-full"></div>
                )}
              </button>
            </div>
          </div>

          {/* 结果内容 */}
          <div className="p-4">
            {error && (
              <div className="text-center py-6">
                <div className="w-10 h-10 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <p className="text-gray-900 text-sm font-medium mb-1">搜索失败</p>
                <p className="text-gray-500 text-xs">{error}</p>
              </div>
            )}

            {!error && !isLoading && results.length === 0 && (
              <div className="text-center py-6">
                <div className="w-10 h-10 bg-gray-50 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Search className="w-5 h-5 text-gray-400" />
                </div>
                <p className="text-gray-900 text-sm font-medium mb-1">未找到相关提示词</p>
                <p className="text-gray-500 text-xs">
                  {activeTab === 'public' ? '尝试使用其他关键词搜索' : '您还没有创建相关的提示词'}
                </p>
              </div>
            )}

            {isLoading && (
              <div className="text-center py-6">
                <div className="w-10 h-10 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-3">
                  <div className="w-5 h-5 border-2 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
                </div>
                <p className="text-gray-900 text-sm font-medium">搜索中...</p>
              </div>
            )}

            {results.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-3">
                  <span className="text-xs text-gray-500">
                    找到 {results.length} 个结果
                  </span>
                  <button
                    onClick={handleClear}
                    className="text-xs text-blue-600 hover:text-blue-700 font-medium"
                  >
                    清除
                  </button>
                </div>
                <div className="space-y-1 max-h-[420px] overflow-y-auto scrollable">
                  {results.map((prompt, index) => (
                    <PromptCard
                      key={prompt.id}
                      prompt={prompt}
                      onClick={() => handlePromptSelect(prompt.id)}
                      isSelected={index === selectedIndex}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>


        </div>
      )}
    </div>
  );
}

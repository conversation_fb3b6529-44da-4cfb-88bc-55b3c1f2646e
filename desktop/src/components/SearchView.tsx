import { useState, useEffect, useCallback } from "react";
import { Search, X } from "lucide-react";
import { useSearchStore } from "../store/searchStore";
import { useAppStore } from "../store/appStore";
import PromptCard from "./PromptCard";
import { debounce } from "../utils/debounce";

type TabType = 'public' | 'my';

export default function SearchView() {
  const [inputValue, setInputValue] = useState("");
  const [activeTab, setActiveTab] = useState<TabType>('public');
  const { query, results, isLoading, error, search, setQuery, clearResults } = useSearchStore();
  const { setSelectedPrompt } = useAppStore();

  // 防抖搜索
  const debouncedSearch = useCallback(
    debounce((searchQuery: string) => {
      if (searchQuery.trim()) {
        search({
          query: searchQuery.trim(),
          user_only: activeTab === 'my'
        });
      } else {
        clearResults();
      }
    }, 300),
    [search, clearResults, activeTab]
  );

  // 处理输入变化
  const handleInputChange = (value: string) => {
    setInputValue(value);
    setQuery(value);
    debouncedSearch(value);
  };

  // 当标签页切换时重新搜索
  useEffect(() => {
    if (query.trim()) {
      search({
        query: query.trim(),
        user_only: activeTab === 'my'
      });
    }
  }, [activeTab, query, search]);

  // 处理提示词选择
  const handlePromptSelect = (promptId: string) => {
    setSelectedPrompt(promptId);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      // 如果有搜索结果，选择第一个
      if (results.length > 0) {
        handlePromptSelect(results[0].id);
      }
    } else if (e.key === 'Escape') {
      handleClear();
    }
  };

  const handleClear = () => {
    setInputValue('');
    setQuery('');
    clearResults();
  };

  return (
    <div className="space-y-3">
      {/* 搜索框 - 始终显示 */}
      <div className="relative">
        {/* 主搜索容器 - 苹果简约风格 */}
        <div className="relative backdrop-blur-xl bg-white/80 border border-black/10 rounded-2xl shadow-lg p-4">
          <div className="relative">
            <svg className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5 z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <input
              type="text"
              placeholder="搜索你AI提示词，如「GPT提示词」「抖音脚本」「cursor规则」「爆款文案提示词」..."
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-full pl-12 pr-12 py-3 bg-transparent border-0 text-gray-900 placeholder-gray-500 focus:outline-none text-base font-medium"
              autoFocus
            />
            {isLoading ? (
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <div className="w-5 h-5 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
              </div>
            ) : inputValue && (
              <button
                onClick={handleClear}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <X className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 标签页 - 只在有搜索时显示 */}
      {query && (
        <div className="relative">
          {/* 标签页容器 - 苹果简约风格 */}
          <div className="relative backdrop-blur-xl bg-white/80 border border-black/10 border-b-0 rounded-t-2xl shadow-sm max-w-2xl mx-auto overflow-hidden">
            <div className="flex">
              <button
                onClick={() => setActiveTab('public')}
                className={`flex-1 py-3 px-6 text-sm font-medium text-center transition-all duration-200 relative ${
                  activeTab === 'public'
                    ? 'text-blue-600 bg-blue-50/50'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50/50'
                }`}
              >
                {activeTab === 'public' && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-blue-600 rounded-full"></div>
                )}
                公共提示词
              </button>
              <div className="w-px bg-gray-200"></div>
              <button
                onClick={() => setActiveTab('my')}
                className={`flex-1 py-3 px-6 text-sm font-medium text-center transition-all duration-200 relative ${
                  activeTab === 'my'
                    ? 'text-blue-600 bg-blue-50/50'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50/50'
                }`}
              >
                {activeTab === 'my' && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-blue-600 rounded-full"></div>
                )}
                我的提示词
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 搜索结果窗体 - 只在有搜索时显示 */}
      {query && (
        <div className="relative">
          {/* 结果窗体容器 - 苹果简约风格 */}
          <div className="relative backdrop-blur-xl bg-white/80 border border-black/10 border-t-0 rounded-b-2xl shadow-sm max-w-2xl mx-auto">
            <div className="p-4">
              {error && (
                <div className="text-center py-8">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-red-50 rounded-full mb-3">
                    <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="text-gray-900 text-base font-medium mb-1">搜索失败</p>
                  <p className="text-gray-500 text-sm">{error}</p>
                </div>
              )}

              {!error && !isLoading && results.length === 0 && (
                <div className="text-center py-8">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-gray-50 rounded-full mb-3">
                    <Search className="w-6 h-6 text-gray-400" />
                  </div>
                  <p className="text-gray-900 text-base font-medium mb-1">未找到相关提示词</p>
                  <p className="text-gray-500 text-sm">
                    {activeTab === 'public' ? '尝试使用其他关键词搜索' : '您还没有创建相关的提示词'}
                  </p>
                </div>
              )}

              {isLoading && (
                <div className="text-center py-8">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-50 rounded-full mb-3">
                    <div className="w-6 h-6 border-2 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
                  </div>
                  <p className="text-gray-900 text-base font-medium">搜索中...</p>
                  <p className="text-gray-500 text-sm mt-1">正在为您查找相关提示词</p>
                </div>
              )}

              {results.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <h3 className="text-base font-semibold text-gray-900">
                        {activeTab === 'public' ? '公共提示词' : '我的提示词'}
                      </h3>
                      <span className="px-2 py-1 bg-gray-100 rounded-full text-gray-600 text-xs font-medium">
                        {results.length}
                      </span>
                    </div>
                    <button
                      onClick={() => {
                        setInputValue('');
                        setQuery('');
                        clearResults();
                      }}
                      className="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors duration-200"
                    >
                      清除搜索
                    </button>
                  </div>
                  <div className="space-y-2 max-h-80 overflow-y-auto pr-1">
                    {results.map((prompt) => (
                      <PromptCard
                        key={prompt.id}
                        prompt={prompt}
                        onClick={() => handlePromptSelect(prompt.id)}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 底部提示 - 只在有搜索结果时显示 */}
      {query && results.length > 0 && (
        <div className="text-center">
          <div className="inline-flex items-center space-x-3 px-4 py-2 bg-gray-50 rounded-xl border border-gray-200">
            <div className="flex items-center space-x-1.5">
              <kbd className="px-1.5 py-0.5 bg-white rounded text-gray-600 text-xs font-mono border border-gray-200">↑↓</kbd>
              <span className="text-gray-500 text-xs">导航</span>
            </div>
            <div className="w-px h-3 bg-gray-300"></div>
            <div className="flex items-center space-x-1.5">
              <kbd className="px-1.5 py-0.5 bg-white rounded text-gray-600 text-xs font-mono border border-gray-200">Enter</kbd>
              <span className="text-gray-500 text-xs">选择</span>
            </div>
            <div className="w-px h-3 bg-gray-300"></div>
            <div className="flex items-center space-x-1.5">
              <kbd className="px-1.5 py-0.5 bg-white rounded text-gray-600 text-xs font-mono border border-gray-200">Esc</kbd>
              <span className="text-gray-500 text-xs">关闭</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

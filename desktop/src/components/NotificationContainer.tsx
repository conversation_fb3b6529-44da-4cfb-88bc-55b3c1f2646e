import { useEffect } from "react";
import { X, Check<PERSON>ircle, XCircle, AlertTriangle, Info } from "lucide-react";
import { useNotificationStore, Notification, NotificationType } from "../store/notificationStore";

const NotificationIcon = ({ type }: { type: NotificationType }) => {
  const iconProps = { className: "w-5 h-5" };
  
  switch (type) {
    case 'success':
      return <CheckCircle {...iconProps} className="w-5 h-5 text-green-600" />;
    case 'error':
      return <XCircle {...iconProps} className="w-5 h-5 text-red-600" />;
    case 'warning':
      return <AlertTriangle {...iconProps} className="w-5 h-5 text-yellow-600" />;
    case 'info':
      return <Info {...iconProps} className="w-5 h-5 text-blue-600" />;
    default:
      return <Info {...iconProps} className="w-5 h-5 text-gray-600" />;
  }
};

const NotificationItem = ({ notification }: { notification: Notification }) => {
  const { removeNotification } = useNotificationStore();

  const getNotificationStyles = (type: NotificationType) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  return (
    <div
      className={`
        flex items-start space-x-3 p-4 rounded-lg border shadow-sm
        ${getNotificationStyles(notification.type)}
        animate-in slide-in-from-right-full duration-300
      `}
    >
      <NotificationIcon type={notification.type} />
      
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium">{notification.title}</h4>
        {notification.message && (
          <p className="text-sm opacity-90 mt-1">{notification.message}</p>
        )}
        {notification.action && (
          <button
            onClick={notification.action.onClick}
            className="text-sm font-medium underline mt-2 hover:no-underline"
          >
            {notification.action.label}
          </button>
        )}
      </div>

      <button
        onClick={() => removeNotification(notification.id)}
        className="flex-shrink-0 p-1 rounded-md hover:bg-black/10 transition-colors"
      >
        <X className="w-4 h-4" />
      </button>
    </div>
  );
};

export default function NotificationContainer() {
  const { notifications } = useNotificationStore();

  // 限制最大通知数量
  useEffect(() => {
    if (notifications.length > 5) {
      const { removeNotification } = useNotificationStore.getState();
      const oldestNotification = notifications[0];
      removeNotification(oldestNotification.id);
    }
  }, [notifications]);

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 space-y-2 max-w-sm w-full">
      {notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
        />
      ))}
    </div>
  );
}

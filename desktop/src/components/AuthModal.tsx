import { useState } from "react";
import LoginModal from "./LoginModal";
import RegisterModal from "./RegisterModal";

type AuthMode = 'login' | 'register';

interface AuthModalProps {
  initialMode?: AuthMode;
  onClose?: () => void;
}

export default function AuthModal({ initialMode = 'login', onClose }: AuthModalProps) {
  const [mode, setMode] = useState<AuthMode>(initialMode);

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  const switchToLogin = () => setMode('login');
  const switchToRegister = () => setMode('register');

  if (mode === 'register') {
    return (
      <RegisterModal 
        onClose={handleClose}
        onSwitchToLogin={switchToLogin}
      />
    );
  }

  return (
    <LoginModal 
      onSwitchToRegister={switchToRegister}
    />
  );
}

import { useEffect, useState } from "react";
import { useAppStore } from "../store/appStore";
import SearchView from "./SearchView";
import PromptDetailView from "./PromptDetailView";
import PromptListView from "./PromptListView";
import LoginView from "./LoginView";
import { Sparkles, Search, List } from 'lucide-react';
import { listen } from '@tauri-apps/api/event';
import { invoke } from '@tauri-apps/api/core';
import { getCurrentWindow } from '@tauri-apps/api/window';
import { LogicalSize } from '@tauri-apps/api/window';

export default function CommandPalette() {
  const { hideApp, selectedPromptId, setSelectedPrompt, currentPage, isAuthenticated, logout, setCurrentPage } = useAppStore();
  const [showPromptDetail, setShowPromptDetail] = useState(false);
  const [activeTab, setActiveTab] = useState<'search' | 'browse'>('search');

  // 窗口大小调整函数
  const resizeWindow = async (width: number, height: number) => {
    try {
      const currentWindow = getCurrentWindow();
      console.log(`🪟 Resizing window to ${width}x${height}`);

      await currentWindow.setSize(new LogicalSize(width, height));
      await currentWindow.center();

      console.log(`✅ Window successfully resized to ${width}x${height}`);
    } catch (error) {
      console.error('❌ Failed to resize window:', error);
    }
  };

  // 监听选中的提示词变化
  useEffect(() => {
    if (selectedPromptId) {
      setShowPromptDetail(true);
    }
  }, [selectedPromptId]);

  // 监听ESC键关闭应用
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        if (showPromptDetail) {
          // 如果详情窗体打开，先关闭详情窗体
          handleClosePromptDetail();
        } else {
          // 否则关闭整个应用
          hideApp();
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [hideApp, showPromptDetail]);

  // 监听托盘菜单事件
  useEffect(() => {
    const setupEventListeners = async () => {
      // 监听退出登录事件
      const unlistenLogout = await listen('logout-requested', () => {
        // 执行退出登录逻辑
        console.log('Logout requested from tray menu');
        logout();
      });

      // 监听导航到设置页面事件
      const unlistenSettings = await listen('navigate-to-settings', () => {
        // 这里可以添加导航到设置页面的逻辑
        console.log('Navigate to settings requested from tray menu');
        setCurrentPage('settings');
      });

      return () => {
        unlistenLogout();
        unlistenSettings();
      };
    };

    setupEventListeners();
  }, []);

  // 组件挂载时立即设置登录窗口大小
  useEffect(() => {
    const initializeOnMount = async () => {
      console.log('🎯 Component mounted, setting initial login window size');
      await resizeWindow(400, 500); // 直接设置登录窗口大小
      await setCurrentPage('login');
    };

    initializeOnMount();
  }, []); // 只在组件挂载时执行一次

  // 初始化窗口大小
  useEffect(() => {
    const initializeWindow = async () => {
      console.log('🚀 Initializing window, isAuthenticated:', isAuthenticated);
      if (!isAuthenticated) {
        console.log('📱 Setting login mode');
        await resizeWindow(400, 500); // 登录窗口
        await setCurrentPage('login');
      } else {
        console.log('🖥️ Setting main mode');
        await resizeWindow(900, 800); // 主应用窗口
        await setCurrentPage('main');
      }
    };

    initializeWindow();
  }, [isAuthenticated, setCurrentPage]);

  // 关闭提示词详情
  const handleClosePromptDetail = () => {
    setShowPromptDetail(false);
    setSelectedPrompt(null);
  };

  // 处理提示词选择
  const handlePromptSelect = (promptId: string) => {
    setSelectedPrompt(promptId);
  };

  // 根据当前页面渲染不同内容
  const renderContent = () => {
    switch (currentPage) {
      case 'login':
        return <LoginView />;

      case 'main':
        return (
          <div className="min-h-screen p-6 bg-transparent relative">
            <div className="w-full max-w-4xl mx-auto">
              {/* 标题区域 - 使用与web端完全一致的样式 */}
              <div className="flex items-center justify-center mb-6 pt-6">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-10 h-10 bg-gradient-to-br from-violet-600 to-cyan-600 rounded-xl flex items-center justify-center">
                      <Sparkles className="w-6 h-6 text-white" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                  </div>
                  <div>
                    <span className="text-2xl font-bold bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent">
                      PromptHub
                    </span>
                    <div className="text-xs text-white font-medium" style={{textShadow: '1px 1px 3px rgba(0,0,0,0.7)'}}>AI提示词分享、创作平台</div>
                  </div>
                </div>
              </div>

              {/* 搜索视图 - 始终显示 */}
              <div className="fade-in">
                <SearchView />
              </div>

              {/* 提示词详情浮层 */}
              {showPromptDetail && selectedPromptId && (
                <PromptDetailView onClose={handleClosePromptDetail} />
              )}
            </div>
          </div>
        );

      case 'settings':
        return (
          <div className="min-h-screen p-6 bg-transparent relative">
            <div className="w-full max-w-2xl mx-auto">
              <div className="text-center py-20">
                <h1 className="text-2xl font-bold text-gray-800 mb-4">设置页面</h1>
                <p className="text-gray-600">设置功能正在开发中...</p>
                <button
                  onClick={() => setCurrentPage('main')}
                  className="mt-4 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
                >
                  返回主页
                </button>
              </div>
            </div>
          </div>
        );

      default:
        return <LoginView />;
    }
  };

  return renderContent();
}

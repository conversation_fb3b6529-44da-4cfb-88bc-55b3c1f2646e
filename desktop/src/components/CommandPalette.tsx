import { useEffect, useState } from "react";
import { useAppStore } from "../store/appStore";
import SearchView from "./SearchView";
import PromptDetailView from "./PromptDetailView";
import { Sparkles } from 'lucide-react';

export default function CommandPalette() {
  const { hideApp, selectedPromptId, setSelectedPrompt } = useAppStore();
  const [showPromptDetail, setShowPromptDetail] = useState(false);

  // 监听选中的提示词变化
  useEffect(() => {
    if (selectedPromptId) {
      setShowPromptDetail(true);
    }
  }, [selectedPromptId]);

  // 监听ESC键关闭应用
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        if (showPromptDetail) {
          // 如果详情窗体打开，先关闭详情窗体
          handleClosePromptDetail();
        } else {
          // 否则关闭整个应用
          hideApp();
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [hideApp, showPromptDetail]);

  // 关闭提示词详情
  const handleClosePromptDetail = () => {
    setShowPromptDetail(false);
    setSelectedPrompt(null);
  };

  return (
    <div className="min-h-screen p-6 bg-transparent">
      <div className="w-full max-w-4xl mx-auto">
        {/* 标题区域 - 使用与web端完全一致的样式 */}
        <div className="flex items-center justify-center mb-6 pt-6">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-violet-600 to-cyan-600 rounded-xl flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
            </div>
            <div>
              <span className="text-2xl font-bold bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent">
                PromptHub
              </span>
              <div className="text-xs text-white font-medium" style={{textShadow: '1px 1px 3px rgba(0,0,0,0.7)'}}>AI提示词分享、创作平台</div>
            </div>
          </div>
        </div>

        {/* 搜索视图 - 始终显示 */}
        <div className="fade-in">
          <SearchView />
        </div>

        {/* 提示词详情浮层 */}
        {showPromptDetail && selectedPromptId && (
          <PromptDetailView onClose={handleClosePromptDetail} />
        )}
      </div>
    </div>
  );
}

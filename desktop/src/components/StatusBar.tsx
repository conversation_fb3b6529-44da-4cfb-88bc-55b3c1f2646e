import { useState } from "react";
import { Settings, LogOut, User, Minimize2, Info, ChevronDown } from "lucide-react";
import { useAuthStore } from "../store/authStore";
import { useAppStore } from "../store/appStore";
import { useNotificationStore } from "../store/notificationStore";

export default function StatusBar() {
  const [showMenu, setShowMenu] = useState(false);
  const { user, logout } = useAuthStore();
  const { hideApp } = useAppStore();
  const { showSuccess } = useNotificationStore();

  const handleLogout = async () => {
    try {
      await logout();
      showSuccess("退出成功", "已安全退出登录");
      setShowMenu(false);
    } catch (error) {
      console.error("退出登录失败:", error);
    }
  };

  const handleHideApp = () => {
    hideApp();
    setShowMenu(false);
  };

  const handleToggleMenu = () => {
    setShowMenu(!showMenu);
  };

  return (
    <div className="fixed top-3 right-3 z-50">
      <div className="relative">
        {/* macOS 风格状态栏按钮 */}
        <button
          onClick={handleToggleMenu}
          className="flex items-center gap-1 px-2 py-1 bg-black/20 backdrop-blur-md rounded-md hover:bg-black/30 transition-all duration-200"
        >
          <div className="w-5 h-5 bg-gradient-to-br from-violet-500 to-cyan-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">P</span>
          </div>
          <ChevronDown className="w-3 h-3 text-white/80" />
        </button>

        {/* macOS 风格下拉菜单 */}
        {showMenu && (
          <div className="absolute top-full right-0 mt-1 w-52 bg-white/95 backdrop-blur-xl border border-gray-200/30 rounded-lg shadow-xl overflow-hidden">
            {/* 用户信息 */}
            <div className="px-3 py-2 border-b border-gray-100/50">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-gradient-to-br from-violet-500 to-cyan-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">
                    {user?.username?.charAt(0).toUpperCase() || "U"}
                  </span>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {user?.username || "用户"}
                  </div>
                  <div className="text-xs text-gray-500">
                    {user?.email || "未设置邮箱"}
                  </div>
                </div>
              </div>
            </div>

            {/* 菜单项 */}
            <div className="py-1">
              <button
                onClick={handleHideApp}
                className="w-full flex items-center justify-between px-3 py-1.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
              >
                <div className="flex items-center gap-2">
                  <Minimize2 className="w-4 h-4" />
                  隐藏窗口
                </div>
                <span className="text-xs text-gray-400 font-mono">⌘⇧P</span>
              </button>

              <button
                onClick={() => {
                  window.open('https://prompthub.xin', '_blank');
                  setShowMenu(false);
                }}
                className="w-full flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
              >
                <Info className="w-4 h-4" />
                访问网站
              </button>

              <button
                onClick={() => {
                  // 可以添加设置功能
                  setShowMenu(false);
                }}
                className="w-full flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
              >
                <Settings className="w-4 h-4" />
                设置
              </button>

              <div className="border-t border-gray-100/50 my-1"></div>

              <button
                onClick={handleLogout}
                className="w-full flex items-center gap-2 px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 transition-colors"
              >
                <LogOut className="w-4 h-4" />
                退出登录
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 点击外部关闭菜单 */}
      {showMenu && (
        <div
          className="fixed inset-0 z-[-1]"
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  );
}

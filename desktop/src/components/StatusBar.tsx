import { useState } from "react";
import { Settings, LogOut, Eye, EyeOff, User, Minimize2, Info } from "lucide-react";
import { useAuthStore } from "../store/authStore";
import { useAppStore } from "../store/appStore";
import { useNotificationStore } from "../store/notificationStore";

export default function StatusBar() {
  const [showMenu, setShowMenu] = useState(false);
  const { user, logout } = useAuthStore();
  const { hideApp } = useAppStore();
  const { showSuccess } = useNotificationStore();

  const handleLogout = async () => {
    try {
      await logout();
      showSuccess("退出成功", "已安全退出登录");
      setShowMenu(false);
    } catch (error) {
      console.error("退出登录失败:", error);
    }
  };

  const handleHideApp = () => {
    hideApp();
    setShowMenu(false);
  };

  const handleToggleMenu = () => {
    setShowMenu(!showMenu);
  };

  return (
    <div className="fixed top-4 right-4 z-50">
      <div className="relative">
        {/* 状态栏按钮 */}
        <button
          onClick={handleToggleMenu}
          className="flex items-center gap-2 px-3 py-2 bg-white/90 backdrop-blur-xl border border-gray-200/50 rounded-lg shadow-sm hover:bg-white/95 transition-all duration-200"
        >
          <User className="w-4 h-4 text-gray-600" />
          <span className="text-sm text-gray-700 font-medium">
            {user?.username || "用户"}
          </span>
          <Settings className="w-4 h-4 text-gray-500" />
        </button>

        {/* 下拉菜单 */}
        {showMenu && (
          <div className="absolute top-full right-0 mt-2 w-48 bg-white/95 backdrop-blur-xl border border-gray-200/50 rounded-lg shadow-lg overflow-hidden">
            {/* 用户信息 */}
            <div className="px-4 py-3 border-b border-gray-100">
              <div className="text-sm font-medium text-gray-900">
                {user?.username || "用户"}
              </div>
              <div className="text-xs text-gray-500">
                {user?.email || "未设置邮箱"}
              </div>
            </div>

            {/* 菜单项 */}
            <div className="py-1">
              <button
                onClick={handleHideApp}
                className="w-full flex items-center justify-between px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <Minimize2 className="w-4 h-4" />
                  隐藏窗口
                </div>
                <span className="text-xs text-gray-400">Cmd+Shift+P</span>
              </button>

              <button
                onClick={() => {
                  // 可以添加设置功能
                  setShowMenu(false);
                }}
                className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <Settings className="w-4 h-4" />
                设置
              </button>

              <button
                onClick={() => {
                  window.open('https://prompthub.xin', '_blank');
                  setShowMenu(false);
                }}
                className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <Info className="w-4 h-4" />
                访问网站
              </button>

              <div className="border-t border-gray-100 my-1"></div>

              <button
                onClick={handleLogout}
                className="w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
              >
                <LogOut className="w-4 h-4" />
                退出登录
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 点击外部关闭菜单 */}
      {showMenu && (
        <div
          className="fixed inset-0 z-[-1]"
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  );
}

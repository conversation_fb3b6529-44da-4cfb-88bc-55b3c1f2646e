import { useState } from "react";
import { <PERSON>ader2, Eye, EyeOff } from "lucide-react";
import { useAuthStore } from "../store/authStore";
import { useNotificationStore } from "../store/notificationStore";

interface LoginModalProps {
  onSwitchToRegister?: () => void;
}

export default function LoginModal({ onSwitchToRegister }: LoginModalProps) {
  const [emailOrUsername, setEmailOrUsername] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading, error, clearError } = useAuthStore();
  const { showSuccess, showError } = useNotificationStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("🚀 Form submitted with:", { emailOrUsername, password });

    if (emailOrUsername && password) {
      console.log("✅ Validation passed, calling login...");
      const success = await login({ identifier: emailOrUsername, password });
      console.log("🎯 Login result:", success);

      if (success) {
        showSuccess("登录成功", "欢迎回来！");
      }
    } else {
      console.log("❌ Validation failed:", { emailOrUsername, password });
    }
  };

  const handleInputChange = () => {
    if (error) {
      clearError();
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="w-full max-w-md p-6 bg-white/95 backdrop-blur-xl border border-gray-200/50 rounded-xl shadow-lg">
        {/* 标题 */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-foreground">PromptHub</h1>
          <p className="text-muted-foreground mt-2">登录您的账户</p>
        </div>

        {/* 登录表单 */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 邮箱或用户名输入 */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              邮箱或用户名
            </label>
            <input
              type="text"
              value={emailOrUsername}
              onChange={(e) => {
                setEmailOrUsername(e.target.value);
                handleInputChange();
              }}
              className="w-full px-3 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              placeholder="输入您的邮箱或用户名"
              required
            />
          </div>

          {/* 密码输入 */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              密码
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  handleInputChange();
                }}
                className="w-full px-3 py-2 pr-10 bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                placeholder="输入您的密码"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                {showPassword ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
              {error}
            </div>
          )}

          {/* 登录按钮 */}
          <button
            type="submit"
            disabled={isLoading || !emailOrUsername || !password}
            className="w-full flex items-center justify-center gap-2 bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : null}
            {isLoading ? "登录中..." : "登录"}
          </button>
        </form>

        {/* 切换到注册 */}
        {onSwitchToRegister && (
          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground">
              还没有账户？{" "}
              <button
                onClick={onSwitchToRegister}
                className="text-primary hover:underline"
              >
                立即注册
              </button>
            </p>
          </div>
        )}

        {/* 底部提示 */}
        <div className="mt-4 text-center text-xs text-muted-foreground">
          <p>使用您的 PromptHub 账户登录</p>
        </div>
      </div>
    </div>
  );
}

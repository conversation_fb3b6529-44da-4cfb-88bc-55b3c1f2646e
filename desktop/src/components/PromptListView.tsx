import { <PERSON>, Copy, Eye, Calendar, User } from "lucide-react";
import { apiServices, Prompt } from "../lib/api-services";
import { usePagination } from "../hooks/usePagination";

interface PromptListViewProps {
  onPromptSelect: (promptId: string) => void;
}

export default function PromptListView({ onPromptSelect }: PromptListViewProps) {
  const {
    items: prompts,
    isLoading,
    isLoadingMore,
    error,
    hasMore,
    total,
    loadMore,
    refresh,
    setError,
  } = usePagination(
    (params) => apiServices.prompt.getPublicPrompts({
      ...params,
      sortBy: 'createdAt'
    }),
    {
      initialPageSize: 20,
      maxItems: 500, // 限制最大加载数量
    }
  );

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // 截断文本
  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (error && prompts.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={refresh}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 标题 */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-foreground">热门提示词</h2>
        <span className="text-sm text-muted-foreground">
          {total > 0 ? `共 ${total} 个提示词` : `${prompts.length} 个提示词`}
        </span>
      </div>

      {/* 提示词列表 */}
      <div className="grid gap-4">
        {prompts.map((prompt) => (
          <div
            key={prompt.id}
            onClick={() => onPromptSelect(prompt.id)}
            className="p-4 bg-card border border-border rounded-lg hover:shadow-md transition-shadow cursor-pointer"
          >
            {/* 标题和作者 */}
            <div className="flex items-start justify-between mb-2">
              <h3 className="font-medium text-foreground line-clamp-1">
                {prompt.title}
              </h3>
              <div className="flex items-center text-xs text-muted-foreground ml-2">
                <User className="w-3 h-3 mr-1" />
                {prompt.authorName || '匿名'}
              </div>
            </div>

            {/* 描述 */}
            {prompt.description && (
              <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                {truncateText(prompt.description)}
              </p>
            )}

            {/* 内容预览 */}
            <div className="bg-muted/50 rounded-md p-3 mb-3">
              <p className="text-xs text-muted-foreground font-mono line-clamp-3">
                {truncateText(prompt.content, 150)}
              </p>
            </div>

            {/* 标签 */}
            {prompt.tags && prompt.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {prompt.tags.slice(0, 3).map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-md"
                  >
                    {tag}
                  </span>
                ))}
                {prompt.tags.length > 3 && (
                  <span className="text-xs text-muted-foreground">
                    +{prompt.tags.length - 3}
                  </span>
                )}
              </div>
            )}

            {/* 统计信息 */}
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <Heart className="w-3 h-3 mr-1" />
                  {prompt.likes || 0}
                </div>
                <div className="flex items-center">
                  <Copy className="w-3 h-3 mr-1" />
                  {prompt.downloads || 0}
                </div>
                <div className="flex items-center">
                  <Eye className="w-3 h-3 mr-1" />
                  {prompt.views || 0}
                </div>
              </div>
              <div className="flex items-center">
                <Calendar className="w-3 h-3 mr-1" />
                {formatDate(prompt.createdAt)}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 加载更多按钮 */}
      {hasMore && (
        <div className="text-center pt-4">
          <button
            onClick={loadMore}
            disabled={isLoadingMore}
            className="px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoadingMore ? '加载中...' : '加载更多'}
          </button>
        </div>
      )}

      {/* 错误提示 */}
      {error && prompts.length > 0 && (
        <div className="text-center pt-4">
          <p className="text-red-600 text-sm mb-2">{error}</p>
          <button
            onClick={() => setError(null)}
            className="text-sm text-primary hover:underline"
          >
            忽略错误
          </button>
        </div>
      )}

      {/* 空状态 */}
      {!isLoading && prompts.length === 0 && !error && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">暂无提示词</p>
        </div>
      )}

      {/* 初始加载状态 */}
      {isLoading && prompts.length === 0 && (
        <div className="text-center py-12">
          <div className="inline-flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
            <span className="text-muted-foreground">加载中...</span>
          </div>
        </div>
      )}
    </div>
  );
}

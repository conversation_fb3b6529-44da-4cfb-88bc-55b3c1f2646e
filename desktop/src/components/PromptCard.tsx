import { Heart, Download, Eye, Tag } from "lucide-react";
import { Prompt } from "../types";

interface PromptCardProps {
  prompt: Prompt;
  onClick: () => void;
}

export default function PromptCard({ prompt, onClick }: PromptCardProps) {
  return (
    <div
      className="group cursor-pointer transition-all duration-200 hover:scale-[1.01] active:scale-[0.99]"
      onClick={onClick}
    >
      {/* 主卡片容器 - 苹果简约风格 */}
      <div className="relative backdrop-blur-xl bg-white/90 border border-black/5 rounded-xl p-4 shadow-sm group-hover:bg-white/95 group-hover:shadow-md transition-all duration-200">
        {/* 标题和分类 */}
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-gray-900 text-base line-clamp-1 flex-1 mr-3">
            {prompt.title}
          </h3>
          <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-md text-xs font-medium flex-shrink-0">
            {prompt.category}
          </span>
        </div>

        {/* 描述 */}
        {prompt.description && (
          <p className="text-gray-600 mb-3 line-clamp-2 leading-relaxed text-sm">
            {prompt.description}
          </p>
        )}

        {/* 标签 */}
        {prompt.tags && prompt.tags.length > 0 && (
          <div className="flex items-center gap-1.5 mb-3 flex-wrap">
            {prompt.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="bg-gray-50 text-gray-500 px-2 py-0.5 rounded text-xs"
              >
                {tag}
              </span>
            ))}
            {prompt.tags.length > 3 && (
              <span className="text-xs text-gray-400">
                +{prompt.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* 统计信息 */}
        <div className="flex items-center gap-4 text-xs text-gray-400">
          <div className="flex items-center gap-1">
            <Heart className="w-3 h-3" />
            <span>{prompt.likes || 0}</span>
          </div>
          <div className="flex items-center gap-1">
            <Download className="w-3 h-3" />
            <span>{prompt.downloads || 0}</span>
          </div>
          <div className="flex items-center gap-1">
            <Eye className="w-3 h-3" />
            <span>{prompt.views || 0}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

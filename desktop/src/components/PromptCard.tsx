import { Heart, Download, Eye, Tag } from "lucide-react";
import { Prompt } from "../types";

interface PromptCardProps {
  prompt: Prompt;
  onClick: () => void;
}

export default function PromptCard({ prompt, onClick }: PromptCardProps) {
  return (
    <div
      className="relative group cursor-pointer transition-all duration-300 hover:scale-[1.02]"
      onClick={onClick}
    >
      {/* 背景光晕效果 */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

      {/* 主卡片容器 */}
      <div className="relative backdrop-blur-2xl bg-white/8 hover:bg-white/12 border border-white/15 hover:border-white/25 rounded-2xl p-5 shadow-xl transition-all duration-300">
        {/* 标题和分类 */}
        <div className="flex items-start justify-between mb-3">
          <h3 className="font-bold text-white text-lg line-clamp-1 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-blue-400 group-hover:to-purple-400 group-hover:bg-clip-text transition-all duration-300">
            {prompt.title}
          </h3>
          <span className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-300 px-3 py-1.5 rounded-xl text-xs font-semibold ml-3 flex-shrink-0 backdrop-blur-sm border border-blue-400/20">
            {prompt.category}
          </span>
        </div>

        {/* 描述 */}
        {prompt.description && (
          <p className="text-white/70 mb-4 line-clamp-2 leading-relaxed text-sm">
            {prompt.description}
          </p>
        )}

        {/* 标签 */}
        {prompt.tags.length > 0 && (
          <div className="flex items-center gap-2 mb-4 flex-wrap">
            <Tag className="w-4 h-4 text-white/50" />
            {prompt.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="bg-white/10 text-white/80 px-2.5 py-1 rounded-lg text-xs font-medium backdrop-blur-sm border border-white/10"
              >
                {tag}
              </span>
            ))}
            {prompt.tags.length > 3 && (
              <span className="text-xs text-white/50 font-medium">
                +{prompt.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* 统计信息 */}
        <div className="flex items-center gap-6 text-sm text-white/60">
          <div className="flex items-center gap-2 group/stat hover:text-pink-400 transition-colors">
            <Heart className="w-4 h-4" />
            <span className="font-semibold">{prompt.likes}</span>
          </div>
          <div className="flex items-center gap-2 group/stat hover:text-blue-400 transition-colors">
            <Download className="w-4 h-4" />
            <span className="font-semibold">{prompt.downloads}</span>
          </div>
          <div className="flex items-center gap-2 group/stat hover:text-green-400 transition-colors">
            <Eye className="w-4 h-4" />
            <span className="font-semibold">{prompt.views}</span>
          </div>
        </div>

        {/* 悬停时的装饰元素 */}
        <div className="absolute top-3 right-3 w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>
    </div>
  );
}

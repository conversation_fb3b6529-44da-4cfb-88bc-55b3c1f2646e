import { Heart, Download, <PERSON> } from "lucide-react";
import { Prompt } from "../types";

interface PromptCardProps {
  prompt: Prompt;
  onClick: () => void;
  isSelected?: boolean;
}

export default function PromptCard({ prompt, onClick, isSelected = false }: PromptCardProps) {
  return (
    <div
      className={`group cursor-pointer transition-all duration-150 rounded-lg p-3 -mx-1 ${
        isSelected
          ? 'bg-blue-50 border border-blue-200'
          : 'hover:bg-gray-50/80'
      }`}
      onClick={onClick}
    >
      <div className="flex items-start justify-between mb-2 min-w-0">
        <h3 className="font-medium text-gray-900 text-sm line-clamp-1 flex-1 mr-3 group-hover:text-blue-600 transition-colors truncate">
          {prompt.title}
        </h3>
        <span className="bg-gray-100 text-gray-500 px-2 py-0.5 rounded text-xs font-medium flex-shrink-0 whitespace-nowrap">
          {prompt.category}
        </span>
      </div>

      {/* 描述 */}
      {prompt.description && (
        <p className="text-gray-500 mb-2 line-clamp-1 text-xs leading-relaxed truncate">
          {prompt.description}
        </p>
      )}

      {/* 统计信息 */}
      <div className="flex items-center justify-between min-w-0">
        <div className="flex items-center gap-3 text-xs text-gray-400 flex-shrink-0">
          <div className="flex items-center gap-1">
            <Heart className="w-3 h-3" />
            <span>{prompt.likes || 0}</span>
          </div>
          <div className="flex items-center gap-1">
            <Download className="w-3 h-3" />
            <span>{prompt.downloads || 0}</span>
          </div>
          <div className="flex items-center gap-1">
            <Eye className="w-3 h-3" />
            <span>{prompt.views || 0}</span>
          </div>
        </div>

        {/* 标签 */}
        {prompt.tags && prompt.tags.length > 0 && (
          <div className="flex items-center gap-1 flex-shrink min-w-0">
            {prompt.tags.slice(0, 2).map((tag) => (
              <span
                key={tag}
                className="bg-gray-50 text-gray-400 px-1.5 py-0.5 rounded text-xs whitespace-nowrap"
              >
                {tag}
              </span>
            ))}
            {prompt.tags.length > 2 && (
              <span className="text-xs text-gray-300 whitespace-nowrap">
                +{prompt.tags.length - 2}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

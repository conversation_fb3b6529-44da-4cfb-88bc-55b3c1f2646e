import { Heart, Download, Eye, Tag } from "lucide-react";
import { Prompt } from "../types";

interface PromptCardProps {
  prompt: Prompt;
  onClick: () => void;
}

export default function PromptCard({ prompt, onClick }: PromptCardProps) {
  return (
    <div
      className="bg-black/40 hover:bg-black/50 border border-black/20 rounded-lg p-4 cursor-pointer group transition-all duration-200 hover:shadow-lg backdrop-blur-sm"
      onClick={onClick}
    >
      {/* 标题和分类 */}
      <div className="flex items-start justify-between mb-2">
        <h3 className="font-semibold text-gray-900 text-base line-clamp-1 group-hover:text-gray-800 transition-colors">
          {prompt.title}
        </h3>
        <span className="bg-blue-500/30 text-blue-800 px-2 py-1 rounded-full text-xs font-medium ml-3 flex-shrink-0">
          {prompt.category}
        </span>
      </div>

      {/* 描述 */}
      {prompt.description && (
        <p className="text-gray-700 mb-3 line-clamp-2 leading-relaxed text-sm">
          {prompt.description}
        </p>
      )}

      {/* 标签 */}
      {prompt.tags.length > 0 && (
        <div className="flex items-center gap-2 mb-3 flex-wrap">
          <Tag className="w-3 h-3 text-gray-600" />
          {prompt.tags.slice(0, 3).map((tag) => (
            <span
              key={tag}
              className="bg-gray-200/80 text-gray-700 px-2 py-0.5 rounded-full text-xs"
            >
              {tag}
            </span>
          ))}
          {prompt.tags.length > 3 && (
            <span className="text-xs text-gray-600 font-medium">
              +{prompt.tags.length - 3}
            </span>
          )}
        </div>
      )}

      {/* 统计信息 */}
      <div className="flex items-center gap-4 text-xs text-gray-600">
        <div className="flex items-center gap-1">
          <Heart className="w-3 h-3" />
          <span className="font-medium">{prompt.likes}</span>
        </div>
        <div className="flex items-center gap-1">
          <Download className="w-3 h-3" />
          <span className="font-medium">{prompt.downloads}</span>
        </div>
        <div className="flex items-center gap-1">
          <Eye className="w-3 h-3" />
          <span className="font-medium">{prompt.views}</span>
        </div>
      </div>
    </div>
  );
}

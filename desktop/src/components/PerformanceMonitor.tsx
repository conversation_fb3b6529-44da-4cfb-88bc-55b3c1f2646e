import { useState, useEffect } from "react";
import { apiClient } from "../lib/api-client";

interface PerformanceStats {
  cacheStats: {
    totalItems: number;
    validItems: number;
    expiredItems: number;
    maxSize: number;
    usage: number;
  };
  memoryUsage?: {
    used: number;
    total: number;
  };
  renderTime: number;
  lastUpdate: string;
}

export default function PerformanceMonitor() {
  const [stats, setStats] = useState<PerformanceStats | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [renderStart] = useState(Date.now());

  useEffect(() => {
    const updateStats = () => {
      const cacheStats = apiClient.getCacheStats();
      const renderTime = Date.now() - renderStart;

      setStats({
        cacheStats,
        renderTime,
        lastUpdate: new Date().toLocaleTimeString(),
      });
    };

    // 初始更新
    updateStats();

    // 定期更新
    const interval = setInterval(updateStats, 5000);

    return () => clearInterval(interval);
  }, [renderStart]);

  // 开发环境下显示性能监控
  useEffect(() => {
    const isDev = process.env.NODE_ENV === 'development';
    setIsVisible(isDev);
  }, []);

  if (!isVisible || !stats) {
    return null;
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getCacheUsageColor = (usage: number) => {
    if (usage < 50) return 'text-green-600';
    if (usage < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="fixed bottom-4 left-4 z-40 bg-black/80 text-white text-xs rounded-lg p-3 font-mono max-w-xs">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-semibold">性能监控</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ×
        </button>
      </div>

      <div className="space-y-1">
        {/* 渲染时间 */}
        <div className="flex justify-between">
          <span>渲染时间:</span>
          <span className={stats.renderTime > 1000 ? 'text-red-400' : 'text-green-400'}>
            {stats.renderTime}ms
          </span>
        </div>

        {/* 缓存统计 */}
        <div className="border-t border-gray-600 pt-1 mt-2">
          <div className="text-gray-300 mb-1">缓存统计</div>
          
          <div className="flex justify-between">
            <span>使用率:</span>
            <span className={getCacheUsageColor(stats.cacheStats.usage)}>
              {stats.cacheStats.usage.toFixed(1)}%
            </span>
          </div>
          
          <div className="flex justify-between">
            <span>有效项:</span>
            <span>{stats.cacheStats.validItems}</span>
          </div>
          
          <div className="flex justify-between">
            <span>过期项:</span>
            <span className={stats.cacheStats.expiredItems > 0 ? 'text-yellow-400' : ''}>
              {stats.cacheStats.expiredItems}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span>总计:</span>
            <span>{stats.cacheStats.totalItems}/{stats.cacheStats.maxSize}</span>
          </div>
        </div>

        {/* 内存使用 */}
        {stats.memoryUsage && (
          <div className="border-t border-gray-600 pt-1 mt-2">
            <div className="text-gray-300 mb-1">内存使用</div>
            <div className="flex justify-between">
              <span>已用:</span>
              <span>{formatBytes(stats.memoryUsage.used)}</span>
            </div>
            <div className="flex justify-between">
              <span>总计:</span>
              <span>{formatBytes(stats.memoryUsage.total)}</span>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="border-t border-gray-600 pt-2 mt-2 space-y-1">
          <button
            onClick={() => apiClient.clearCache()}
            className="w-full text-left text-red-400 hover:text-red-300"
          >
            清除缓存
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="w-full text-left text-blue-400 hover:text-blue-300"
          >
            重新加载
          </button>
        </div>

        {/* 最后更新时间 */}
        <div className="text-gray-500 text-center mt-2 pt-1 border-t border-gray-600">
          {stats.lastUpdate}
        </div>
      </div>
    </div>
  );
}

// 开发环境下的快捷键支持
if (process.env.NODE_ENV === 'development') {
  let monitorVisible = false;
  
  document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + Shift + M 切换性能监控显示
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'M') {
      e.preventDefault();
      monitorVisible = !monitorVisible;
      
      // 触发重新渲染
      const event = new CustomEvent('togglePerformanceMonitor', {
        detail: { visible: monitorVisible }
      });
      window.dispatchEvent(event);
    }
  });
}

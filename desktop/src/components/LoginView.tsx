import { useState } from 'react';
import { useAppStore } from '../store/appStore';
import { <PERSON>rkles, Eye, EyeOff } from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';

interface LoginRequest {
  email_or_username: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  data?: {
    user: any;
    token: string;
  };
  error?: string;
}

export default function LoginView() {
  const { login } = useAppStore();
  const [formData, setFormData] = useState({
    identifier: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await invoke<LoginResponse>('login', {
        request: {
          email_or_username: formData.identifier,
          password: formData.password
        } as LoginRequest
      });

      if (response.success) {
        // 登录成功，切换到主应用
        await login();
      } else {
        setError(response.error || '登录失败');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('登录失败，请检查网络连接');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = () => {
    // 打开注册页面（网页版）
    window.open('https://prompthub.xin/register', '_blank');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
      <div className="w-full max-w-md">
        {/* Logo 和标题 */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-violet-600 to-cyan-600 rounded-xl flex items-center justify-center">
              <Sparkles className="w-7 h-7 text-white" />
            </div>
          </div>
          <h1 className="text-2xl font-bold bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent">
            PromptHub
          </h1>
          <p className="text-gray-600 text-sm mt-2">AI提示词分享、创作平台</p>
        </div>

        {/* 登录表单 */}
        <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 邮箱或用户名 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                邮箱或用户名
              </label>
              <input
                type="text"
                value={formData.identifier}
                onChange={(e) => setFormData({ ...formData, identifier: e.target.value })}
                placeholder="输入您的邮箱或用户名"
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 bg-white/50"
                required
              />
            </div>

            {/* 密码 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                密码
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  placeholder="输入您的密码"
                  className="w-full px-4 py-3 pr-12 border border-gray-200 rounded-xl focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 bg-white/50"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* 错误信息 */}
            {error && (
              <div className="text-red-500 text-sm text-center bg-red-50 rounded-lg p-2">
                {error}
              </div>
            )}

            {/* 登录按钮 */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-violet-600 to-cyan-600 text-white py-3 rounded-xl font-medium hover:from-violet-700 hover:to-cyan-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '登录中...' : '登录'}
            </button>

            {/* 注册链接 */}
            <div className="text-center text-sm text-gray-600">
              还没有账户？{' '}
              <button
                type="button"
                onClick={handleRegister}
                className="text-violet-600 hover:text-violet-700 font-medium transition-colors"
              >
                立即注册
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

import { useState, useCallback, useRef } from 'react';
import { ApiResponse, PaginationParams } from '../lib/api-config';

interface UsePaginationOptions<T> {
  initialPageSize?: number;
  maxItems?: number;
  cacheKey?: string;
}

interface PaginationState<T> {
  items: T[];
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
  total: number;
}

interface PaginationActions<T> {
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  reset: () => void;
  setError: (error: string | null) => void;
}

export function usePagination<T>(
  fetchFunction: (params: PaginationParams) => Promise<ApiResponse<T[]>>,
  options: UsePaginationOptions<T> = {}
): PaginationState<T> & PaginationActions<T> {
  const {
    initialPageSize = 20,
    maxItems = 1000,
  } = options;

  const [state, setState] = useState<PaginationState<T>>({
    items: [],
    isLoading: false,
    isLoadingMore: false,
    error: null,
    hasMore: true,
    page: 0,
    total: 0,
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const isInitialLoadRef = useRef(true);

  const loadPage = useCallback(async (page: number, append: boolean = false) => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();

    const isInitialLoad = page === 1 && !append;
    
    setState(prev => ({
      ...prev,
      isLoading: isInitialLoad,
      isLoadingMore: !isInitialLoad,
      error: null,
    }));

    try {
      const response = await fetchFunction({
        page,
        limit: initialPageSize,
      });

      if (response.success && response.data) {
        const newItems = response.data;
        
        setState(prev => {
          const updatedItems = append ? [...prev.items, ...newItems] : newItems;
          
          // 限制最大项目数
          const limitedItems = updatedItems.slice(0, maxItems);
          
          // 计算是否还有更多数据
          let hasMore = false;
          if (response.pagination) {
            const { page: currentPage, limit, total } = response.pagination;
            hasMore = (currentPage * limit) < total && limitedItems.length < maxItems;
          } else {
            // 如果没有分页信息，根据返回的数据量判断
            hasMore = newItems.length === initialPageSize && limitedItems.length < maxItems;
          }

          return {
            ...prev,
            items: limitedItems,
            isLoading: false,
            isLoadingMore: false,
            hasMore,
            page,
            total: response.pagination?.total || limitedItems.length,
          };
        });
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          isLoadingMore: false,
          error: response.message || '加载失败',
        }));
      }
    } catch (error) {
      // 忽略被取消的请求
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      const errorMessage = error instanceof Error ? error.message : '加载失败';
      setState(prev => ({
        ...prev,
        isLoading: false,
        isLoadingMore: false,
        error: errorMessage,
      }));
    }
  }, [fetchFunction, initialPageSize, maxItems]);

  const loadMore = useCallback(async () => {
    if (state.isLoading || state.isLoadingMore || !state.hasMore) {
      return;
    }

    await loadPage(state.page + 1, true);
  }, [state.isLoading, state.isLoadingMore, state.hasMore, state.page, loadPage]);

  const refresh = useCallback(async () => {
    isInitialLoadRef.current = true;
    await loadPage(1, false);
  }, [loadPage]);

  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    setState({
      items: [],
      isLoading: false,
      isLoadingMore: false,
      error: null,
      hasMore: true,
      page: 0,
      total: 0,
    });
    
    isInitialLoadRef.current = true;
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  // 初始加载
  if (isInitialLoadRef.current && state.items.length === 0 && !state.isLoading && !state.error) {
    isInitialLoadRef.current = false;
    loadPage(1, false);
  }

  return {
    ...state,
    loadMore,
    refresh,
    reset,
    setError,
  };
}

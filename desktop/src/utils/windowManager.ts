import { getCurrentWindow } from '@tauri-apps/api/window';
import { LogicalSize } from '@tauri-apps/api/window';

export interface WindowSize {
  width: number;
  height: number;
  minWidth?: number;
  minHeight?: number;
}

export const WINDOW_SIZES = {
  // 登录页面 - 小窗口
  LOGIN: {
    width: 400,
    height: 500,
    minWidth: 350,
    minHeight: 450,
  },
  // 主应用 - 大窗口
  MAIN: {
    width: 900,
    height: 800,
    minWidth: 800,
    minHeight: 700,
  },
  // 设置页面 - 中等窗口
  SETTINGS: {
    width: 600,
    height: 700,
    minWidth: 500,
    minHeight: 600,
  },
} as const;

export type WindowSizeKey = keyof typeof WINDOW_SIZES;

export class WindowManager {
  private static currentWindow = getCurrentWindow();

  static async resizeWindow(sizeKey: WindowSizeKey) {
    const size = WINDOW_SIZES[sizeKey];

    try {
      console.log(`🪟 Resizing window to ${sizeKey}: ${size.width}x${size.height}`);

      // 设置窗口大小
      await this.currentWindow.setSize(new LogicalSize(size.width, size.height));

      // 设置最小尺寸
      if (size.minWidth && size.minHeight) {
        await this.currentWindow.setMinSize(new LogicalSize(size.minWidth, size.minHeight));
      }

      // 居中窗口
      await this.currentWindow.center();

      console.log(`✅ Window successfully resized to ${sizeKey}: ${size.width}x${size.height}`);
    } catch (error) {
      console.error('❌ Failed to resize window:', error);
    }
  }

  static async setLoginMode() {
    await this.resizeWindow('LOGIN');
  }

  static async setMainMode() {
    await this.resizeWindow('MAIN');
  }

  static async setSettingsMode() {
    await this.resizeWindow('SETTINGS');
  }

  static async getCurrentSize() {
    try {
      return await this.currentWindow.innerSize();
    } catch (error) {
      console.error('Failed to get window size:', error);
      return null;
    }
  }
}

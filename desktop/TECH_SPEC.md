# PromptHub 桌面客户端技术规格文档

**版本**: 1.0  
**日期**: 2025年7月12日

## 1. 技术架构概览

### 1.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    PromptHub 桌面客户端                      │
├─────────────────────────────────────────────────────────────┤
│  React UI Layer (TypeScript + Tailwind CSS)                │
│  ├── 命令面板组件                                            │
│  ├── 搜索组件                                               │
│  ├── 变量填充表单                                            │
│  └── 设置界面                                               │
├─────────────────────────────────────────────────────────────┤
│  Zustand State Management                                   │
│  ├── 用户状态                                               │
│  ├── 搜索状态                                               │
│  └── 应用设置                                               │
├─────────────────────────────────────────────────────────────┤
│  Tauri Core (Rust)                                         │
│  ├── 全局快捷键监听                                          │
│  ├── 窗口管理                                               │
│  ├── 系统集成                                               │
│  ├── 网络请求                                               │
│  └── 本地数据库                                             │
├─────────────────────────────────────────────────────────────┤
│  SQLite Database                                            │
│  ├── 用户认证信息                                            │
│  ├── 提示词缓存                                             │
│  └── 应用配置                                               │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 技术栈详细说明

#### 前端技术栈
- **React 18+**: 组件化UI开发
- **TypeScript 5+**: 类型安全
- **Tailwind CSS 3+**: 原子化CSS框架
- **Zustand 4+**: 轻量级状态管理
- **React Hook Form**: 表单处理
- **Framer Motion**: 动画效果

#### 后端技术栈 (Rust)
- **Tauri v2**: 跨平台桌面应用框架
- **tokio**: 异步运行时
- **serde**: 序列化/反序列化
- **reqwest**: HTTP客户端
- **sqlx**: 数据库操作
- **global-hotkey**: 全局快捷键
- **clipboard**: 剪贴板操作

## 2. 核心模块设计

### 2.1 前端模块结构

```
src/
├── components/           # UI组件
│   ├── CommandPalette/   # 命令面板
│   ├── SearchBox/        # 搜索框
│   ├── PromptList/       # 提示词列表
│   ├── VariableForm/     # 变量填充表单
│   ├── LoginModal/       # 登录模态框
│   └── Settings/         # 设置界面
├── hooks/                # 自定义Hooks
│   ├── useSearch.ts      # 搜索逻辑
│   ├── useAuth.ts        # 认证逻辑
│   ├── useHotkey.ts      # 快捷键处理
│   └── usePrompts.ts     # 提示词管理
├── store/                # Zustand状态管理
│   ├── authStore.ts      # 认证状态
│   ├── searchStore.ts    # 搜索状态
│   ├── settingsStore.ts  # 设置状态
│   └── promptStore.ts    # 提示词状态
├── types/                # TypeScript类型定义
│   ├── api.ts            # API接口类型
│   ├── prompt.ts         # 提示词类型
│   └── user.ts           # 用户类型
├── utils/                # 工具函数
│   ├── api.ts            # API调用
│   ├── storage.ts        # 本地存储
│   ├── template.ts       # 模板处理
│   └── validation.ts     # 数据验证
└── App.tsx               # 主应用组件
```

### 2.2 Rust后端模块结构

```
src-tauri/src/
├── main.rs               # 主入口
├── commands/             # Tauri命令
│   ├── auth.rs           # 认证相关命令
│   ├── search.rs         # 搜索相关命令
│   ├── prompts.rs        # 提示词相关命令
│   ├── system.rs         # 系统集成命令
│   └── mod.rs            # 模块导出
├── db/                   # 数据库操作
│   ├── models.rs         # 数据模型
│   ├── migrations.rs     # 数据库迁移
│   └── mod.rs            # 模块导出
├── services/             # 业务逻辑
│   ├── api_client.rs     # API客户端
│   ├── auth_service.rs   # 认证服务
│   ├── prompt_service.rs # 提示词服务
│   └── mod.rs            # 模块导出
├── utils/                # 工具函数
│   ├── crypto.rs         # 加密解密
│   ├── config.rs         # 配置管理
│   └── mod.rs            # 模块导出
└── lib.rs                # 库入口
```

## 3. 数据模型设计

### 3.1 SQLite数据库结构

```sql
-- 用户认证信息表
CREATE TABLE auth_tokens (
    id INTEGER PRIMARY KEY,
    user_id TEXT NOT NULL,
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    expires_at INTEGER NOT NULL,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- 提示词缓存表
CREATE TABLE cached_prompts (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    description TEXT,
    category TEXT,
    tags TEXT, -- JSON array
    variables TEXT, -- JSON array
    user_id TEXT NOT NULL,
    is_public BOOLEAN NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    synced_at INTEGER NOT NULL
);

-- 应用配置表
CREATE TABLE app_settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at INTEGER NOT NULL
);

-- 使用历史表
CREATE TABLE usage_history (
    id INTEGER PRIMARY KEY,
    prompt_id TEXT NOT NULL,
    used_at INTEGER NOT NULL,
    variables_filled TEXT -- JSON object
);
```

### 3.2 TypeScript类型定义

```typescript
// 提示词类型
interface Prompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  category: string;
  tags: string[];
  variables: Variable[];
  userId: string;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
}

// 变量类型
interface Variable {
  name: string;
  description?: string;
  defaultValue?: string;
  required: boolean;
  type: 'text' | 'number' | 'select';
  options?: string[]; // for select type
}

// 用户类型
interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
}

// 认证状态类型
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
}

// 搜索状态类型
interface SearchState {
  query: string;
  results: Prompt[];
  isLoading: boolean;
  error: string | null;
}
```

## 4. API接口设计

### 4.1 认证接口

```typescript
// 登录
POST /api/auth/login
Request: { email: string, password: string }
Response: { token: string, user: User, refreshToken: string }

// 刷新令牌
POST /api/auth/refresh
Request: { refreshToken: string }
Response: { token: string, refreshToken: string }

// 验证令牌
GET /api/auth/verify
Headers: { Authorization: "Bearer <token>" }
Response: { user: User }
```

### 4.2 提示词接口

```typescript
// 搜索提示词
GET /api/search?q={keyword}&limit={limit}&offset={offset}
Headers: { Authorization: "Bearer <token>" }
Response: { prompts: Prompt[], total: number }

// 获取用户提示词列表
GET /api/prompts?limit={limit}&offset={offset}
Headers: { Authorization: "Bearer <token>" }
Response: { prompts: Prompt[], total: number }

// 获取提示词详情
GET /api/prompts/{id}
Headers: { Authorization: "Bearer <token>" }
Response: { prompt: Prompt }
```

## 5. 核心功能实现

### 5.1 全局快捷键实现

```rust
// Rust端实现
use global_hotkey::{GlobalHotKeyManager, HotKeyState};

pub fn setup_global_hotkey(app_handle: tauri::AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    let manager = GlobalHotKeyManager::new()?;
    let hotkey = HotKey::new(Some(Modifiers::CONTROL | Modifiers::SHIFT), Code::KeyP);
    
    manager.register(hotkey)?;
    
    let event_receiver = GlobalHotKeyEvent::receiver();
    
    std::thread::spawn(move || {
        loop {
            if let Ok(event) = event_receiver.try_recv() {
                if event.state == HotKeyState::Pressed {
                    app_handle.emit_all("hotkey-pressed", ()).unwrap();
                }
            }
        }
    });
    
    Ok(())
}
```

### 5.2 变量解析与填充

```typescript
// 前端变量解析
export function parseVariables(content: string): Variable[] {
  const regex = /\{\{(\w+)(?:\|([^}]+))?\}\}/g;
  const variables: Variable[] = [];
  let match;
  
  while ((match = regex.exec(content)) !== null) {
    const [, name, description] = match;
    variables.push({
      name,
      description: description || '',
      required: true,
      type: 'text'
    });
  }
  
  return variables;
}

// 变量替换
export function replaceVariables(content: string, values: Record<string, string>): string {
  return content.replace(/\{\{(\w+)(?:\|[^}]+)?\}\}/g, (match, name) => {
    return values[name] || match;
  });
}
```

### 5.3 文本注入实现

```rust
// Rust端文本注入
use clipboard::{ClipboardProvider, ClipboardContext};

#[tauri::command]
pub async fn inject_text(text: String) -> Result<(), String> {
    // 先保存当前剪贴板内容
    let mut ctx: ClipboardContext = ClipboardProvider::new()
        .map_err(|e| format!("Failed to access clipboard: {}", e))?;
    
    let original_content = ctx.get_contents().unwrap_or_default();
    
    // 设置新内容到剪贴板
    ctx.set_contents(text)
        .map_err(|e| format!("Failed to set clipboard: {}", e))?;
    
    // 模拟 Ctrl+V 粘贴
    simulate_paste();
    
    // 延迟后恢复原剪贴板内容
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    ctx.set_contents(original_content)
        .map_err(|e| format!("Failed to restore clipboard: {}", e))?;
    
    Ok(())
}

fn simulate_paste() {
    // 平台特定的粘贴实现
    #[cfg(target_os = "windows")]
    {
        use winapi::um::winuser::{keybd_event, VK_CONTROL, VK_V, KEYEVENTF_KEYUP};
        unsafe {
            keybd_event(VK_CONTROL as u8, 0, 0, 0);
            keybd_event(VK_V as u8, 0, 0, 0);
            keybd_event(VK_V as u8, 0, KEYEVENTF_KEYUP, 0);
            keybd_event(VK_CONTROL as u8, 0, KEYEVENTF_KEYUP, 0);
        }
    }
    
    #[cfg(target_os = "macos")]
    {
        // macOS实现
    }
}
```

## 6. 性能优化策略

### 6.1 启动优化
- 延迟加载非核心模块
- 预编译模板和正则表达式
- 使用SQLite WAL模式提升数据库性能

### 6.2 内存优化
- 实现提示词缓存的LRU策略
- 及时清理不再使用的组件状态
- 使用虚拟滚动处理大量搜索结果

### 6.3 网络优化
- 实现请求防抖和节流
- 使用HTTP/2连接复用
- 实现智能的离线缓存策略

## 7. 安全考虑

### 7.1 数据加密
- 使用AES-256加密存储敏感数据
- JWT令牌安全存储
- 网络传输使用HTTPS

### 7.2 权限控制
- 最小权限原则
- 安全的API调用
- 输入验证和清理

## 8. 测试策略

### 8.1 单元测试
- React组件测试 (Jest + React Testing Library)
- Rust函数测试 (cargo test)
- API接口测试

### 8.2 集成测试
- 端到端工作流测试
- 跨平台兼容性测试
- 性能基准测试

## 9. 部署与分发

### 9.1 构建流程
- 自动化CI/CD流程
- 代码签名和公证
- 多平台并行构建

### 9.2 更新机制
- 使用Tauri内置updater
- 增量更新支持
- 回滚机制

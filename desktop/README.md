# PromptHub 桌面客户端

> 高效、轻量的AI提示词管理与使用工具，让您在任何应用中无缝使用提示词。

## 📋 项目概述

PromptHub 桌面客户端是 PromptHub 生态系统中的"效率终端"，旨在让用户能够在任何应用程序中快速搜索、填充并使用他们的提示词，大幅提升工作效率。

### 🎯 核心理念

遵循"主应用（Web）+ 伴侣应用（Desktop）"的设计哲学：
- **Web端**：管理与探索中心，负责提示词的CRUD、分享和社区功能
- **桌面端**：高效工作终端，专注于提供即时访问和使用体验

## 🚀 核心功能

- **全局激活**：通过自定义快捷键（默认 Cmd/Ctrl + Shift + P）在任何应用中唤起
- **实时搜索**：快速查找您需要的提示词
- **双输入模式**：系统提示词 + 用户输入，简单高效
- **无缝注入**：将生成的文本直接粘贴到当前活跃的应用程序中

- **系统集成**：状态栏/托盘图标，开机自启动选项

## 🛠️ 技术栈

- **框架**：Tauri v2
- **前端**：React + TypeScript + Tailwind CSS
- **状态管理**：Zustand
- **本地存储**：SQLite (via tauri-plugin-sql)
- **系统交互**：Rust

## 📦 项目结构

```
desktop/
├── src/                  # React 前端代码
│   ├── components/       # UI组件
│   ├── hooks/            # 自定义Hooks
│   ├── store/            # Zustand状态管理
│   ├── types/            # TypeScript类型定义
│   └── utils/            # 工具函数
├── src-tauri/            # Rust 后端代码
│   ├── src/              # Rust源代码
│   │   ├── main.rs       # 主入口
│   │   ├── commands.rs   # Tauri命令
│   │   └── db.rs         # 数据库操作
│   ├── Cargo.toml        # Rust依赖配置
│   └── tauri.conf.json   # Tauri配置
├── public/               # 静态资源
├── package.json          # 前端依赖
└── README.md             # 项目文档
```

## 🔄 开发流程

1. 克隆仓库
2. 安装依赖
3. 启动开发服务器
4. 构建应用

## 📝 贡献指南

欢迎贡献代码、报告问题或提出新功能建议！

## 📄 许可证

MIT

#!/bin/bash

# 🔍 环境变量配置验证脚本
echo "======================================"
echo "🔍 PromptHub 环境配置验证"
echo "======================================"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✅ $1 存在${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 不存在${NC}"
        return 1
    fi
}

check_service() {
    if curl -s "$1" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $2 服务正常${NC}"
        return 0
    else
        echo -e "${RED}❌ $2 服务无响应${NC}"
        return 1
    fi
}

# 1. 检查配置文件
echo "📋 1. 检查配置文件"
echo "-------------------"

config_ok=true

# 后端配置
if check_file "backend/.env"; then
    echo "   后端环境变量："
    cd backend
    source .env 2>/dev/null
    echo "   - PORT: ${PORT:-未设置}"
    echo "   - DB_HOST: ${DB_HOST:-未设置}"
    echo "   - DB_NAME: ${DB_NAME:-未设置}"
    echo "   - DB_USER: ${DB_USER:-未设置}"
    cd ..
else
    config_ok=false
fi

# 前端配置
if check_file "frontend/web/.env.local"; then
    echo "   前端环境变量："
    echo "   - API_URL: $(grep NEXT_PUBLIC_API_URL frontend/web/.env.local | cut -d'=' -f2)"
    echo "   - APP_NAME: $(grep NEXT_PUBLIC_APP_NAME frontend/web/.env.local | cut -d'=' -f2)"
else
    config_ok=false
fi

echo ""

# 2. 检查端口配置一致性
echo "🔌 2. 检查端口配置"
echo "-------------------"

backend_port=$(grep "^PORT=" backend/.env 2>/dev/null | cut -d'=' -f2)
frontend_api_port=$(grep "NEXT_PUBLIC_API_URL" frontend/web/.env.local 2>/dev/null | sed 's/.*localhost://' | sed 's/\/api//')

if [ "$backend_port" = "$frontend_api_port" ]; then
    echo -e "${GREEN}✅ 端口配置一致：$backend_port${NC}"
else
    echo -e "${RED}❌ 端口配置不一致${NC}"
    echo "   后端端口：$backend_port"
    echo "   前端API端口：$frontend_api_port"
    config_ok=false
fi

echo ""

# 3. 检查数据库连接
echo "🗄️ 3. 检查数据库连接"
echo "-------------------"

cd backend
source .env 2>/dev/null

if mysql -u "$DB_USER" -h "$DB_HOST" -P "$DB_PORT" "$DB_NAME" -e "SELECT 1" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 数据库连接成功${NC}"
    
    # 检查表结构
    table_count=$(mysql -u "$DB_USER" -h "$DB_HOST" -P "$DB_PORT" "$DB_NAME" -e "SHOW TABLES" 2>/dev/null | wc -l)
    if [ "$table_count" -gt 1 ]; then
        echo -e "${GREEN}✅ 数据库表结构正常（$((table_count-1))个表）${NC}"
    else
        echo -e "${YELLOW}⚠️  数据库表可能未初始化${NC}"
    fi
    
    # 检查数据
    prompt_count=$(mysql -u "$DB_USER" -h "$DB_HOST" -P "$DB_PORT" "$DB_NAME" -e "SELECT COUNT(*) FROM prompts" 2>/dev/null | tail -1)
    echo "   提示词数量：$prompt_count"
    
else
    echo -e "${RED}❌ 数据库连接失败${NC}"
    echo "   主机：$DB_HOST:$DB_PORT"
    echo "   用户：$DB_USER"
    echo "   数据库：$DB_NAME"
    config_ok=false
fi

cd ..
echo ""

# 4. 检查服务状态
echo "🚀 4. 检查服务状态"
echo "-------------------"

# 检查MySQL服务
if brew services list | grep mysql | grep started > /dev/null 2>&1; then
    echo -e "${GREEN}✅ MySQL服务运行中${NC}"
else
    echo -e "${YELLOW}⚠️  MySQL服务未启动${NC}"
fi

# 检查后端API
if check_service "http://localhost:${backend_port:-9000}/api/health" "后端API"; then
    # 获取API状态
    api_status=$(curl -s "http://localhost:${backend_port:-9000}/api/health" 2>/dev/null | grep -o '"success":[^,]*' | cut -d':' -f2)
    if [ "$api_status" = "true" ]; then
        echo "   API健康检查通过"
    fi
fi

# 检查前端服务
if check_service "http://localhost:3000" "前端服务 (3000)"; then
    frontend_port=3000
elif check_service "http://localhost:3001" "前端服务 (3001)"; then
    frontend_port=3001
else
    echo -e "${YELLOW}⚠️  前端服务未启动${NC}"
fi

echo ""

# 5. 总结
echo "📊 5. 配置总结"
echo "-------------------"

if [ "$config_ok" = true ]; then
    echo -e "${GREEN}🎉 配置验证通过！${NC}"
    echo ""
    echo "当前配置："
    echo "- 后端API：http://localhost:${backend_port:-9000}"
    echo "- 前端Web：http://localhost:${frontend_port:-3000}"
    echo "- 数据库：${DB_HOST:-localhost}:${DB_PORT:-3306}/${DB_NAME:-prompthub}"
    echo ""
    echo "🚀 可以开始开发了！"
else
    echo -e "${RED}❌ 配置存在问题，请检查上述错误${NC}"
    echo ""
    echo "🔧 修复建议："
    echo "1. 确保所有环境变量文件存在"
    echo "2. 检查端口配置一致性"
    echo "3. 验证数据库连接"
    echo "4. 启动必要的服务"
    echo ""
    echo "💡 参考文档：docs/环境变量配置指南.md"
fi

echo ""
echo "======================================" 
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'sh-cynosdbmysql-grp-knh8ui5y.sql.tencentcdb.com',
  user: 'root',
  password: 'Baby901221',
  database: 'prompthub_prod',
  port: 27410,
  charset: 'utf8mb4',
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000
};

async function checkImportedData() {
  console.log('🔍 检查导入的数据');
  console.log('==================================================');
  
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 查询最近导入的提示词（按创建时间倒序）
    const [recentPrompts] = await connection.execute(`
      SELECT 
        id, userId, title, description, content, category, tags, 
        likes, downloads, views, createdAt, updatedAt
      FROM prompts 
      ORDER BY updatedAt DESC 
      LIMIT 10
    `);
    
    console.log('\n📋 最近导入的10条提示词:');
    console.log('==================================================');
    
    recentPrompts.forEach((prompt, index) => {
      console.log(`\n${index + 1}. 📝 ${prompt.title}`);
      console.log(`   ID: ${prompt.id}`);
      console.log(`   用户ID: ${prompt.userId}`);
      console.log(`   分类: ${prompt.category}`);
      console.log(`   描述: ${prompt.description}`);
      console.log(`   内容预览: ${prompt.content.substring(0, 100)}...`);
      console.log(`   标签: ${prompt.tags}`);
      console.log(`   互动数据: 👍${prompt.likes} 📥${prompt.downloads} 👁${prompt.views}`);
      console.log(`   创建时间: ${prompt.createdAt}`);
      console.log(`   更新时间: ${prompt.updatedAt}`);
    });
    
    // 查询总体统计
    const [totalStats] = await connection.execute(`
      SELECT COUNT(*) as total_prompts FROM prompts
    `);
    
    const [userStats] = await connection.execute(`
      SELECT COUNT(*) as total_users FROM users WHERE role = 'user'
    `);
    
    const [categoryStats] = await connection.execute(`
      SELECT category, COUNT(*) as count 
      FROM prompts 
      GROUP BY category 
      ORDER BY count DESC
    `);
    
    console.log('\n📊 数据库总体统计:');
    console.log('==================================================');
    console.log(`总提示词数量: ${totalStats[0].total_prompts}`);
    console.log(`总用户数量: ${userStats[0].total_users}`);
    
    console.log('\n📊 分类分布:');
    categoryStats.forEach(stat => {
      console.log(`   ${stat.category}: ${stat.count} 个`);
    });
    
    // 检查测试数据
    const [testData] = await connection.execute(`
      SELECT title, category, likes, downloads, views, tags
      FROM prompts 
      WHERE title IN ('编程导师', '文案写手', '数据分析专家', '生活助手')
      ORDER BY title
    `);
    
    if (testData.length > 0) {
      console.log('\n🧪 测试数据检查:');
      console.log('==================================================');
      testData.forEach(item => {
        console.log(`📝 ${item.title}`);
        console.log(`   分类: ${item.category}`);
        console.log(`   互动: 👍${item.likes} 📥${item.downloads} 👁${item.views}`);
        console.log(`   标签: ${item.tags}`);
        console.log('');
      });
    }
    
    // 检查分类映射是否正确
    console.log('\n🎯 分类映射检查:');
    console.log('==================================================');
    
    const expectedMappings = [
      { title: '编程导师', expectedCategory: 'programming' },
      { title: '文案写手', expectedCategory: 'copywriting' },
      { title: '数据分析专家', expectedCategory: 'data-analysis' },
      { title: '生活助手', expectedCategory: 'lifestyle' }
    ];
    
    for (const mapping of expectedMappings) {
      const [result] = await connection.execute(
        'SELECT category FROM prompts WHERE title = ?',
        [mapping.title]
      );
      
      if (result.length > 0) {
        const actualCategory = result[0].category;
        const isCorrect = actualCategory === mapping.expectedCategory;
        console.log(`${isCorrect ? '✅' : '❌'} ${mapping.title}: ${actualCategory} ${isCorrect ? '' : `(期望: ${mapping.expectedCategory})`}`);
      }
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkImportedData().catch(console.error);

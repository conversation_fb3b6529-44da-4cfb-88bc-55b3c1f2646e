const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'sh-cynosdbmysql-grp-knh8ui5y.sql.tencentcdb.com',
  user: 'root',
  password: 'Baby901221',
  database: 'prompthub_prod',
  port: 27410,
  charset: 'utf8mb4',
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000
};

// 生成合理的互动数据
function generateReasonableInteractionData() {
  const likes = Math.floor(Math.random() * 46) + 5; // 5-50
  const downloads = Math.floor(Math.random() * 29) + 2; // 2-30
  const views = likes + downloads + Math.floor(Math.random() * 150) + 50; // 57-280左右
  
  return { likes, downloads, views };
}

async function fixInteractionData() {
  console.log('🔧 修复异常的互动数据');
  console.log('==================================================');
  
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 查找异常数据（点赞>50 或 下载>30 或 浏览>500）
    const [abnormalData] = await connection.execute(`
      SELECT id, title, likes, downloads, views, createdAt
      FROM prompts 
      WHERE likes > 50 OR downloads > 30 OR views > 500
      ORDER BY views DESC, likes DESC, downloads DESC
    `);
    
    console.log(`\n🔍 发现 ${abnormalData.length} 条异常数据:`);
    
    if (abnormalData.length === 0) {
      console.log('✅ 没有发现异常数据，所有互动数据都在合理范围内');
      return;
    }
    
    // 显示前10条最异常的数据
    console.log('\n📊 最异常的数据示例:');
    abnormalData.slice(0, 10).forEach((item, index) => {
      console.log(`${index + 1}. "${item.title.substring(0, 30)}..." 👍${item.likes} 📥${item.downloads} 👁${item.views}`);
    });
    
    console.log(`\n🔧 开始修复 ${abnormalData.length} 条异常数据...`);
    
    let fixedCount = 0;
    const batchSize = 50;
    
    // 分批处理
    for (let i = 0; i < abnormalData.length; i += batchSize) {
      const batch = abnormalData.slice(i, i + batchSize);
      
      for (const item of batch) {
        try {
          // 生成新的合理数据
          const newData = generateReasonableInteractionData();
          
          // 更新数据库
          await connection.execute(`
            UPDATE prompts 
            SET likes = ?, downloads = ?, views = ?
            WHERE id = ?
          `, [newData.likes, newData.downloads, newData.views, item.id]);
          
          fixedCount++;
          
          // 显示修复进度
          if (fixedCount % 100 === 0) {
            console.log(`📊 已修复 ${fixedCount}/${abnormalData.length} 条数据`);
          }
          
          // 显示修复详情（前20条）
          if (fixedCount <= 20) {
            console.log(`✅ [${fixedCount}] "${item.title.substring(0, 25)}..." ${item.likes}→${newData.likes} ${item.downloads}→${newData.downloads} ${item.views}→${newData.views}`);
          }
          
        } catch (error) {
          console.error(`❌ 修复失败 ID:${item.id} - ${error.message}`);
        }
      }
    }
    
    console.log(`\n✅ 修复完成！共修复 ${fixedCount} 条异常数据`);
    
    // 验证修复结果
    const [verifyData] = await connection.execute(`
      SELECT 
        COUNT(*) as total_prompts,
        MIN(likes) as min_likes, MAX(likes) as max_likes,
        MIN(downloads) as min_downloads, MAX(downloads) as max_downloads,
        MIN(views) as min_views, MAX(views) as max_views,
        AVG(likes) as avg_likes,
        AVG(downloads) as avg_downloads,
        AVG(views) as avg_views
      FROM prompts
    `);
    
    const stats = verifyData[0];
    console.log('\n📊 修复后的数据统计:');
    console.log(`   总提示词数: ${stats.total_prompts}`);
    console.log(`   点赞范围: ${stats.min_likes} - ${stats.max_likes} (平均: ${Math.round(stats.avg_likes)})`);
    console.log(`   下载范围: ${stats.min_downloads} - ${stats.max_downloads} (平均: ${Math.round(stats.avg_downloads)})`);
    console.log(`   浏览范围: ${stats.min_views} - ${stats.max_views} (平均: ${Math.round(stats.avg_views)})`);
    
    // 检查是否还有异常数据
    const [remainingAbnormal] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM prompts 
      WHERE likes > 50 OR downloads > 30 OR views > 500
    `);
    
    if (remainingAbnormal[0].count === 0) {
      console.log('\n🎉 所有异常数据已修复完成！');
    } else {
      console.log(`\n⚠️  仍有 ${remainingAbnormal[0].count} 条异常数据需要处理`);
    }
    
  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 显示当前异常数据统计
async function showAbnormalStats() {
  console.log('📊 异常数据统计分析');
  console.log('==================================================');
  
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    
    // 统计各种异常情况
    const [stats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_prompts,
        COUNT(CASE WHEN likes > 50 THEN 1 END) as high_likes,
        COUNT(CASE WHEN downloads > 30 THEN 1 END) as high_downloads,
        COUNT(CASE WHEN views > 500 THEN 1 END) as high_views,
        COUNT(CASE WHEN likes > 50 OR downloads > 30 OR views > 500 THEN 1 END) as total_abnormal,
        MAX(likes) as max_likes,
        MAX(downloads) as max_downloads,
        MAX(views) as max_views
      FROM prompts
    `);
    
    const data = stats[0];
    console.log(`总提示词数: ${data.total_prompts}`);
    console.log(`异常点赞数 (>50): ${data.high_likes} 条`);
    console.log(`异常下载数 (>30): ${data.high_downloads} 条`);
    console.log(`异常浏览量 (>500): ${data.high_views} 条`);
    console.log(`总异常数据: ${data.total_abnormal} 条`);
    console.log(`最大值: 点赞${data.max_likes}, 下载${data.max_downloads}, 浏览${data.max_views}`);
    
  } catch (error) {
    console.error('❌ 统计失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 命令行参数处理
const args = process.argv.slice(2);

if (args.includes('--help')) {
  console.log(`
📖 互动数据修复工具

用法: node fix-interaction-data.js [选项]

选项:
  --stats    只显示异常数据统计，不进行修复
  --help     显示帮助信息

功能:
- 修复异常的点赞、下载、浏览数据
- 将数据调整到合理范围（点赞5-50，下载2-30，浏览57-280）
- 提供详细的修复日志和统计信息
`);
} else if (args.includes('--stats')) {
  showAbnormalStats();
} else {
  fixInteractionData();
}

module.exports = {
  fixInteractionData,
  showAbnormalStats,
  generateReasonableInteractionData
};

# 新格式数据导入工具说明

针对您新增的两种数据格式，我已创建了专门的导入脚本来处理不同的数据结构。

## 📁 数据格式支持

### 1. NewZone格式 (`newzone_prompts.json`)
使用脚本：`import-newzone-prompts.js`

**数据结构：**
```json
{
  "id": 5,
  "count": 5196,
  "tags": ["ai"],
  "website": "https://github.com/...",
  "zh": {
    "title": "提示词生成器",
    "prompt": "I want you to act as...",
    "remark": "根据指定要求，让 ChatGPT 生成提示词。",
    "description": "我想让你充当一个提示生成器..."
  }
}
```

**特点：**
- 中文内容在`zh`对象中
- 使用`prompt`字段存储提示词内容
- 包含`count`使用次数统计
- 有`website`来源信息
- 支持`tags`标签数组

### 2. UserPrompts格式 (`user_prompts.json`)
使用脚本：`import-user-prompts.js`

**数据结构：**
```json
{
  "id": 10,
  "title": "JSON翻译",
  "description": "请帮我把JSON格式的文本翻译成简体中文...",
  "remark": "Contributed by jodybarna83.",
  "upvotes": 6,
  "downvotes": 1,
  "upvoteDifference": 5,
  "owner": "jodybarna83",
  "createdAt": "2023-06-02T05:12:26.260Z"
}
```

**特点：**
- 直接的标题和描述结构
- 包含点赞数据(`upvotes`, `downvotes`)
- 有`owner`作者信息
- 包含创建时间戳
- 支持`remark`备注信息

## 🚀 使用方法

### 安装依赖
```bash
cd scripts/bulk-import
npm install
```

### 导入NewZone格式数据
```bash
# 确保newzone_prompts.json文件在bulk-import目录下
node import-newzone-prompts.js
```

### 导入UserPrompts格式数据
```bash
# 确保user_prompts.json文件在bulk-import目录下
node import-user-prompts.js
```

## 🔍 核心功能特性

### 智能分类推断
两个脚本都具备智能分类功能：

**NewZone脚本分类策略：**
1. 优先根据`tags`标签映射分类
2. 分析标题、描述、备注中的关键词
3. 应用特殊规则（如"评论"→写作类）
4. 默认分配到`ai-assistant`分类

**UserPrompts脚本分类策略：**
1. 根据标题和描述关键词智能匹配
2. 特殊格式识别（如JSON→编程类）
3. 长文本自动归类为写作类
4. 支持多种技术栈关键词识别

### 数据完整性保护
- **重复检测**：自动跳过已存在的标题
- **数据验证**：检查必要字段完整性
- **错误处理**：详细的错误日志和进度显示
- **批量处理**：大文件分批插入，避免内存溢出

### 智能数据生成
- **互动数据**：基于原始热度生成点赞、收藏、浏览量
- **时间分布**：将数据分散到过去的时间段
- **用户关联**：智能映射原作者或随机分配用户
- **标签提取**：从内容中自动提取相关标签

## 📊 导入统计

运行后会显示详细统计信息：
- 成功导入的提示词数量
- 跳过的重复或无效数据
- 分类分布统计
- 用户分布统计（UserPrompts格式）
- 点赞和收藏总数

## ⚙️ 数据库映射

### 字段映射表

| 源格式字段 | 数据库字段 | 说明 |
|------------|------------|------|
| **NewZone格式** |
| `zh.title` | `title` | 提示词标题 |
| `zh.prompt` | `content` | 提示词内容 |
| `zh.description` | `description` | 详细描述 |
| `tags` | `tags` | JSON格式标签 |
| `count` | - | 用于生成互动数据 |
| **UserPrompts格式** |
| `title` | `title` | 提示词标题 |
| `description` | `content` | 提示词内容 |
| `remark + description` | `description` | 组合描述 |
| `upvotes` | - | 用于生成点赞数 |
| `owner` | - | 用于用户映射 |

### 自动分类映射

| 检测关键词 | 分配分类 |
|------------|----------|
| gpt, 提示词, 助手 | `ai-assistant` |
| 写作, 文案, 翻译 | `copywriting` |
| 编程, 代码, python | `programming` |
| 设计, ui, 创意 | `design` |
| 教育, 学习, 教学 | `education` |
| 商业, 营销, 管理 | `business-analysis` |
| 数据, 统计, 分析 | `data-analysis` |
| 美食, 旅行, 生活 | `lifestyle` |

## 🔧 自定义配置

如需修改分类映射或数据处理规则，可以编辑脚本中的以下部分：

1. **分类映射**：修改`keywordToCategoryMapping`对象
2. **数据库配置**：修改`dbConfig`对象
3. **互动数据规则**：调整点赞、收藏生成算法
4. **时间分布**：修改`totalDays`参数

## 🚨 注意事项

1. **数据备份**：导入前请备份现有数据库
2. **用户依赖**：确保数据库中已有用户数据
3. **内存使用**：大文件会分批处理，但仍需注意内存使用
4. **重复运行**：脚本支持重复运行，会自动跳过已存在数据
5. **数据库连接**：确保数据库连接信息正确且有写入权限

## 📝 运行示例

```bash
# 导入NewZone数据
$ node import-newzone-prompts.js
🚀 NewZone提示词导入工具
==================================================
📄 读取NewZone提示词数据...
📋 读取到 3057 条提示词数据
🔗 连接数据库...
✅ 数据库连接成功
👥 找到 50 个现有用户
开始处理NewZone格式提示词数据...
📊 已处理 100 / 3057 条数据
...
✅ 成功导入 2891 个提示词
⚠️  跳过 166 个重复或无效数据
```

这两个专门的导入脚本已经完全适配了您的新数据格式，可以智能处理数据转换和分类，确保导入的数据质量和完整性。 
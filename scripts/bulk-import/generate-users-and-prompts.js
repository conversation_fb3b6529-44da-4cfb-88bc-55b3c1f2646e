const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

// 数据库配置 - 生产环境配置
const dbConfig = {
  host: 'sh-cynosdbmysql-grp-knh8ui5y.sql.tencentcdb.com',  // 腾讯云生产数据库地址
  user: 'root',          // 数据库用户名
  password: 'Baby901221', // 数据库密码
  database: 'prompthub_prod', // 生产数据库名称
  port: 27410,           // 生产数据库端口
  charset: 'utf8mb4',
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000
};

// 如果需要使用环境变量，可以取消下面的注释
/*
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'prompthub',
  port: process.env.DB_PORT || 3306,
  charset: 'utf8mb4'
};
*/

// 从命令行参数获取配置
const args = process.argv.slice(2);
const options = {
  createUsers: args.includes('--create-users') || args.includes('-u'),
  userCount: parseInt(args.find(arg => arg.startsWith('--users='))?.split('=')[1]) || 100,
  skipExistingUsers: args.includes('--skip-existing') || true, // 默认跳过已存在用户
  force: args.includes('--force') || args.includes('-f') // 强制重新创建所有用户
};

// 显示帮助信息
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
📖 PromptHub 批量导入工具使用说明

用法: node generate-users-and-prompts.js [选项]

选项:
  --create-users, -u     创建新用户（默认不创建，只导入提示词）
  --users=N             指定创建用户数量（默认100）
  --skip-existing       跳过已存在的用户名（默认启用）
  --force, -f           强制重新创建所有用户（会删除现有用户）
  --help, -h            显示此帮助信息

示例:
  node generate-users-and-prompts.js                    # 只导入提示词到现有用户
  node generate-users-and-prompts.js --create-users    # 创建100个用户并导入提示词
  node generate-users-and-prompts.js -u --users=50     # 创建50个用户并导入提示词
  node generate-users-and-prompts.js --force           # 强制重新创建所有用户和提示词

⚠️  注意: 使用 --force 会删除现有的用户和提示词数据！
  `);
  process.exit(0);
}

// 100个时尚中文用户名（既有中文也有英文风格）
const fashionableUsernames = [
  // 时尚社交风格中文用户名
  '柠檬小仙女', '奶茶星人', '月亮贩卖员', '云朵收集者', '星辰追梦人',
  '咖啡与猫', '晚风撞怀', '橘子汽水', '温柔一刀', '半糖主义',
  '薄荷小清新', '樱花飞舞', '蓝色多瑙河', '午后阳光', '梦想捕手',
  '小确幸', '治愈系少女', '森系小鹿', '慢生活家', '文艺青年',
  '独角兽女孩', '追光者', '海风轻吻', '雨后彩虹', '暖阳如你',
  '小时光', '甜甜圈女孩', '北极星', '软糖公主', '花火少年',
  
  // 时尚英文用户名
  'MoonlightDreamer', 'VelvetVibes', 'GoldenHour', 'StardustSoul', 'OceanBreeze',
  'SunsetGlow', 'WildFlower', 'CherryBlossom', 'MidnightMuse', 'SilverLining',
  'CosmicDancer', 'PeachFuzz', 'LavenderSkies', 'HoneyMoon', 'RoseGold',
  'CloudNine', 'SweetEscape', 'CandyFloss', 'DreamCatcher', 'AngelWings',
  'ButterflyKiss', 'CottonCandy', 'StarlightWish', 'VanillaSky', 'BlushPink',
  'GlitterBomb', 'SoftServe', 'PastelDream', 'FloralVibes', 'SugarRush',
  'MistyMorning', 'GoldenGlow', 'SeashellSong', 'BubbleTea', 'CrystalClear',
  'VintageVibes', 'RetroChic', 'BohoSoul', 'MinimalMood', 'ModernMuse',
  'ChicStyle', 'TrendyTwist', 'FashionForward', 'StyleIcon', 'GlamourGirl',
  'ElegantEdge', 'ClassyVibes', 'SophisticatedSoul', 'LuxeLife', 'PoshPrincess',
  'UrbanChic', 'CityGirl', 'MetroStyle', 'CosmopolitanVibes', 'DowntownDiva',
  'SunKissed', 'MoonChild', 'StarGazer', 'SkyDancer', 'CloudWalker',
  'OceanDreamer', 'MountainHigh', 'DesertRose', 'ForestFairy', 'GardenGlow',
  'WildHeart', 'FreeSpirit', 'BohemianSoul', 'GypsyQueen', 'WanderlustWoman',
  'AdventureSeeker', 'ExplorerSoul', 'TravelBug', 'NomadLife', 'GlobeTrotter',
  'AestheticVibes', 'MinimalLife', 'SlowLiving', 'Hygge', 'Cozy',
  'SoftGirl', 'DarkAcademia', 'CottageCore', 'Y2K', 'Indie'
];

// 时尚社交风格个人简介模板
const bioTemplates = [
  '生活美学家 | 用心记录每个美好瞬间 🌸',
  '咖啡控 | 在文字与代码间寻找诗意 ☕',
  '治愈系少女 | 分享温暖的小确幸 💕',
  '摄影爱好者 | 用镜头捕捉世界的美好 📸',
  '文艺青年 | 书香茶韵，诗酒趁年华 📚',
  '旅行达人 | 世界那么大，我想去看看 ✈️',
  '美食博主 | 人间烟火气，最抚凡人心 🍜',
  '手作控 | 慢生活的艺术践行者 🎨',
  '花艺师 | 用花草装点生活的诗意 🌿',
  '瑜伽教练 | 在一呼一吸间找到内心平静 🧘‍♀️',
  '独立设计师 | 追求极简美学与功能平衡 ✨',
  '音乐发烧友 | 在旋律中寻找生活的节拍 🎵',
  '小众电影爱好者 | 光影交错间品味人生 🎬',
  '园艺新手 | 和植物一起慢慢成长 🌱',
  '夜猫子 | 在深夜的创作中找到自己 🌙'
];

// 分类映射 - 将提示词分类映射到数据库分类（使用英文key）
const categoryMapping = {
  '写作': 'copywriting',
  '教育': 'education', 
  '编程': 'programming',
  '设计': 'design',
  '数据分析': 'data-analysis',
  '商业分析': 'business-analysis',
  'AI助手': 'ai-assistant',
  '生活方式': 'lifestyle',
  '生产力': 'ai-assistant', // 映射到ai-assistant
  '创意写作': 'copywriting',
  '图像生成': 'design',
  '商业营销': 'business-analysis'
};

// 获取随机数
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 获取随机数组元素
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// 生成随机邮箱
function generateEmail(username) {
  const domains = ['gmail.com', 'outlook.com', 'qq.com', '163.com', 'hotmail.com', 'sina.com', 'foxmail.com'];
  
  // 中文用户名映射表
  const chineseNameMap = {
    '柠檬小仙女': 'lemon_fairy',
    '奶茶星人': 'milktea_lover',
    '月亮贩卖员': 'moon_seller',
    '云朵收集者': 'cloud_collector',
    '星辰追梦人': 'star_dreamer',
    '咖啡与猫': 'coffee_cat',
    '晚风撞怀': 'evening_breeze',
    '橘子汽水': 'orange_soda',
    '温柔一刀': 'gentle_blade',
    '半糖主义': 'half_sugar',
    '薄荷小清新': 'mint_fresh',
    '樱花飞舞': 'sakura_dance',
    '蓝色多瑙河': 'blue_danube',
    '午后阳光': 'afternoon_sun',
    '梦想捕手': 'dream_catcher',
    '小确幸': 'little_happiness',
    '治愈系少女': 'healing_girl',
    '森系小鹿': 'forest_deer',
    '慢生活家': 'slow_life',
    '文艺青年': 'artsy_youth',
    '独角兽女孩': 'unicorn_girl',
    '追光者': 'light_chaser',
    '海风轻吻': 'sea_breeze',
    '雨后彩虹': 'rainbow_after_rain',
    '暖阳如你': 'warm_sun',
    '小时光': 'little_time',
    '甜甜圈女孩': 'donut_girl',
    '北极星': 'north_star',
    '软糖公主': 'candy_princess',
    '花火少年': 'firework_youth'
  };
  
  // 处理用户名
  let emailUsername;
  if (chineseNameMap[username]) {
    // 中文用户名使用映射
    emailUsername = chineseNameMap[username] + getRandomInt(100, 999);
  } else {
    // 英文用户名直接使用
    emailUsername = username.toLowerCase().replace(/[^\w]/g, '') + getRandomInt(10, 99);
  }
  
  return `${emailUsername}@${getRandomElement(domains)}`;
}

// 生成随机头像URL
function generateAvatar() {
  const avatarServices = [
    'https://api.dicebear.com/7.x/avataaars/svg?seed=',
    'https://api.dicebear.com/7.x/personas/svg?seed=',
    'https://api.dicebear.com/7.x/initials/svg?seed=',
    'https://api.dicebear.com/7.x/bottts/svg?seed=',
    'https://api.dicebear.com/7.x/micah/svg?seed='
  ];
  const seed = Math.random().toString(36).substring(7);
  return getRandomElement(avatarServices) + seed;
}

// 生成过去的随机日期（MySQL TIMESTAMP格式）
function generatePastDate(daysAgo) {
  const now = new Date();
  const pastDate = new Date(now.getTime() - (daysAgo * 24 * 60 * 60 * 1000));
  // 添加随机小时和分钟
  pastDate.setHours(getRandomInt(0, 23));
  pastDate.setMinutes(getRandomInt(0, 59));
  pastDate.setSeconds(getRandomInt(0, 59));
  return pastDate;
}

// 获取现有用户
async function getExistingUsers(connection) {
  const [users] = await connection.query('SELECT id, username FROM users');
  return users;
}

// 创建用户数据
async function createUsers(connection) {
  console.log(`开始创建用户 (目标: ${options.userCount}个)...`);
  
  // 获取现有用户
  const existingUsers = await getExistingUsers(connection);
  const existingUsernames = new Set(existingUsers.map(u => u.username));
  
  if (options.force) {
    console.log('⚠️  强制模式：删除现有用户和相关数据...');
    await connection.query('SET FOREIGN_KEY_CHECKS = 0');
    await connection.query('DELETE FROM likes');
    await connection.query('DELETE FROM prompts');
    await connection.query('DELETE FROM users');
    await connection.query('SET FOREIGN_KEY_CHECKS = 1');
    console.log('✅ 现有数据已清除');
  }
  
  const users = [];
  const usersToCreate = [];
  let createdCount = 0;
  let skippedCount = 0;
  
  for (let i = 0; i < options.userCount && i < fashionableUsernames.length; i++) {
    const username = fashionableUsernames[i];
    
    // 检查用户名是否已存在
    if (!options.force && existingUsernames.has(username)) {
      if (options.skipExistingUsers) {
        console.log(`⏭️  跳过已存在用户: ${username}`);
        skippedCount++;
        continue;
      }
    }
    
    const email = generateEmail(username);
    const password = await bcrypt.hash('password123', 10); // 默认密码
    const bio = getRandomElement(bioTemplates);
    const avatar = generateAvatar();
    const createdAt = generatePastDate(getRandomInt(1, 90)); // 过去90天内随机
    
    usersToCreate.push([
      username,
      email,
      password,
      bio,
      avatar,
      'user', // role
      1, // isActive (TRUE)
      createdAt,
      createdAt
    ]);
    createdCount++;
  }
  
  if (usersToCreate.length > 0) {
    const insertUserQuery = `
      INSERT INTO users (username, email, password, bio, avatar, role, isActive, createdAt, updatedAt)
      VALUES ?
    `;
    
    await connection.query(insertUserQuery, [usersToCreate]);
    console.log(`✅ ${createdCount}个新用户创建完成`);
  }
  
  if (skippedCount > 0) {
    console.log(`ℹ️  跳过了${skippedCount}个已存在的用户`);
  }
  
  // 获取所有用户（包括现有的和新创建的）
  const [allUsers] = await connection.query('SELECT id, username FROM users ORDER BY id');
  console.log(`📊 当前用户总数: ${allUsers.length}`);
  
  return allUsers;
}

// 处理提示词数据
async function processPrompts(connection, users, promptsData) {
  console.log('开始处理提示词数据...');
  
  if (!Array.isArray(promptsData)) {
    throw new Error('提示词数据格式错误，应该是数组');
  }
  
  const prompts = [];
  const totalDays = 60; // 分布在过去60天
  const promptsPerDay = Math.ceil(promptsData.length / totalDays);
  
  for (let i = 0; i < promptsData.length; i++) {
    const promptData = promptsData[i];
    const dayIndex = Math.floor(i / promptsPerDay);
    const randomUser = getRandomElement(users);
    
    // 映射分类
    const mappedCategory = categoryMapping[promptData.category] || 'ai-assistant';
    
    // 生成创建时间（分布在过去60天，每天几条）
    const createdAt = generatePastDate(dayIndex + getRandomInt(0, 2)); // 在指定天数基础上增加0-2天随机
    
    // 处理标签 - 确保JSON格式正确
    let tagsJson = '[]';
    if (Array.isArray(promptData.tags)) {
      tagsJson = JSON.stringify(promptData.tags);
    } else if (typeof promptData.tags === 'string' && promptData.tags.trim()) {
      tagsJson = JSON.stringify([promptData.tags]);
    }
    
    // 随机生成互动数据 - 增大数据量
    const likes = getRandomInt(5, 50);        // 点赞数：5-50
    const copies = getRandomInt(2, 30);       // 复制数：2-30  
    const views = getRandomInt(likes + copies, likes + copies + getRandomInt(50, 200)); // 浏览量更大
    
    prompts.push([
      randomUser.id, // userId
      promptData.title,
      promptData.description || '', // description
      promptData.content || promptData.prompt || '', // 支持content或prompt字段
      mappedCategory, // category
      tagsJson, // tags (JSON)
      0, // isPrivate (FALSE)
      'published', // status
      likes, // likes
      copies, // downloads 改为 copies (复制数)
      views, // views
      createdAt,
      createdAt
    ]);
  }
  
  const insertPromptQuery = `
    INSERT INTO prompts (userId, title, description, content, category, tags, isPrivate, status, likes, downloads, views, createdAt, updatedAt)
    VALUES ?
  `;
  
  await connection.query(insertPromptQuery, [prompts]);
  console.log(`✅ ${prompts.length}个提示词创建完成`);
  
  // 获取创建的提示词
  const [promptRows] = await connection.query('SELECT id, userId FROM prompts ORDER BY id DESC LIMIT ?', [prompts.length]);
  return promptRows.reverse();
}

// 生成随机互动数据（点赞）
async function generateInteractions(connection, users, prompts) {
  console.log('生成随机互动数据...');
  
  const likes = [];
  
  // 为每个提示词生成随机点赞
  for (const prompt of prompts) {
    // 增大点赞数量范围：5-30个点赞（更活跃的数据）
    const likeCount = getRandomInt(5, 30);
    const shuffledUsers = [...users].sort(() => 0.5 - Math.random());
    
    for (let i = 0; i < likeCount && i < shuffledUsers.length; i++) {
      if (shuffledUsers[i].id !== prompt.userId) { // 不能给自己点赞
        likes.push([shuffledUsers[i].id, prompt.id, new Date()]);
      }
    }
  }
  
  // 插入点赞数据
  if (likes.length > 0) {
    const insertLikesQuery = `INSERT INTO likes (userId, promptId, createdAt) VALUES ?`;
    await connection.query(insertLikesQuery, [likes]);
  }
  
  console.log(`✅ 生成了${likes.length}个点赞`);
}

// 更新统计数据
async function updateStatistics(connection) {
  console.log('更新统计数据...');
  
  // 更新提示词的点赞数 - 基于实际的点赞记录
  await connection.query(`
    UPDATE prompts p 
    SET likes = (
      SELECT COUNT(*) FROM likes l WHERE l.promptId = p.id
    )
  `);
  
  // 更新分类统计 - 基于英文分类key
  const categories = [
    'ai-assistant', 'copywriting', 'programming', 'design', 
    'data-analysis', 'business-analysis', 'education', 'lifestyle'
  ];
  
  for (const category of categories) {
    await connection.query(`
      UPDATE categories 
      SET prompt_count = (
        SELECT COUNT(*) FROM prompts WHERE category = ? AND status = 'published'
      ) 
      WHERE name = ?
    `, [category, category]);
  }
  
  console.log('✅ 统计数据更新完成');
}

// 主函数
async function main() {
  let connection;
  
  try {
    console.log('🚀 PromptHub 批量导入工具');
    console.log('='.repeat(50));
    console.log(`📋 运行配置:`);
    console.log(`   创建用户: ${options.createUsers ? '是' : '否'}`);
    if (options.createUsers) {
      console.log(`   用户数量: ${options.userCount}`);
      console.log(`   跳过已存在: ${options.skipExistingUsers ? '是' : '否'}`);
      console.log(`   强制重建: ${options.force ? '是' : '否'}`);
    }
    console.log('='.repeat(50));
    
    // 检查提示词数据文件
    const promptsFilePath = path.join(__dirname, 'prompts-data.json');
    if (!fs.existsSync(promptsFilePath)) {
      console.error('❌ 请先创建 prompts-data.json 文件并放入提示词数据');
      console.log('💡 您可以参考 prompts-data-example.json 文件的格式');
      process.exit(1);
    }
    
    // 读取提示词数据
    const promptsData = JSON.parse(fs.readFileSync(promptsFilePath, 'utf8'));
    console.log(`📄 读取到 ${promptsData.length} 条提示词数据`);
    
    // 连接数据库
    console.log('🔗 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 检查数据库连接
    const [result] = await connection.query('SELECT 1');
    console.log('✅ 数据库连接测试成功');
    
    let users;
    
    // 创建用户（如果需要）
    if (options.createUsers) {
      users = await createUsers(connection);
    } else {
      // 获取现有用户
      users = await getExistingUsers(connection);
      console.log(`📊 使用现有用户 (${users.length}个)`);
      
      if (users.length === 0) {
        console.error('❌ 数据库中没有用户，请先使用 --create-users 选项创建用户');
        process.exit(1);
      }
    }
    
    // 处理提示词
    const prompts = await processPrompts(connection, users, promptsData);
    
    // 生成互动数据
    await generateInteractions(connection, users, prompts);
    
    // 更新统计数据
    await updateStatistics(connection);
    
    // 显示最终统计
    const [userCount] = await connection.query('SELECT COUNT(*) as count FROM users');
    const [promptCount] = await connection.query('SELECT COUNT(*) as count FROM prompts');
    const [likeCount] = await connection.query('SELECT COUNT(*) as count FROM likes');
    const [copyStats] = await connection.query('SELECT SUM(downloads) as total_copies FROM prompts');
    const [viewStats] = await connection.query('SELECT SUM(views) as total_views FROM prompts');
    
    console.log('\n🎉 批量导入完成！');
    console.log('='.repeat(50));
    console.log(`👥 用户总数: ${userCount[0].count}`);
    console.log(`📝 提示词总数: ${promptCount[0].count}`);
    console.log(`👍 点赞总数: ${likeCount[0].count}`);
    console.log(`📋 复制总数: ${copyStats[0].total_copies || 0}`);
    console.log(`👀 浏览总数: ${viewStats[0].total_views || 0}`);
    console.log('='.repeat(50));
    console.log('🌟 您的PromptHub平台现在看起来有很多活跃用户了！');
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
    console.error('详细错误信息:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main }; 
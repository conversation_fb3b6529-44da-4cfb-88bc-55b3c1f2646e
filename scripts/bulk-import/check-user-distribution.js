const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'sh-cynosdbmysql-grp-knh8ui5y.sql.tencentcdb.com',
  user: 'root',
  password: 'Baby901221',
  database: 'prompthub_prod',
  port: 27410,
  charset: 'utf8mb4',
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000
};

async function checkUserDistribution() {
  console.log('🔍 检查用户分配情况');
  console.log('==================================================');
  
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 查询所有用户的ID范围
    const [userStats] = await connection.execute(`
      SELECT 
        MIN(id) as min_user_id,
        MAX(id) as max_user_id,
        COUNT(*) as total_users
      FROM users 
      WHERE role = 'user'
    `);
    
    console.log('\n👥 用户ID统计:');
    console.log(`   最小用户ID: ${userStats[0].min_user_id}`);
    console.log(`   最大用户ID: ${userStats[0].max_user_id}`);
    console.log(`   总用户数: ${userStats[0].total_users}`);
    
    // 查询最近导入的提示词的用户分配情况
    const [recentPrompts] = await connection.execute(`
      SELECT 
        userId,
        title,
        updatedAt
      FROM prompts 
      WHERE title IN ('编程导师', '文案写手', '数据分析专家', '生活助手', '测试提示词1')
      ORDER BY updatedAt DESC
    `);
    
    console.log('\n📋 测试提示词的用户分配:');
    recentPrompts.forEach(prompt => {
      console.log(`   "${prompt.title}" → 用户ID: ${prompt.userId}`);
    });
    
    // 检查用户ID是否都在118以下
    const userIdsAbove118 = recentPrompts.filter(p => p.userId > 118);
    const userIdsBelow118 = recentPrompts.filter(p => p.userId <= 118);
    
    console.log('\n🎯 用户ID分布检查:');
    console.log(`   ID ≤ 118的提示词: ${userIdsBelow118.length} 个`);
    console.log(`   ID > 118的提示词: ${userIdsAbove118.length} 个`);
    
    if (userIdsAbove118.length > 0) {
      console.log('\n⚠️  分配到ID > 118的提示词:');
      userIdsAbove118.forEach(prompt => {
        console.log(`   "${prompt.title}" → 用户ID: ${prompt.userId}`);
      });
    }
    
    // 查询所有用户ID列表
    const [allUsers] = await connection.execute(`
      SELECT id, username, role 
      FROM users 
      WHERE role = 'user'
      ORDER BY id
    `);
    
    console.log('\n📊 所有用户ID列表:');
    console.log(`   用户ID范围: ${allUsers.map(u => u.id).join(', ')}`);
    
    // 检查是否有ID > 118的用户
    const usersAbove118 = allUsers.filter(u => u.id > 118);
    const usersBelow118 = allUsers.filter(u => u.id <= 118);
    
    console.log('\n🔍 用户ID分析:');
    console.log(`   ID ≤ 118的用户数: ${usersBelow118.length}`);
    console.log(`   ID > 118的用户数: ${usersAbove118.length}`);
    
    if (usersAbove118.length > 0) {
      console.log('\n📋 ID > 118的用户:');
      usersAbove118.slice(0, 10).forEach(user => {
        console.log(`   ID: ${user.id}, 用户名: ${user.username}`);
      });
      if (usersAbove118.length > 10) {
        console.log(`   ... 还有 ${usersAbove118.length - 10} 个用户`);
      }
    }
    
    // 查询最近导入的所有提示词的用户分配统计
    const [userDistribution] = await connection.execute(`
      SELECT 
        userId,
        COUNT(*) as prompt_count
      FROM prompts 
      WHERE updatedAt >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
      GROUP BY userId
      ORDER BY userId
    `);
    
    if (userDistribution.length > 0) {
      console.log('\n📈 最近1小时导入的提示词用户分配统计:');
      userDistribution.forEach(stat => {
        const isAbove118 = stat.userId > 118 ? ' ⚠️' : '';
        console.log(`   用户ID ${stat.userId}: ${stat.prompt_count} 个提示词${isAbove118}`);
      });
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkUserDistribution().catch(console.error);

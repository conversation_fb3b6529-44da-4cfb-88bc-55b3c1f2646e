# 批量导入用户和提示词数据

这个工具用于批量向PromptHub平台导入用户和提示词数据，包含100个时尚社交用户名（文艺治愈系+INS风格）和智能的时间分布策略。

## 🎯 功能特点

- 🎨 **100个时尚社交用户名** - 30个文艺治愈系中文昵称 + 70个INS风英文昵称
- ⏰ **智能时间分布** - 提示词创建时间分布在过去60天，每天几条，有利于SEO
- 🔄 **随机分配** - 提示词随机分配给用户，模拟真实使用场景
- 📊 **活跃互动数据** - 点赞(5-50个)、复制(2-30次)、浏览量(50-200次)
- 🗃️ **分类映射** - 自动将中文分类映射到数据库对应分类
- 🛡️ **智能重复检测** - 避免重复创建用户，支持增量导入
- ⚙️ **灵活配置** - 支持多种运行模式和参数配置

## 📋 表结构说明

本脚本基于以下MySQL数据库表结构：

### 用户表 (users)
```sql
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE,
  password VARCHAR(255) NOT NULL,
  bio TEXT,
  avatar VARCHAR(500),
  role VARCHAR(20) DEFAULT 'user',
  isActive BOOLEAN DEFAULT TRUE,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 提示词表 (prompts)
```sql
CREATE TABLE prompts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  userId INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  content TEXT NOT NULL,
  category VARCHAR(50) NOT NULL,
  tags JSON DEFAULT NULL,
  isPrivate BOOLEAN DEFAULT FALSE,
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  type ENUM('text', 'image', 'agent', 'mcp') DEFAULT 'text',
  likes INT DEFAULT 0,
  downloads INT DEFAULT 0,
  views INT DEFAULT 0,
  rating DECIMAL(3,2) DEFAULT 0.00,
  usage_count INT DEFAULT 0,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
);
```

## 🚀 使用方法

### 1. 安装依赖

```bash
cd scripts/bulk-import
npm install
```

### 2. 配置数据库

复制环境变量配置文件：
```bash
cp env.example .env
```

编辑 `.env` 文件，填入正确的数据库配置：
```env
DB_HOST=*************
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=prompthub
DB_PORT=3306
```

### 3. 准备提示词数据

创建 `prompts-data.json` 文件，将您的提示词数据放入其中。格式如下：

```json
[
  {
    "id": 5262,
    "title": "世界级文案高手",
    "content": "你是世界级文案高手。帮我重写这个 {登陆页面/销售话术/邮件}，让它更有吸引力、更简洁、更能打动人。用 PAS 或 AIDA 这种经过验证的框架。原稿在这儿：{贴上原文}。",
    "category": "写作",
    "tags": [],
    "hotness": 1,
    "isPublic": true
  }
]
```

### 4. 运行导入脚本

#### 📖 命令行参数说明

```bash
# 查看帮助信息
node generate-users-and-prompts.js --help

# 基本选项
--create-users, -u     创建新用户（默认不创建，只导入提示词）
--users=N             指定创建用户数量（默认100）
--skip-existing       跳过已存在的用户名（默认启用）
--force, -f           强制重新创建所有用户（⚠️会删除现有数据）
--help, -h            显示帮助信息
```

#### 🎯 使用场景

**场景1: 首次导入（创建用户+提示词）**
```bash
# 创建100个用户并导入提示词
node generate-users-and-prompts.js --create-users

# 或者简写
node generate-users-and-prompts.js -u
```

**场景2: 只导入提示词到现有用户**
```bash
# 使用现有用户，只导入新的提示词
node generate-users-and-prompts.js
```

**场景3: 创建指定数量的用户**
```bash
# 创建50个用户并导入提示词
node generate-users-and-prompts.js --create-users --users=50

# 或者简写
node generate-users-and-prompts.js -u --users=50
```

**场景4: 强制重新创建所有数据**
```bash
# ⚠️ 危险操作：删除所有现有数据并重新创建
node generate-users-and-prompts.js --force --create-users
```

**场景5: 增量添加用户**
```bash
# 跳过已存在的用户名，只创建新用户（默认行为）
node generate-users-and-prompts.js --create-users --skip-existing
```

## 📊 分类映射

脚本会自动将中文分类映射到数据库的英文分类：

| 中文分类 | 数据库分类 | 说明 |
|---------|------------|------|
| 写作 | copywriting | 文案写作 |
| 教育 | education | 教育培训 |
| 编程 | programming | 编程开发 |
| 设计 | design | 设计创意 |
| 数据分析 | data-analysis | 数据分析 |
| 商业分析 | business-analysis | 商业分析 |
| AI助手 | ai-assistant | AI助手 |
| 生活方式 | lifestyle | 生活方式 |
| 生产力 | ai-assistant | 映射到AI助手 |
| 图像生成 | design | 映射到设计 |

## ⚠️ 注意事项

### 安全提醒
- 🚨 **--force 参数会删除所有现有数据**，请谨慎使用
- 💾 建议在生产环境使用前先备份数据库
- 🔐 确保数据库配置文件的安全性

### 性能考虑
- 📈 大量数据导入可能需要一些时间，请耐心等待
- 🔄 支持增量导入，避免重复操作
- 💽 建议在服务器性能较好的时段执行

### 数据质量
- ✅ 互动数据更活跃：点赞5-50个，复制2-30次，浏览量50-200次
- 🎲 用户分配和时间分布都经过随机化处理
- 📅 提示词创建时间分布在过去60天，有利于SEO
- 📊 浏览量 > 点赞数 + 复制数，符合真实使用模式

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库配置
   # 确认数据库服务运行状态
   # 验证网络连接
   ```

2. **用户名重复错误**
   ```bash
   # 使用默认的跳过模式
   node generate-users-and-prompts.js --create-users --skip-existing
   ```

3. **提示词数据格式错误**
   ```bash
   # 检查 prompts-data.json 格式
   # 参考 prompts-data-example.json
   ```

## 📈 运行示例

```bash
🚀 PromptHub 批量导入工具
==================================================
📋 运行配置:
   创建用户: 是
   用户数量: 100
   跳过已存在: 是
   强制重建: 否
==================================================
📄 读取到 156 条提示词数据
🔗 连接数据库...
✅ 数据库连接成功
✅ 数据库连接测试成功
开始创建用户 (目标: 100个)...
⏭️  跳过已存在用户: 创意大师
⏭️  跳过已存在用户: 代码忍者
✅ 98个新用户创建完成
ℹ️  跳过了2个已存在的用户
📊 当前用户总数: 100
开始处理提示词数据...
✅ 156个提示词创建完成
生成随机互动数据...
✅ 生成了1247个点赞
更新统计数据...
✅ 统计数据更新完成

🎉 批量导入完成！
==================================================
👥 用户总数: 100
📝 提示词总数: 156
👍 点赞总数: 1247
📋 复制总数: 2184
==================================================
🌟 您的PromptHub平台现在看起来有很多活跃用户了！
```

## 📞 技术支持

如果遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查数据库连接和配置
3. 验证提示词数据格式
4. 在项目仓库创建Issue 
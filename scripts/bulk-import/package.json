{"name": "bulk-import-prompts", "version": "1.0.0", "description": "批量导入用户和提示词数据到PromptHub平台", "main": "generate-users-and-prompts.js", "scripts": {"import": "node generate-users-and-prompts.js", "import-with-users": "node generate-users-and-prompts.js --create-users", "import-prompts-only": "node generate-users-and-prompts.js", "create-50-users": "node generate-users-and-prompts.js --create-users --users=50", "help": "node generate-users-and-prompts.js --help", "force-rebuild": "node generate-users-and-prompts.js --force --create-users", "import-promptport": "node import-promptport-data.js", "import-promptport-custom": "node import-promptport-data.js --file=", "test": "echo \"No test specified\" && exit 1"}, "keywords": ["prompts", "bulk-import", "database", "mysql"], "author": "PromptHub Team", "license": "MIT", "dependencies": {"mysql2": "^3.6.5", "bcryptjs": "^2.4.3"}}
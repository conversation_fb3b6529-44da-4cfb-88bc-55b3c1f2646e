const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置 - 生产环境配置
const dbConfig = {
  host: 'sh-cynosdbmysql-grp-knh8ui5y.sql.tencentcdb.com',
  user: 'root',
  password: 'Baby901221',
  database: 'prompthub_prod',
  port: 27410,
  charset: 'utf8mb4',
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000
};

// 关键词到分类的智能映射
const keywordToCategoryMapping = {
  // AI助手类
  'gpt': 'ai-assistant',
  'chatgpt': 'ai-assistant',
  '提示': 'ai-assistant',
  '提示词': 'ai-assistant',
  '优化': 'ai-assistant',
  '助手': 'ai-assistant',
  '生成': 'ai-assistant',
  
  // 写作类
  '写作': 'copywriting',
  '文案': 'copywriting',
  '翻译': 'copywriting',
  '内容': 'copywriting',
  '文章': 'copywriting',
  '博客': 'copywriting',
  '新闻': 'copywriting',
  '故事': 'copywriting',
  '小说': 'copywriting',
  '创作': 'copywriting',
  '润色': 'copywriting',
  
  // 编程类
  '编程': 'programming',
  '代码': 'programming',
  '开发': 'programming',
  '程序': 'programming',
  '软件': 'programming',
  'python': 'programming',
  'javascript': 'programming',
  'java': 'programming',
  'css': 'programming',
  'html': 'programming',
  'react': 'programming',
  'vue': 'programming',
  'api': 'programming',
  
  // 设计类
  '设计': 'design',
  'ui': 'design',
  'ux': 'design',
  '界面': 'design',
  '用户体验': 'design',
  '视觉': 'design',
  '图片': 'design',
  '图像': 'design',
  '创意': 'design',
  
  // 教育类
  '教育': 'education',
  '学习': 'education',
  '教学': 'education',
  '解释': 'education',
  '指导': 'education',
  '培训': 'education',
  '课程': 'education',
  '老师': 'education',
  '讲解': 'education',
  '知识': 'education',
  
  // 商业分析类
  '商业': 'business-analysis',
  '营销': 'business-analysis',
  '市场': 'business-analysis',
  '策略': 'business-analysis',
  '管理': 'business-analysis',
  '分析': 'business-analysis',
  '咨询': 'business-analysis',
  '销售': 'business-analysis',
  '创业': 'business-analysis',
  'b2b': 'business-analysis',
  'b2c': 'business-analysis',
  
  // 数据分析类
  '数据': 'data-analysis',
  '统计': 'data-analysis',
  '报告': 'data-analysis',
  '图表': 'data-analysis',
  '可视化': 'data-analysis',
  
  // 生活方式类
  '美食': 'lifestyle',
  '旅行': 'lifestyle',
  '健康': 'lifestyle',
  '生活': 'lifestyle',
  '娱乐': 'lifestyle',
  '游戏': 'lifestyle',
  '电影': 'lifestyle',
  '音乐': 'lifestyle',
  '运动': 'lifestyle',
  '健身': 'lifestyle'
};

// 智能分类推断函数
function inferCategory(promptData) {
  const { title = '', description = '', remark = '' } = promptData;
  const allText = `${title} ${description} ${remark}`.toLowerCase();
  
  // 根据关键词匹配分类
  for (const [keyword, category] of Object.entries(keywordToCategoryMapping)) {
    if (allText.includes(keyword.toLowerCase())) {
      return category;
    }
  }
  
  // 特殊规则
  if (title.includes('JSON') || description.includes('JSON')) {
    return 'programming';
  }
  
  if (description.length > 500) {
    return 'copywriting'; // 长描述通常是写作类
  }
  
  // 默认分类
  return 'ai-assistant';
}

// 从owner字段推断或创建用户映射
function mapOwnerToUser(owner, users) {
  // 如果owner为空或null，返回随机用户
  if (!owner) {
    return users[Math.floor(Math.random() * users.length)];
  }
  
  // 尝试找到匹配的用户名
  const matchedUser = users.find(user => 
    user.username.toLowerCase() === owner.toLowerCase()
  );
  
  if (matchedUser) {
    return matchedUser;
  }
  
  // 如果没找到匹配用户，返回随机用户
  return users[Math.floor(Math.random() * users.length)];
}

// 获取随机数
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 生成过去的随机日期
function generatePastDate(daysAgo) {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo - getRandomInt(0, 1));
  date.setHours(getRandomInt(0, 23), getRandomInt(0, 59), getRandomInt(0, 59));
  return date;
}

// 获取现有用户（排除真实用户id>=118）
async function getExistingUsers(connection) {
  const [users] = await connection.query('SELECT id, username FROM users WHERE id < 118 ORDER BY id');
  console.log(`✅ 获取到 ${users.length} 个可用用户（排除真实用户id>=118）`);
  
  if (users.length === 0) {
    throw new Error('没有可用的用户来分配提示词！请确保数据库中有id<118的用户。');
  }
  
  return users;
}

// 处理UserPrompts格式的数据
async function processUserPrompts(connection, users, promptsData) {
  console.log('开始处理UserPrompts格式数据...');
  
  if (!Array.isArray(promptsData)) {
    throw new Error('提示词数据格式错误，应该是数组');
  }
  
  const prompts = [];
  const totalDays = 120; // 分布在过去120天
  const promptsPerDay = Math.ceil(promptsData.length / totalDays);
  
  let processedCount = 0;
  let skippedCount = 0;
  
  for (let i = 0; i < promptsData.length; i++) {
    const promptData = promptsData[i];
    
    // 验证数据完整性
    if (!promptData.title || !promptData.description) {
      console.log(`⚠️  跳过不完整的数据 (ID: ${promptData.id})`);
      skippedCount++;
      continue;
    }
    
    const { 
      title, 
      description, 
      remark = '', 
      upvotes = 0, 
      downvotes = 0,
      upvoteDifference = 0,
      owner = '',
      createdAt,
      updatedAt
    } = promptData;
    
    // 检查是否已存在相同标题的提示词
    const [existing] = await connection.query(
      'SELECT id FROM prompts WHERE title = ?', 
      [title]
    );
    
    if (existing.length > 0) {
      console.log(`⚠️  跳过已存在的提示词: ${title}`);
      skippedCount++;
      continue;
    }
    
    // 根据owner映射用户
    const assignedUser = mapOwnerToUser(owner, users);
    const dayIndex = Math.floor(i / promptsPerDay);
    
    // 智能推断分类
    const category = inferCategory(promptData);
    
    // 处理创建时间
    let finalCreatedAt;
    if (createdAt) {
      finalCreatedAt = new Date(createdAt);
    } else {
      finalCreatedAt = generatePastDate(dayIndex + getRandomInt(0, 5));
    }
    
    // 处理标签 - 从标题和描述提取关键词作为标签
    const tags = [];
    if (title.includes('JSON')) tags.push('JSON');
    if (title.includes('GPT') || description.includes('GPT')) tags.push('GPT');
    if (description.includes('翻译')) tags.push('翻译');
    if (description.includes('优化')) tags.push('优化');
    if (description.includes('创业')) tags.push('创业');
    
    const tagsJson = JSON.stringify(tags);
    
    // 基于upvotes和downvotes生成互动数据
    const baseUpvotes = Math.max(0, upvotes);
    const baseDownvotes = Math.max(0, downvotes);
    
    const likes = Math.max(1, baseUpvotes + getRandomInt(0, 10));
    const copies = Math.max(1, Math.floor(likes * 0.6) + getRandomInt(1, 8));
    const views = Math.max(likes + copies, likes * getRandomInt(3, 8) + getRandomInt(10, 50));
    
    // 构建最终描述
    const finalDescription = remark ? `${remark}\n\n${description}` : description;
    
    prompts.push([
      assignedUser.id,
      title,
      finalDescription.substring(0, 1000), // 限制描述长度
      description, // content字段使用原始description
      category,
      tagsJson,
      0, // isPrivate
      'published', // status
      likes,
      copies,
      views,
      finalCreatedAt,
      finalCreatedAt
    ]);
    
    processedCount++;
    
    // 每处理200条显示进度
    if (processedCount % 200 === 0) {
      console.log(`📊 已处理 ${processedCount} / ${promptsData.length} 条数据`);
    }
  }
  
  if (prompts.length === 0) {
    console.log('❌ 没有有效的提示词数据需要导入');
    return [];
  }
  
  // 分批插入数据 (每次1000条)
  const batchSize = 1000;
  let insertedCount = 0;
  
  const insertPromptQuery = `
    INSERT INTO prompts (
      userId, title, description, content, category, tags, 
      isPrivate, status, likes, downloads, views, createdAt, updatedAt
    ) VALUES ?
  `;
  
  for (let i = 0; i < prompts.length; i += batchSize) {
    const batch = prompts.slice(i, i + batchSize);
    await connection.query(insertPromptQuery, [batch]);
    insertedCount += batch.length;
    console.log(`📋 已插入 ${insertedCount} / ${prompts.length} 条数据`);
  }
  
  console.log(`✅ 成功导入 ${processedCount} 个提示词`);
  console.log(`⚠️  跳过 ${skippedCount} 个重复或无效数据`);
  
  // 返回新创建的提示词
  const [promptRows] = await connection.query(
    'SELECT id, userId, category FROM prompts ORDER BY id DESC LIMIT ?', 
    [prompts.length]
  );
  
  return promptRows.reverse();
}

// 生成随机互动数据
async function generateInteractions(connection, users, prompts) {
  if (prompts.length === 0) return;
  
  console.log('生成随机互动数据...');
  
  const batchSize = 5000;
  let totalLikes = 0;
  let totalFavorites = 0;
  
  // 分批处理提示词
  for (let batchStart = 0; batchStart < prompts.length; batchStart += batchSize) {
    const batch = prompts.slice(batchStart, batchStart + batchSize);
    const likes = [];
    const favorites = [];
    
    for (const prompt of batch) {
      // 获取该提示词的目标点赞数
      const [promptInfo] = await connection.query(
        'SELECT likes FROM prompts WHERE id = ?', 
        [prompt.id]
      );
      
      const targetLikes = promptInfo[0].likes;
      const shuffledUsers = [...users].sort(() => 0.5 - Math.random());
      
      // 生成点赞
      let actualLikes = 0;
      for (let i = 0; i < targetLikes && i < shuffledUsers.length; i++) {
        if (shuffledUsers[i].id !== prompt.userId) {
          likes.push([shuffledUsers[i].id, prompt.id, new Date()]);
          actualLikes++;
        }
      }
      
      // 生成收藏（点赞用户的20-40%也会收藏）
      const favoriteCount = Math.floor(actualLikes * (0.2 + Math.random() * 0.2));
      for (let i = 0; i < favoriteCount; i++) {
        if (shuffledUsers[i].id !== prompt.userId) {
          favorites.push([shuffledUsers[i].id, prompt.id, new Date()]);
        }
      }
    }
    
    // 批量插入点赞
    if (likes.length > 0) {
      await connection.query('INSERT INTO likes (userId, promptId, createdAt) VALUES ?', [likes]);
      totalLikes += likes.length;
    }
    
    // 批量插入收藏
    if (favorites.length > 0) {
      await connection.query('INSERT INTO favorites (userId, promptId, createdAt) VALUES ?', [favorites]);
      totalFavorites += favorites.length;
    }
    
    console.log(`📊 批次 ${Math.floor(batchStart/batchSize) + 1}: 生成了 ${likes.length} 个点赞，${favorites.length} 个收藏`);
  }
  
  console.log(`✅ 总共生成了 ${totalLikes} 个点赞，${totalFavorites} 个收藏`);
}

// 更新统计数据
async function updateStatistics(connection) {
  console.log('更新统计数据...');
  
  // 更新提示词点赞数
  await connection.query(`
    UPDATE prompts p 
    SET likes = (SELECT COUNT(*) FROM likes l WHERE l.promptId = p.id)
  `);
  
  // 更新分类统计
  const categories = [
    'ai-assistant', 'copywriting', 'programming', 'design', 
    'data-analysis', 'business-analysis', 'education', 'lifestyle'
  ];
  
  for (const category of categories) {
    await connection.query(`
      UPDATE categories 
      SET prompt_count = (
        SELECT COUNT(*) FROM prompts WHERE category = ? AND status = 'published'
      ) 
      WHERE name = ?
    `, [category, category]);
  }
  
  console.log('✅ 统计数据更新完成');
}

// 显示分类分布统计
async function showCategoryStats(connection) {
  console.log('\n📊 分类分布统计:');
  console.log('='.repeat(40));
  
  const [categoryStats] = await connection.query(`
    SELECT 
      category,
      COUNT(*) as count,
      ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
    FROM prompts 
    WHERE status = 'published'
    GROUP BY category 
    ORDER BY count DESC
  `);
  
  for (const stat of categoryStats) {
    console.log(`${stat.category.padEnd(20)} ${stat.count.toString().padStart(4)} 条 (${stat.percentage}%)`);
  }
}

// 显示用户分布统计
async function showOwnerStats(connection) {
  console.log('\n👥 用户分布统计 (Top 10):');
  console.log('='.repeat(40));
  
  const [userStats] = await connection.query(`
    SELECT 
      u.username,
      COUNT(p.id) as prompt_count
    FROM users u
    LEFT JOIN prompts p ON u.id = p.userId
    GROUP BY u.id, u.username
    HAVING prompt_count > 0
    ORDER BY prompt_count DESC
    LIMIT 10
  `);
  
  for (const stat of userStats) {
    console.log(`${stat.username.padEnd(20)} ${stat.prompt_count.toString().padStart(4)} 条`);
  }
}

// 主函数
async function main() {
  let connection;
  
  try {
    console.log('🚀 UserPrompts数据导入工具');
    console.log('='.repeat(50));
    
    // 检查数据文件
    const promptsFilePath = path.join(__dirname, 'user_prompts.json');
    if (!fs.existsSync(promptsFilePath)) {
      console.error('❌ 找不到 user_prompts.json 文件');
      process.exit(1);
    }
    
    // 读取数据
    console.log('📄 读取UserPrompts数据...');
    const promptsData = JSON.parse(fs.readFileSync(promptsFilePath, 'utf8'));
    console.log(`📋 读取到 ${promptsData.length} 条提示词数据`);
    
    // 连接数据库
    console.log('🔗 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 获取现有用户
    const users = await getExistingUsers(connection);
    if (users.length === 0) {
      console.error('❌ 数据库中没有用户，请先创建用户');
      process.exit(1);
    }
    console.log(`👥 找到 ${users.length} 个现有用户`);
    
    // 处理提示词
    const prompts = await processUserPrompts(connection, users, promptsData);
    
    if (prompts.length > 0) {
      // 生成互动数据
      await generateInteractions(connection, users, prompts);
      
      // 更新统计
      await updateStatistics(connection);
      
      // 显示统计信息
      await showCategoryStats(connection);
      await showOwnerStats(connection);
    }
    
    // 显示最终统计
    const [promptCount] = await connection.query('SELECT COUNT(*) as count FROM prompts');
    const [likeCount] = await connection.query('SELECT COUNT(*) as count FROM likes');
    const [favoriteCount] = await connection.query('SELECT COUNT(*) as count FROM favorites');
    
    console.log('\n🎉 UserPrompts数据导入完成！');
    console.log('='.repeat(50));
    console.log(`📝 提示词总数: ${promptCount[0].count}`);
    console.log(`👍 点赞总数: ${likeCount[0].count}`);
    console.log(`⭐ 收藏总数: ${favoriteCount[0].count}`);
    console.log('='.repeat(50));
    
  } catch (error) {
    console.error('❌ 导入过程中发生错误:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔒 数据库连接已关闭');
    }
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main }; 
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置 - 生产环境配置
const dbConfig = {
  host: 'sh-cynosdbmysql-grp-knh8ui5y.sql.tencentcdb.com',
  user: 'root',
  password: 'Baby901221',
  database: 'prompthub_prod',
  port: 27410,
  charset: 'utf8mb4',
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000
};

// 从命令行参数获取配置
const args = process.argv.slice(2);
const options = {
  fileName: args.find(arg => arg.startsWith('--file='))?.split('=')[1] || 'promptport_prompts.json',
  help: args.includes('--help') || args.includes('-h')
};

// 显示帮助信息
if (options.help) {
  console.log(`
📖 PromptPort数据导入工具

用法: node import-promptport-data.js [选项]

选项:
  --file=filename       指定JSON文件名（默认: promptport_prompts.json）
  --help, -h           显示帮助信息

特点:
- 只导入zh字段下的中文内容
- 自动分配到现有用户
- 智能分类映射
- 保留原始互动数据
`);
  process.exit(0);
}

// 分类映射 - 根据关键词智能分配分类（优化版）
const categoryMapping = {
  'programming': ['编程', '代码', '开发', 'python', 'javascript', 'java', 'react', 'vue', '程序', '算法', '导师', '技术'],
  'copywriting': ['写作', '文案', '翻译', '润色', '改写', '创作', '文章', '内容', '营销', '广告', '写手'],
  'data-analysis': ['数据', '统计', '分析', '图表', '报告', '研究', '调研', '指标', '趋势', '数据分析'],
  'lifestyle': ['生活', '娱乐', '美食', '旅行', '健康', '运动', '音乐', '电影', '游戏', '休闲', '生活助手'],
  'design': ['设计', 'ui', 'ux', '界面', '创意', '美术', '视觉', '图像', '海报', '品牌'],
  'education': ['教育', '学习', '教学', '培训', '课程', '知识', '解释', '讲解', '辅导', '学术'],
  'business-analysis': ['商业', '管理', '策略', '市场', '销售', '运营', '企业', '商务'],
  'ai-assistant': ['助手', 'gpt', 'ai', '人工智能', '机器人', '聊天', '对话', '提示词']
};

// 智能分类推断
function inferCategory(title, content, description, topics) {
  const textToAnalyze = `${title} ${content} ${description} ${topics.join(' ')}`.toLowerCase();
  
  for (const [category, keywords] of Object.entries(categoryMapping)) {
    for (const keyword of keywords) {
      if (textToAnalyze.includes(keyword)) {
        return category;
      }
    }
  }
  
  return 'ai-assistant'; // 默认分类
}

// 生成随机时间（过去60天内）
function generateRandomTime() {
  const now = new Date();
  const sixtyDaysAgo = new Date(now.getTime() - (60 * 24 * 60 * 60 * 1000));
  const randomTime = new Date(sixtyDaysAgo.getTime() + Math.random() * (now.getTime() - sixtyDaysAgo.getTime()));
  return randomTime;
}

// 生成随机互动数据（与原始脚本保持一致的合理范围）
function generateInteractionData() {
  const likes = Math.floor(Math.random() * 46) + 5; // 5-50
  const downloads = Math.floor(Math.random() * 29) + 2; // 2-30
  const views = likes + downloads + Math.floor(Math.random() * 150) + 50; // 57-280左右

  return { likes, downloads, views };
}

// 将原始数据调整到合理范围
function adjustInteractionData(originalLikes, originalDownloads, originalViews) {
  // 如果原始数据在合理范围内，直接使用
  if (originalLikes <= 50 && originalDownloads <= 30 && originalViews <= 300) {
    return {
      likes: Math.max(originalLikes, 1),
      downloads: Math.max(originalDownloads, 1),
      views: Math.max(originalViews, originalLikes + originalDownloads + 10)
    };
  }

  // 如果原始数据过大，按比例缩放到合理范围
  const scaleFactor = Math.min(
    50 / Math.max(originalLikes, 1),
    30 / Math.max(originalDownloads, 1),
    300 / Math.max(originalViews, 1)
  );

  const likes = Math.max(Math.floor(originalLikes * scaleFactor), 5);
  const downloads = Math.max(Math.floor(originalDownloads * scaleFactor), 2);
  const views = Math.max(Math.floor(originalViews * scaleFactor), likes + downloads + 10);

  return { likes, downloads, views };
}

// 检查日期是否在2024年6月之后
function isAfterJune2024(dateString) {
  if (!dateString) return false;

  try {
    const date = new Date(dateString);
    const june2024 = new Date('2024-06-01T00:00:00Z');
    return date >= june2024;
  } catch (e) {
    return false;
  }
}

// 将原始数据调整到合理范围
function adjustInteractionData(originalLikes, originalDownloads, originalViews) {
  // 如果原始数据在合理范围内，直接使用
  if (originalLikes <= 50 && originalDownloads <= 30 && originalViews <= 300) {
    return {
      likes: Math.max(originalLikes, 1),
      downloads: Math.max(originalDownloads, 1),
      views: Math.max(originalViews, originalLikes + originalDownloads + 10)
    };
  }

  // 如果原始数据过大，按比例缩放到合理范围
  const scaleFactor = Math.min(
    50 / Math.max(originalLikes, 1),
    30 / Math.max(originalDownloads, 1),
    300 / Math.max(originalViews, 1)
  );

  const likes = Math.max(Math.floor(originalLikes * scaleFactor), 5);
  const downloads = Math.max(Math.floor(originalDownloads * scaleFactor), 2);
  const views = Math.max(Math.floor(originalViews * scaleFactor), likes + downloads + 10);

  return { likes, downloads, views };
}

// 创建提示词数据
async function createPrompts(connection, promptsData) {
  console.log('开始处理PromptPort提示词数据...');
  
  // 获取现有用户（只选择ID ≤ 118的用户）
  const [users] = await connection.execute('SELECT id FROM users WHERE role = "user" AND id <= 118');
  if (users.length === 0) {
    throw new Error('数据库中没有符合条件的用户（ID ≤ 118），请先创建用户');
  }
  
  console.log(`👥 找到 ${users.length} 个现有用户`);
  
  // 获取已存在的提示词标题（避免重复）
  const [existingPrompts] = await connection.execute('SELECT title FROM prompts');
  const existingTitles = new Set(existingPrompts.map(p => p.title));
  
  const prompts = [];
  let successCount = 0;
  let skipCount = 0;
  
  for (const item of promptsData) {
    // 只使用zh字段下的内容
    if (!item.zh || !item.zh.title || !item.zh.prompt) {
      skipCount++;
      continue;
    }
    
    const title = item.zh.title;
    const content = item.zh.prompt;
    const description = item.zh.description || `来自${title}的AI提示词`;
    const topics = item.topics || [];

    // 过滤2024年6月之前的提示词
    if (!isAfterJune2024(item.created_at)) {
      console.log(`⚠️  跳过2024年6月前的数据: ${title}`);
      skipCount++;
      continue;
    }

    // 检查是否已存在
    if (existingTitles.has(title)) {
      console.log(`⚠️  跳过重复数据: ${title}`);
      skipCount++;
      continue;
    }
    
    // 随机分配用户
    const randomUser = users[Math.floor(Math.random() * users.length)];
    const userId = randomUser.id;
    
    // 智能分类
    const category = inferCategory(title, content, description, topics);
    
    // 调整互动数据到合理范围
    let likes, downloads, views;
    if (item.focus || item.copy || item.view) {
      // 如果有原始数据，调整到合理范围
      const adjusted = adjustInteractionData(
        item.focus || 0,
        item.copy || 0,
        item.view || 0
      );
      likes = adjusted.likes;
      downloads = adjusted.downloads;
      views = adjusted.views;
    } else {
      // 如果没有原始数据，生成随机数据
      const generated = generateInteractionData();
      likes = generated.likes;
      downloads = generated.downloads;
      views = generated.views;
    }
    
    // 处理创建时间
    let createdAt;
    if (item.created_at) {
      try {
        createdAt = new Date(item.created_at);
        if (isNaN(createdAt.getTime())) {
          createdAt = generateRandomTime();
        }
      } catch (e) {
        createdAt = generateRandomTime();
      }
    } else {
      createdAt = generateRandomTime();
    }
    
    prompts.push([
      userId,
      title,
      description,
      content,
      category,
      JSON.stringify(topics),
      false, // isPrivate
      'published', // status
      likes,
      downloads,
      views,
      createdAt,
      new Date() // updatedAt
    ]);
    
    existingTitles.add(title);
    successCount++;
    
    // 显示进度
    if (successCount % 100 === 0) {
      console.log(`📊 已处理 ${successCount} 条数据`);
    }
  }
  
  if (prompts.length === 0) {
    console.log('⚠️  没有有效的提示词数据需要导入');
    return [];
  }
  
  // 逐个插入提示词并显示详细日志
  console.log(`📝 开始逐个插入 ${prompts.length} 个提示词...`);

  const insertPromptQuery = `
    INSERT INTO prompts (userId, title, description, content, category, tags, isPrivate, status, likes, downloads, views, createdAt, updatedAt)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  let insertedCount = 0;
  for (let i = 0; i < prompts.length; i++) {
    const prompt = prompts[i];
    try {
      await connection.execute(insertPromptQuery, prompt);
      insertedCount++;
      console.log(`✅ [${insertedCount}/${prompts.length}] 成功插入: "${prompt[1]}" (分类: ${prompt[4]}, 用户: ${prompt[0]})`);

      // 每10个显示一次进度
      if (insertedCount % 10 === 0) {
        console.log(`📊 进度: ${insertedCount}/${prompts.length} (${(insertedCount/prompts.length*100).toFixed(1)}%)`);
      }
    } catch (error) {
      console.error(`❌ [${i+1}/${prompts.length}] 插入失败: "${prompt[1]}" - ${error.message}`);
    }
  }

  console.log(`✅ 完成插入: ${insertedCount}/${prompts.length} 个提示词`);
  
  console.log('\n📊 导入统计:');
  console.log(`✅ 成功导入: ${successCount} 个提示词`);
  console.log(`⚠️  跳过数据: ${skipCount} 个`);
  
  // 获取创建的提示词
  const [promptRows] = await connection.query('SELECT id, userId FROM prompts ORDER BY id DESC LIMIT ?', [prompts.length]);
  return promptRows.reverse();
}

// 显示分类统计
async function showCategoryStats(connection) {
  const [categoryStats] = await connection.execute(`
    SELECT category, COUNT(*) as count 
    FROM prompts 
    GROUP BY category 
    ORDER BY count DESC
  `);
  
  console.log('\n📊 分类统计:');
  categoryStats.forEach(stat => {
    console.log(`   ${stat.category}: ${stat.count} 个`);
  });
}

// 主函数
async function main() {
  console.log('🚀 PromptPort数据导入工具');
  console.log('==================================================');
  
  // 检查文件是否存在
  const filePath = path.join(__dirname, options.fileName);
  if (!fs.existsSync(filePath)) {
    console.error(`❌ 文件不存在: ${options.fileName}`);
    console.log('请确保JSON文件在bulk-import目录下');
    process.exit(1);
  }
  
  // 读取JSON数据
  console.log(`📄 读取PromptPort数据: ${options.fileName}`);
  let promptsData;
  try {
    const rawData = fs.readFileSync(filePath, 'utf8');
    promptsData = JSON.parse(rawData);
  } catch (error) {
    console.error('❌ JSON文件解析失败:', error.message);
    process.exit(1);
  }
  
  if (!Array.isArray(promptsData)) {
    console.error('❌ JSON数据格式错误，应该是数组格式');
    process.exit(1);
  }
  
  console.log(`📋 原始数据: ${promptsData.length} 条`);
  
  // 连接数据库
  console.log('🔗 连接数据库...');
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
  
  try {
    // 创建提示词
    await createPrompts(connection, promptsData);
    
    // 显示统计信息
    await showCategoryStats(connection);
    
    console.log('\n🎉 导入完成！');
    console.log('==================================================');
    
  } catch (error) {
    console.error('❌ 处理过程中发生错误:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行主函数
main().catch(console.error);

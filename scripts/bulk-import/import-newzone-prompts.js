const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置 - 生产环境配置
const dbConfig = {
  host: 'sh-cynosdbmysql-grp-knh8ui5y.sql.tencentcdb.com',
  user: 'root',
  password: 'Baby901221',
  database: 'prompthub_prod',
  port: 27410,
  charset: 'utf8mb4',
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000
};

// 标签到分类的智能映射
const tagToCategoryMapping = {
  // AI相关
  'ai': 'ai-assistant',
  'chatgpt': 'ai-assistant',
  'assistant': 'ai-assistant',
  'prompt': 'ai-assistant',
  
  // 写作相关
  'writing': 'copywriting',
  'content': 'copywriting',
  'copywriting': 'copywriting',
  'text': 'copywriting',
  'comments': 'copywriting',
  'blog': 'copywriting',
  'article': 'copywriting',
  
  // 编程相关
  'programming': 'programming',
  'code': 'programming',
  'development': 'programming',
  'software': 'programming',
  'tech': 'programming',
  'technical': 'programming',
  
  // 设计相关
  'design': 'design',
  'ui': 'design',
  'ux': 'design',
  'visual': 'design',
  'creative': 'design',
  
  // 教育相关
  'education': 'education',
  'learning': 'education',
  'teaching': 'education',
  'tutorial': 'education',
  'explain': 'education',
  
  // 商业分析相关
  'business': 'business-analysis',
  'analysis': 'business-analysis',
  'marketing': 'business-analysis',
  'strategy': 'business-analysis',
  'finance': 'business-analysis',
  
  // 数据分析相关
  'data': 'data-analysis',
  'analytics': 'data-analysis',
  'statistics': 'data-analysis',
  'research': 'data-analysis'
};

// 关键词到分类的映射（用于标题和描述中的关键词识别）
const keywordToCategoryMapping = {
  // AI助手类关键词
  '提示词': 'ai-assistant',
  '生成器': 'ai-assistant',
  'ChatGPT': 'ai-assistant',
  '助手': 'ai-assistant',
  '机器人': 'ai-assistant',
  
  // 写作类关键词  
  '写作': 'copywriting',
  '文案': 'copywriting',
  '内容': 'copywriting',
  '评论': 'copywriting',
  '博客': 'copywriting',
  '文章': 'copywriting',
  '新闻': 'copywriting',
  '故事': 'copywriting',
  '创作': 'copywriting',
  
  // 编程类关键词
  '编程': 'programming',
  '代码': 'programming',
  '开发': 'programming',
  '软件': 'programming',
  '技术': 'programming',
  '测试': 'programming',
  '架构': 'programming',
  
  // 设计类关键词
  '设计': 'design',
  '界面': 'design',
  '用户体验': 'design',
  '视觉': 'design',
  '创意': 'design',
  
  // 教育类关键词
  '教育': 'education',
  '学习': 'education',
  '教学': 'education',
  '解释': 'education',
  '指导': 'education',
  '培训': 'education',
  '课程': 'education',
  
  // 商业分析类关键词
  '商业': 'business-analysis',
  '营销': 'business-analysis',
  '市场': 'business-analysis',
  '策略': 'business-analysis',
  '管理': 'business-analysis',
  '分析': 'business-analysis',
  '咨询': 'business-analysis',
  
  // 生活方式类关键词
  '美食': 'lifestyle',
  '旅行': 'lifestyle',
  '健康': 'lifestyle',
  '生活': 'lifestyle',
  '娱乐': 'lifestyle',
  '休闲': 'lifestyle'
};

// 智能分类推断函数
function inferCategory(promptData) {
  const { tags = [], zh } = promptData;
  const { title = '', remark = '', description = '' } = zh || {};
  
  // 1. 优先根据标签匹配
  for (const tag of tags) {
    const normalizedTag = tag.toLowerCase();
    if (tagToCategoryMapping[normalizedTag]) {
      return tagToCategoryMapping[normalizedTag];
    }
  }
  
  // 2. 根据标题关键词匹配
  const allText = `${title} ${remark} ${description}`.toLowerCase();
  for (const [keyword, category] of Object.entries(keywordToCategoryMapping)) {
    if (allText.includes(keyword.toLowerCase())) {
      return category;
    }
  }
  
  // 3. 特殊规则推断
  if (title.includes('评论') || title.includes('评审')) {
    return 'copywriting';
  }
  
  if (title.includes('专家') || title.includes('顾问')) {
    return 'business-analysis';
  }
  
  if (title.includes('老师') || title.includes('讲解')) {
    return 'education';
  }
  
  // 4. 默认分类
  return 'ai-assistant';
}

// 获取随机数
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 获取随机数组元素
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// 生成过去的随机日期
function generatePastDate(daysAgo) {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo - getRandomInt(0, 1));
  date.setHours(getRandomInt(0, 23), getRandomInt(0, 59), getRandomInt(0, 59));
  return date;
}

// 获取现有用户（排除真实用户id>=118）
async function getExistingUsers(connection) {
  const [users] = await connection.query('SELECT id, username FROM users WHERE id < 118 ORDER BY id');
  console.log(`✅ 获取到 ${users.length} 个可用用户（排除真实用户id>=118）`);
  
  if (users.length === 0) {
    throw new Error('没有可用的用户来分配提示词！请确保数据库中有id<118的用户。');
  }
  
  return users;
}

// 处理NewZone格式的提示词数据
async function processNewZonePrompts(connection, users, promptsData) {
  console.log('开始处理NewZone格式提示词数据...');
  
  if (!Array.isArray(promptsData)) {
    throw new Error('提示词数据格式错误，应该是数组');
  }
  
  const prompts = [];
  const totalDays = 90; // 分布在过去90天
  const promptsPerDay = Math.ceil(promptsData.length / totalDays);
  
  let processedCount = 0;
  let skippedCount = 0;
  
  for (let i = 0; i < promptsData.length; i++) {
    const promptData = promptsData[i];
    
    // 验证数据完整性
    if (!promptData.zh || !promptData.zh.title || !promptData.zh.prompt) {
      console.log(`⚠️  跳过不完整的数据 (ID: ${promptData.id})`);
      skippedCount++;
      continue;
    }
    
    const { zh, tags = [], count = 0, website = '' } = promptData;
    const { title, prompt, remark = '', description = '' } = zh;
    
    // 检查是否已存在相同标题的提示词
    const [existing] = await connection.query(
      'SELECT id FROM prompts WHERE title = ?', 
      [title]
    );
    
    if (existing.length > 0) {
      console.log(`⚠️  跳过已存在的提示词: ${title}`);
      skippedCount++;
      continue;
    }
    
    const randomUser = getRandomElement(users);
    const dayIndex = Math.floor(i / promptsPerDay);
    
    // 智能推断分类
    const category = inferCategory(promptData);
    
    // 生成创建时间
    const createdAt = generatePastDate(dayIndex + getRandomInt(0, 3));
    
    // 处理标签
    let tagsJson = JSON.stringify(tags || []);
    
    // 基于原始count生成互动数据，但加入随机性
    const basePopularity = Math.min(count || 0, 1000); // 限制最大基础值
    const likes = Math.max(1, Math.floor(basePopularity * 0.02) + getRandomInt(1, 20));
    const copies = Math.max(1, Math.floor(basePopularity * 0.01) + getRandomInt(1, 15));
    const views = Math.max(likes + copies, Math.floor(basePopularity * 0.1) + getRandomInt(20, 100));
    
    // 构建描述（优先使用description，其次remark）
    const finalDescription = description || remark || `${title}相关的AI提示词`;
    
    prompts.push([
      randomUser.id,
      title,
      finalDescription,
      prompt,
      category,
      tagsJson,
      0, // isPrivate
      'published', // status
      likes,
      copies,
      views,
      createdAt,
      createdAt
    ]);
    
    processedCount++;
    
    // 每处理100条显示进度
    if (processedCount % 100 === 0) {
      console.log(`📊 已处理 ${processedCount} / ${promptsData.length} 条数据`);
    }
  }
  
  if (prompts.length === 0) {
    console.log('❌ 没有有效的提示词数据需要导入');
    return [];
  }
  
  // 批量插入
  const insertPromptQuery = `
    INSERT INTO prompts (
      userId, title, description, content, category, tags, 
      isPrivate, status, likes, downloads, views, createdAt, updatedAt
    ) VALUES ?
  `;
  
  await connection.query(insertPromptQuery, [prompts]);
  
  console.log(`✅ 成功导入 ${processedCount} 个提示词`);
  console.log(`⚠️  跳过 ${skippedCount} 个重复或无效数据`);
  
  // 返回新创建的提示词
  const [promptRows] = await connection.query(
    'SELECT id, userId, category FROM prompts ORDER BY id DESC LIMIT ?', 
    [prompts.length]
  );
  
  return promptRows.reverse();
}

// 生成随机互动数据
async function generateInteractions(connection, users, prompts) {
  if (prompts.length === 0) return;
  
  console.log('生成随机互动数据...');
  
  const likes = [];
  const favorites = [];
  
  for (const prompt of prompts) {
    // 基于提示词现有点赞数生成互动
    const [promptInfo] = await connection.query(
      'SELECT likes FROM prompts WHERE id = ?', 
      [prompt.id]
    );
    
    const targetLikes = promptInfo[0].likes;
    const shuffledUsers = [...users].sort(() => 0.5 - Math.random());
    
    // 生成点赞
    let actualLikes = 0;
    for (let i = 0; i < targetLikes && i < shuffledUsers.length; i++) {
      if (shuffledUsers[i].id !== prompt.userId) {
        likes.push([shuffledUsers[i].id, prompt.id, new Date()]);
        actualLikes++;
      }
    }
    
    // 生成收藏（点赞用户的30-50%也会收藏）
    const favoriteCount = Math.floor(actualLikes * (0.3 + Math.random() * 0.2));
    for (let i = 0; i < favoriteCount; i++) {
      if (shuffledUsers[i].id !== prompt.userId) {
        favorites.push([shuffledUsers[i].id, prompt.id, new Date()]);
      }
    }
  }
  
  // 批量插入点赞
  if (likes.length > 0) {
    await connection.query('INSERT INTO likes (userId, promptId, createdAt) VALUES ?', [likes]);
  }
  
  // 批量插入收藏
  if (favorites.length > 0) {
    await connection.query('INSERT INTO favorites (userId, promptId, createdAt) VALUES ?', [favorites]);
  }
  
  console.log(`✅ 生成了 ${likes.length} 个点赞，${favorites.length} 个收藏`);
}

// 更新统计数据
async function updateStatistics(connection) {
  console.log('更新统计数据...');
  
  // 更新提示词点赞数
  await connection.query(`
    UPDATE prompts p 
    SET likes = (SELECT COUNT(*) FROM likes l WHERE l.promptId = p.id)
  `);
  
  // 更新分类统计
  const categories = [
    'ai-assistant', 'copywriting', 'programming', 'design', 
    'data-analysis', 'business-analysis', 'education', 'lifestyle'
  ];
  
  for (const category of categories) {
    await connection.query(`
      UPDATE categories 
      SET prompt_count = (
        SELECT COUNT(*) FROM prompts WHERE category = ? AND status = 'published'
      ) 
      WHERE name = ?
    `, [category, category]);
  }
  
  console.log('✅ 统计数据更新完成');
}

// 显示分类分布统计
async function showCategoryStats(connection) {
  console.log('\n📊 分类分布统计:');
  console.log('='.repeat(40));
  
  const [categoryStats] = await connection.query(`
    SELECT 
      category,
      COUNT(*) as count,
      ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
    FROM prompts 
    WHERE status = 'published'
    GROUP BY category 
    ORDER BY count DESC
  `);
  
  for (const stat of categoryStats) {
    console.log(`${stat.category.padEnd(20)} ${stat.count.toString().padStart(4)} 条 (${stat.percentage}%)`);
  }
}

// 主函数
async function main() {
  let connection;
  
  try {
    console.log('🚀 NewZone提示词导入工具');
    console.log('='.repeat(50));
    
    // 检查数据文件
    const promptsFilePath = path.join(__dirname, 'newzone_prompts.json');
    if (!fs.existsSync(promptsFilePath)) {
      console.error('❌ 找不到 newzone_prompts.json 文件');
      process.exit(1);
    }
    
    // 读取数据
    console.log('📄 读取NewZone提示词数据...');
    const promptsData = JSON.parse(fs.readFileSync(promptsFilePath, 'utf8'));
    console.log(`📋 读取到 ${promptsData.length} 条提示词数据`);
    
    // 连接数据库
    console.log('🔗 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 获取现有用户
    const users = await getExistingUsers(connection);
    if (users.length === 0) {
      console.error('❌ 数据库中没有用户，请先创建用户');
      process.exit(1);
    }
    console.log(`👥 找到 ${users.length} 个现有用户`);
    
    // 处理提示词
    const prompts = await processNewZonePrompts(connection, users, promptsData);
    
    if (prompts.length > 0) {
      // 生成互动数据
      await generateInteractions(connection, users, prompts);
      
      // 更新统计
      await updateStatistics(connection);
      
      // 显示分类统计
      await showCategoryStats(connection);
    }
    
    // 显示最终统计
    const [promptCount] = await connection.query('SELECT COUNT(*) as count FROM prompts');
    const [likeCount] = await connection.query('SELECT COUNT(*) as count FROM likes');
    const [favoriteCount] = await connection.query('SELECT COUNT(*) as count FROM favorites');
    
    console.log('\n🎉 NewZone数据导入完成！');
    console.log('='.repeat(50));
    console.log(`📝 提示词总数: ${promptCount[0].count}`);
    console.log(`👍 点赞总数: ${likeCount[0].count}`);
    console.log(`⭐ 收藏总数: ${favoriteCount[0].count}`);
    console.log('='.repeat(50));
    
  } catch (error) {
    console.error('❌ 导入过程中发生错误:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔒 数据库连接已关闭');
    }
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main }; 
#!/bin/bash

echo "🚀 开始部署 PromptHub 分页功能到服务器..."

# 清理之前的打包文件
rm -f prompthub-deploy.tar.gz

echo "📦 创建部署包（排除不必要文件）..."

# 创建临时目录
mkdir -p deploy-temp
cd deploy-temp

# 复制需要的文件，排除 node_modules 和构建缓存
echo "📁 复制前端文件..."
mkdir -p frontend/web
# 复制所有源代码文件
cp -r ../frontend/web/src frontend/web/
cp -r ../frontend/web/public frontend/web/
# 复制配置文件
cp ../frontend/web/package.json frontend/web/
cp ../frontend/web/package-lock.json frontend/web/
cp ../frontend/web/next.config.* frontend/web/ 2>/dev/null || true
cp ../frontend/web/tailwind.config.js frontend/web/
cp ../frontend/web/postcss.config.mjs frontend/web/
cp ../frontend/web/tsconfig.json frontend/web/
cp ../frontend/web/components.json frontend/web/
cp ../frontend/web/.env.* frontend/web/ 2>/dev/null || true
# 确保 lib 目录存在
mkdir -p frontend/web/src/lib
# 检查并复制关键文件
if [ -f "../frontend/web/src/lib/utils.ts" ]; then
    cp ../frontend/web/src/lib/utils.ts frontend/web/src/lib/
else
    echo "⚠️  utils.ts 不存在，将在服务器上创建"
fi

echo "📁 复制后端文件..."
mkdir -p backend
cp -r ../backend/src backend/
cp -r ../backend/database backend/
cp ../backend/package.json backend/
cp ../backend/package-lock.json backend/
cp ../backend/.env.* backend/ 2>/dev/null || true

echo "📁 复制脚本文件..."
cp -r ../scripts .

# 检查打包大小
echo "📊 检查文件大小..."
du -sh .

# 创建压缩包
cd ..
tar -czf prompthub-deploy.tar.gz -C deploy-temp .

# 清理临时目录
rm -rf deploy-temp

# 显示压缩包大小
PACKAGE_SIZE=$(du -sh prompthub-deploy.tar.gz | cut -f1)
echo "📦 部署包大小: $PACKAGE_SIZE"

if [[ "$PACKAGE_SIZE" =~ ^[0-9]+M$ ]] && [[ ${PACKAGE_SIZE%M} -gt 50 ]]; then
    echo "⚠️  警告：部署包仍然很大 ($PACKAGE_SIZE)，请检查是否包含了不必要的文件"
    echo "📋 包内容："
    tar -tzf prompthub-deploy.tar.gz | head -20
    echo "..."
    exit 1
fi

echo "✅ 部署包创建成功: $PACKAGE_SIZE"

# 上传到服务器
echo "📤 上传到服务器..."
scp prompthub-deploy.tar.gz root@*************:/var/www/

echo "🔧 在服务器上部署..."
ssh root@************* << 'EOF'
cd /var/www

echo "📋 备份当前版本..."
if [ -d "frontend" ]; then
    cp -r frontend frontend-backup-$(date +%Y%m%d-%H%M)
fi
if [ -d "backend" ]; then
    cp -r backend backend-backup-$(date +%Y%m%d-%H%M)
fi

echo "🛑 停止服务..."
systemctl stop prompthub-frontend
systemctl stop prompthub-backend

echo "📦 解压新版本..."
tar -xzf prompthub-deploy.tar.gz

echo "🔧 安装前端依赖并构建..."
cd frontend/web
npm install
npm run build
cd ../..

echo "🔧 安装后端依赖..."
cd backend
npm install --production
cd ..

echo "🚀 启动服务..."
systemctl start prompthub-backend
sleep 5
systemctl start prompthub-frontend

echo "✅ 检查服务状态..."
if systemctl is-active --quiet prompthub-backend; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    journalctl -u prompthub-backend --no-pager -l | tail -10
fi

if systemctl is-active --quiet prompthub-frontend; then
    echo "✅ 前端服务启动成功"
else
    echo "❌ 前端服务启动失败"
    journalctl -u prompthub-frontend --no-pager -l | tail -10
fi



echo "🎉 部署完成！"
echo "🌐 访问 http://*************/admin 测试管理功能"
echo "🌐 访问 http://*************/ 查看网站首页"
echo "🔑 登录信息: admin / admin123"
EOF

echo "✅ 部署完成！"
echo "🌐 请访问 http://*************/ 查看网站"
echo "🔧 管理后台: http://*************/admin"

# 清理本地部署包
rm -f prompthub-deploy.tar.gz

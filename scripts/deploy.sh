#!/bin/bash

# PromptHub 一键部署脚本
# 适用于 Ubuntu 20.04/22.04 LTS

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法检测系统版本"
        exit 1
    fi
    
    . /etc/os-release
    if [[ "$ID" != "ubuntu" ]]; then
        log_warning "此脚本专为Ubuntu设计，其他系统可能需要手动调整"
    fi
    
    log_info "检测到系统: $PRETTY_NAME"
}

# 更新系统
update_system() {
    log_info "更新系统包..."
    sudo apt update && sudo apt upgrade -y
    log_success "系统更新完成"
}

# 安装Node.js
install_nodejs() {
    log_info "安装Node.js 18..."
    
    # 检查是否已安装
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_info "Node.js已安装: $NODE_VERSION"
        return
    fi
    
    # 安装Node.js 18
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    # 验证安装
    NODE_VERSION=$(node --version)
    NPM_VERSION=$(npm --version)
    log_success "Node.js安装完成: $NODE_VERSION, npm: $NPM_VERSION"
}

# 安装PM2
install_pm2() {
    log_info "安装PM2..."
    
    if command -v pm2 &> /dev/null; then
        log_info "PM2已安装"
        return
    fi
    
    sudo npm install -g pm2
    log_success "PM2安装完成"
}

# 安装Nginx
install_nginx() {
    log_info "安装Nginx..."
    
    if command -v nginx &> /dev/null; then
        log_info "Nginx已安装"
        return
    fi
    
    sudo apt install nginx -y
    sudo systemctl enable nginx
    sudo systemctl start nginx
    log_success "Nginx安装完成"
}

# 安装MySQL客户端
install_mysql_client() {
    log_info "安装MySQL客户端..."
    sudo apt install mysql-client -y
    log_success "MySQL客户端安装完成"
}

# 克隆项目代码
clone_project() {
    log_info "克隆项目代码..."
    
    if [[ -d "prompthub" ]]; then
        log_warning "项目目录已存在，跳过克隆"
        return
    fi
    
    # 这里需要替换为实际的Git仓库地址
    # git clone https://github.com/your-username/prompthub.git
    log_warning "请手动克隆项目代码到当前目录"
    log_info "git clone https://github.com/your-username/prompthub.git"
}

# 配置环境变量
setup_env() {
    log_info "配置环境变量..."
    
    # 后端环境变量
    if [[ ! -f "prompthub/backend/.env" ]]; then
        cat > prompthub/backend/.env << EOF
PORT=9000
NODE_ENV=production
DB_HOST=sh-cynosdbmysql-grp-knh8ui5y.sql.tencentcdb.com
DB_PORT=27410
DB_NAME=prompthub
DB_USER=prompthub_user
DB_PASSWORD=your_password
JWT_SECRET=$(openssl rand -base64 32)
EOF
        log_success "后端环境变量配置完成"
    else
        log_info "后端环境变量已存在"
    fi
    
    # 前端环境变量
    if [[ ! -f "prompthub/frontend/web/.env.local" ]]; then
        cat > prompthub/frontend/web/.env.local << EOF
NEXT_PUBLIC_API_URL=http://localhost:9000/api
EOF
        log_success "前端环境变量配置完成"
    else
        log_info "前端环境变量已存在"
    fi
}

# 部署后端
deploy_backend() {
    log_info "部署后端服务..."
    
    cd prompthub/backend
    
    # 安装依赖
    log_info "安装后端依赖..."
    npm install --production
    
    # 启动服务
    log_info "启动后端服务..."
    pm2 start src/server.js --name prompt-api
    pm2 startup
    pm2 save
    
    cd ../..
    log_success "后端部署完成"
}

# 部署前端
deploy_frontend() {
    log_info "部署前端应用..."
    
    cd prompthub/frontend/web
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm install
    
    # 构建项目
    log_info "构建前端项目..."
    npm run build
    
    # 复制静态文件到Nginx目录
    sudo mkdir -p /var/www/prompthub
    sudo cp -r .next/static/* /var/www/prompthub/ 2>/dev/null || true
    sudo cp -r public/* /var/www/prompthub/ 2>/dev/null || true
    
    cd ../../..
    log_success "前端部署完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."
    
    # 创建Nginx配置文件
    sudo tee /etc/nginx/sites-available/prompthub > /dev/null << EOF
server {
    listen 80;
    server_name localhost;
    
    # 前端静态文件
    location / {
        root /var/www/prompthub;
        try_files \$uri \$uri/ /index.html;
    }
    
    # API反向代理
    location /api/ {
        proxy_pass http://localhost:9000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/prompthub /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    sudo nginx -t
    sudo systemctl reload nginx
    
    log_success "Nginx配置完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        sudo ufw allow 22/tcp
        sudo ufw allow 80/tcp
        sudo ufw allow 443/tcp
        sudo ufw --force enable
        log_success "防火墙配置完成"
    else
        log_warning "UFW未安装，跳过防火墙配置"
    fi
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查PM2
    if pm2 list | grep -q "prompt-api"; then
        log_success "后端服务运行正常"
    else
        log_error "后端服务未运行"
    fi
    
    # 检查Nginx
    if systemctl is-active --quiet nginx; then
        log_success "Nginx服务运行正常"
    else
        log_error "Nginx服务未运行"
    fi
    
    # 检查端口
    if netstat -tlnp | grep -q ":80 "; then
        log_success "端口80已开放"
    else
        log_warning "端口80未开放"
    fi
    
    if netstat -tlnp | grep -q ":9000 "; then
        log_success "端口9000已开放"
    else
        log_warning "端口9000未开放"
    fi
}

# 显示部署信息
show_info() {
    log_success "部署完成！"
    echo
    echo "=========================================="
    echo "  PromptHub 部署信息"
    echo "=========================================="
    echo "前端地址: http://$(curl -s ifconfig.me)"
    echo "后端API: http://$(curl -s ifconfig.me):9000"
    echo "本地访问: http://localhost"
    echo
    echo "管理命令:"
    echo "  查看后端日志: pm2 logs prompt-api"
    echo "  重启后端:     pm2 restart prompt-api"
    echo "  查看服务状态: pm2 status"
    echo "  重启Nginx:    sudo systemctl restart nginx"
    echo
    echo "配置文件位置:"
    echo "  后端配置: $(pwd)/prompthub/backend/.env"
    echo "  前端配置: $(pwd)/prompthub/frontend/web/.env.local"
    echo "  Nginx配置: /etc/nginx/sites-available/prompthub"
    echo "=========================================="
}

# 主函数
main() {
    log_info "开始部署PromptHub..."
    
    check_root
    check_system
    update_system
    install_nodejs
    install_pm2
    install_nginx
    install_mysql_client
    clone_project
    setup_env
    deploy_backend
    deploy_frontend
    configure_nginx
    configure_firewall
    check_services
    show_info
    
    log_success "部署完成！请访问 http://$(curl -s ifconfig.me) 查看应用"
}

# 运行主函数
main "$@"

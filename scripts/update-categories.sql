-- 分类数据更新脚本
-- 用于修复数据库分类与前端显示不一致的问题

USE prompthub;

-- 备份现有分类数据（可选）
-- CREATE TABLE categories_backup AS SELECT * FROM categories;

-- 更新现有分类，使description字段存储中文显示名称
UPDATE categories SET 
  description = '生产力',
  icon = '⚡',
  color = '#10B981'
WHERE name = 'ai-assistant';

UPDATE categories SET 
  description = '创意写作',
  icon = '✍️',
  color = '#8B5CF6'
WHERE name = 'copywriting';

UPDATE categories SET 
  description = '编程开发',
  icon = '💻',
  color = '#3B82F6'
WHERE name = 'programming';

UPDATE categories SET 
  description = '图像生成',
  icon = '🎨',
  color = '#F59E0B'
WHERE name = 'design';

-- 插入缺失的分类
INSERT INTO categories (name, description, icon, color, prompt_count, sort_order, isActive) VALUES
('data-analysis', '数据分析', '📊', '#EF4444', 0, 5, 1),
('education', '教育学习', '📚', '#06B6D4', 0, 6, 1),
('business-analysis', '商业营销', '📈', '#EC4899', 0, 7, 1),
('lifestyle', '生活娱乐', '🎮', '#84CC16', 0, 8, 1)
ON DUPLICATE KEY UPDATE
  description = VALUES(description),
  icon = VALUES(icon),
  color = VALUES(color),
  sort_order = VALUES(sort_order);

-- 删除不再使用的分类（如果有）
-- DELETE FROM categories WHERE name = 'mcp-tools' AND prompt_count = 0;

-- 更新分类的提示词数量统计
UPDATE categories SET prompt_count = (
  SELECT COUNT(*) 
  FROM prompts 
  WHERE prompts.category = categories.name 
    AND prompts.status = 'published'
);

-- 查看更新后的结果
SELECT 
  id,
  name as '英文标识符',
  description as '中文显示名称',
  icon as '图标',
  color as '颜色',
  prompt_count as '提示词数量',
  sort_order as '排序',
  isActive as '是否激活'
FROM categories 
WHERE isActive = 1 
ORDER BY sort_order; 
# 职业专区页面关键词和标签配置

## 📋 概览

本文档汇总了PromptHub职业专区所有页面的搜索关键词和分类标签配置，用于数据配置和内容管理。

### 🎯 职业专区列表
- 🔵 程序员专区 (`/for/developers`)
- 🟣 内容创作者专区 (`/for/creators`)
- 🟢 学生专区 (`/for/students`)
- 🟠 设计师专区 (`/for/designers`)
- 🔵 人力资源专区 (`/for/hr`)
- 🔴 营销人员专区 (`/for/marketers`)

---

## 1. 🔵 程序员专区

**页面路径：** `/for/developers`  
**精选提示词搜索关键词：** `程序员`  
**查看全部链接：** `/prompts?search=程序员`

### 分类标签配置

#### 代码审查
- **描述：** 代码质量检查、重构建议、安全扫描工具
- **标签：** `代码审查`, `代码质量`, `代码重构`, `安全检查`, `最佳实践`, `代码优化`

#### 技术文档
- **描述：** API文档、技术方案、用户手册生成工具
- **标签：** `技术文档`, `API文档`, `技术方案`, `用户手册`, `技术写作`, `文档生成`

#### 问题调试
- **描述：** 错误诊断、性能分析、日志分析工具
- **标签：** `问题调试`, `错误诊断`, `性能分析`, `日志分析`, `问题排查`, `系统监控`

#### 架构设计
- **描述：** 系统架构、微服务、数据库设计工具
- **标签：** `架构设计`, `系统架构`, `微服务`, `数据库设计`, `技术选型`, `分布式系统`

---

## 2. 🟣 内容创作者专区

**页面路径：** `/for/creators`  
**精选提示词搜索关键词：** `内容创作者`  
**查看全部链接：** `/prompts?search=内容创作者`

### 分类标签配置

#### 文案写作
- **描述：** 爆款标题、产品文案、情感文案创作工具
- **标签：** `文案写作`, `标题优化`, `产品文案`, `情感文案`, `营销文案`, `品牌故事`

#### 视频脚本
- **描述：** 短视频、直播、教学视频脚本生成工具
- **标签：** `视频脚本`, `短视频`, `直播话术`, `教学视频`, `脚本创作`, `内容策划`

#### 社媒运营
- **描述：** 社交媒体内容规划、话题策划、品牌人设工具
- **标签：** `社媒运营`, `内容规划`, `话题策划`, `用户互动`, `社区运营`, `品牌人设`

#### SEO优化
- **描述：** SEO文章、关键词优化、内容聚合工具
- **标签：** `SEO优化`, `关键词布局`, `长尾词`, `内容聚合`, `搜索排名`, `SEO策略`

---

## 3. 🟢 学生专区

**页面路径：** `/for/students`  
**精选提示词搜索关键词：** `学生`  
**查看全部链接：** `/prompts?search=学生`

### 分类标签配置

#### 论文写作
- **描述：** 论文大纲、文献综述、引用格式规范工具
- **标签：** `论文写作`, `学术写作`, `论文大纲`, `文献综述`, `引用格式`, `学术规范`

#### 学习辅导
- **描述：** 学习计划、知识总结、难点解析工具
- **标签：** `学习辅导`, `学习计划`, `知识总结`, `学习笔记`, `难点解析`, `学习方法`

#### 考试备考
- **描述：** 模拟试题、答题技巧、复习计划工具
- **标签：** `考试备考`, `模拟考试`, `答题技巧`, `复习计划`, `考试策略`, `重点提取`

#### 文献翻译
- **描述：** 外文翻译、术语解释、摘要优化工具
- **标签：** `文献翻译`, `学术翻译`, `专业术语`, `外语学习`, `摘要翻译`, `翻译优化`

---

## 4. 🟠 设计师专区

**页面路径：** `/for/designers`  
**精选提示词搜索关键词：** `设计师`  
**查看全部链接：** `/prompts?search=设计师`

### 分类标签配置

#### 创意灵感
- **描述：** 设计概念、色彩搭配、趋势分析工具
- **标签：** `创意灵感`, `设计概念`, `色彩搭配`, `设计趋势`, `灵感启发`, `创意思维`

#### 设计说明
- **描述：** 设计理念、视觉元素、规范文档工具
- **标签：** `设计说明`, `设计理念`, `视觉元素`, `设计规范`, `创作思路`, `设计解释`

#### 客户沟通
- **描述：** 设计提案、修改回复、进度汇报工具
- **标签：** `客户沟通`, `设计提案`, `修改意见`, `项目进度`, `客户反馈`, `沟通技巧`

#### 品牌策划
- **描述：** 品牌故事、视觉识别、定位分析工具
- **标签：** `品牌策划`, `品牌故事`, `视觉识别`, `品牌定位`, `VI设计`, `品牌理念`

---

## 5. 🔵 人力资源专区

**页面路径：** `/for/hr`  
**精选提示词搜索关键词：** `人力资源`  
**查看全部链接：** `/prompts?search=人力资源`

### 分类标签配置

#### 招聘面试
- **描述：** JD撰写、面试问题、候选人评估工具
- **标签：** `招聘面试`, `职位描述`, `面试问题`, `候选人评估`, `JD撰写`, `人才筛选`

#### 培训管理
- **描述：** 培训方案、效果评估、学习路径工具
- **标签：** `培训管理`, `培训方案`, `员工发展`, `学习路径`, `技能提升`, `培训评估`

#### 绩效评估
- **描述：** 绩效模板、目标设定、反馈沟通工具
- **标签：** `绩效评估`, `目标设定`, `绩效反馈`, `考核标准`, `员工辅导`, `SMART原则`

#### 员工沟通
- **描述：** 员工关怀、离职面谈、团队建设工具
- **标签：** `员工沟通`, `员工关怀`, `离职面谈`, `团队建设`, `企业文化`, `员工活动`

---

## 6. 🔴 营销人员专区

**页面路径：** `/for/marketers`  
**精选提示词搜索关键词：** `营销人员`  
**查看全部链接：** `/prompts?search=营销人员`

### 分类标签配置

#### 电商运营
- **描述：** 产品描述、详情页文案、店铺运营专用工具
- **标签：** `电商`, `电商营销`, `产品描述`, `详情页`, `淘宝`, `京东`, `直播带货`, `电商运营`

#### 广告创意
- **描述：** 广告文案、创意策划、投放优化工具
- **标签：** `广告文案`, `信息流`, `slogan`, `Facebook`, `广告创意`, `品牌策划`

#### 用户分析
- **描述：** 用户画像、市场调研、数据分析工具
- **标签：** `用户画像`, `数据分析`, `市场调研`, `竞品分析`, `情感分析`, `用户反馈`

#### 营销策略
- **描述：** 营销规划、活动策划、推广方案工具
- **标签：** `营销策略`, `活动策划`, `节日营销`, `社媒运营`, `KOL营销`, `品牌推广`

---

## 📊 数据配置统计

### 🎯 搜索关键词汇总
```
程序员
内容创作者
学生
设计师
人力资源
营销人员
```

### 🏷️ 标签体系统计
- **职业数量：** 6个
- **分类数量：** 24个（每职业4个分类）
- **标签总数：** 144个（每分类6个标签）
- **覆盖场景：** 各职业核心工作场景全覆盖

### 📋 分类分布
| 职业 | 分类1 | 分类2 | 分类3 | 分类4 |
|------|-------|-------|-------|-------|
| 程序员 | 代码审查 | 技术文档 | 问题调试 | 架构设计 |
| 内容创作者 | 文案写作 | 视频脚本 | 社媒运营 | SEO优化 |
| 学生 | 论文写作 | 学习辅导 | 考试备考 | 文献翻译 |
| 设计师 | 创意灵感 | 设计说明 | 客户沟通 | 品牌策划 |
| 人力资源 | 招聘面试 | 培训管理 | 绩效评估 | 员工沟通 |
| 营销人员 | 电商运营 | 广告创意 | 用户分析 | 营销策略 |

---

## 💡 数据配置建议

### 1. 精选提示词配置
- **搜索匹配：** 确保提示词的标题、描述或内容中包含对应的职业关键词
- **质量要求：** 精选提示词应该是该职业领域的高质量、高使用率内容
- **数量建议：** 每个职业至少配置10-20个精选提示词

### 2. 分类标签配置
- **标签匹配：** 创建提示词时根据内容特点选择对应的标签
- **多标签支持：** 一个提示词可以包含多个相关标签，提高搜索匹配度
- **标签权重：** 主要标签应该与提示词内容高度相关

### 3. 内容质量要求
- **专业性：** 提示词内容应该符合对应职业的专业需求
- **实用性：** 提供真正能解决工作问题的AI提示词
- **完整性：** 每个分类下都应该有足够的提示词数量

### 4. SEO优化建议
- **关键词密度：** 在提示词标题和描述中合理使用职业关键词
- **长尾关键词：** 结合具体工作场景的长尾关键词
- **内容相关性：** 确保标签与内容的高度相关性

---

## 🔧 技术实现说明

### API调用方式
```javascript
// 精选提示词获取
GET /api/prompts/public?search={职业关键词}&sortBy=downloads&limit=3

// 分类提示词获取
GET /api/prompts/public?tags={标签列表}&sortBy=downloads&limit=3
```

### 页面跳转链接
```javascript
// 查看全部链接格式
/prompts?search={职业关键词}

// 示例
/prompts?search=程序员
/prompts?search=内容创作者
```

### 标签使用规范
- 标签名称使用中文
- 每个标签长度控制在2-6个字符
- 避免重复和冗余标签
- 保持标签的语义清晰性

---

## 📝 维护说明

### 定期更新
- **内容审核：** 定期审核提示词质量和相关性
- **标签优化：** 根据用户搜索行为优化标签配置
- **数据分析：** 分析各职业页面的访问和使用数据

### 扩展建议
- **新职业：** 可根据用户需求增加新的职业专区
- **细分领域：** 在现有职业下增加更细分的专业领域
- **标签丰富：** 根据内容发展增加新的相关标签

---

*最后更新时间：2025年1月*
*文档版本：v1.0*

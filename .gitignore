# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Build outputs
build/
dist/
out/

# Database
*.sqlite
*.sqlite3
*.db

# Logs
logs
*.log

# Runtime data
tmp/
temp/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Local development
.env.local
config/local.json

# Test files
test-results/
playwright-report/
test-results.xml

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Package manager lock files (optional - choose one)
package-lock.json
yarn.lock
pnpm-lock.yaml

# Generated files
generated/
auto-generated/

# Certificate files
*.pem
*.key
*.crt

# Prisma
prisma/migrations/migration_lock.toml

# Browser extension specific
frontend/browser-extension/dist/
frontend/browser-extension/web-ext-artifacts/

# Frontend build outputs
frontend/web/dist/
frontend/web/build/ 

# Development temporary files
*_REPORT.md
*_STATUS.md
*_GUIDE.md
*_COMPLETE.md
*_FIXED.md
*_FIX.md
*_PROBLEM*.md
*_SOLUTION.md
*_UPDATE*.md
*test*.js
*simple*.js
*init*.js

# API and Database temporary files
api.log
database.db
*.sqlite*

# macOS system files
**/.DS_Store
**/._*

# Development scripts output
scripts/output/ 
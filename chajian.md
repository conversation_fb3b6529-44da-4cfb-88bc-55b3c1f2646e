<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词助手 (按钮可拖动最终版)</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* 自定义样式 */
        body { font-family: 'Inter', sans-serif; background-color: #f9fafb; }
        #prompt-sidebar {
            position: fixed;
            top: 0;
            right: 0;
            transition: transform 0.3s ease-in-out;
            will-change: transform;
        }
        .sidebar-hidden {
            transform: translateX(100%);
            pointer-events: none;
        }
        /* [更新] 拖动相关样式现在应用于悬浮按钮 */
        #sidebar-toggle-btn {
            position: fixed;
            will-change: top, left;
            transition: transform 0.3s ease-in-out;
        }
        .is-dragging {
            cursor: grabbing;
            user-select: none;
            transition: none; /* 拖动时移除过渡效果 */
        }
        .is-snapped {
            transition: transform 0.3s ease-in-out, left 0.3s ease-in-out, right 0.3s ease-in-out;
        }
        .is-snapped-right {
            transform: translateX(50%);
        }
        .is-snapped-right:hover {
            transform: translateX(0);
        }
        .is-snapped-left {
            transform: translateX(-50%);
        }
        .is-snapped-left:hover {
            transform: translateX(0);
        }
        .modal-backdrop { background-color: rgba(0, 0, 0, 0.5); backdrop-filter: blur(4px); }
        .no-scroll { overflow: hidden; }
        .btn-primary { background-image: linear-gradient(98deg, #377dff 0%, #a06eff 100%); color: white; transition: all 0.3s ease; box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.1); }
        .btn-primary:hover { box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.15); transform: translateY(-1px); }
        .btn-secondary { background-color: #f3f4f6; color: #374151; border: 1px solid #e5e7eb; }
        .btn-secondary:hover { background-color: #e5e7eb; }
        .active-tab { border-bottom-color: #6d28d9; color: #6d28d9; font-weight: 600; }
        .toggle-checkbox:checked { right: 0; border-color: #6d28d9; }
        .toggle-checkbox:checked + .toggle-label { background-color: #6d28d9; }
        .dropdown-menu { transition: opacity 0.2s ease-out, transform 0.2s ease-out; }
        .loader { border: 2px solid #f3f3f3; border-top: 2px solid #6d28d9; border-radius: 50%; width: 24px; height: 24px; animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <!-- 模拟网页内容 -->
    <div class="p-8 max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-4 text-gray-800">PromptHub 模拟主页</h1>
        <p class="text-gray-600 leading-relaxed">悬浮按钮现在可以拖动和边缘吸附，设置功能已恢复，滑动列表功能已修复。</p>
    </div>

    <!-- 悬浮按钮 (现在可拖动) -->
    <div id="sidebar-toggle-btn" class="fixed bottom-8 right-8 z-50 w-14 h-14 rounded-full flex items-center justify-center cursor-grab btn-primary">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-white pointer-events-none" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" /></svg>
    </div>

    <!-- 插件侧边栏主体 (h-full 保证 flexbox 正确计算高度) -->
    <aside id="prompt-sidebar" class="sidebar-hidden h-full w-96 bg-white shadow-lg z-40 flex flex-col rounded-l-lg">
        <header id="sidebar-header" class="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0"></header>
        
        <!-- [恢复] 设置面板 -->
        <div id="settings-panel" class="hidden p-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-md font-semibold text-gray-700 mb-3">设置</h3>
            <div class="flex items-center justify-between">
                <label for="toggle-floating-btn" class="text-sm text-gray-600">显示悬浮按钮</label>
                <div class="relative inline-block w-10 mr-2 align-middle select-none"><input type="checkbox" name="toggle" id="toggle-floating-btn" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" checked/><label for="toggle-floating-btn" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label></div>
            </div>
        </div>

        <div class="p-4 border-b border-gray-200"><div class="relative"><input id="search-input" type="text" placeholder="搜索..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg></div></div></div>
        <nav class="flex border-b border-gray-200 px-4">
            <button data-tab="my-prompts" class="tab-btn flex-1 py-3 text-sm font-medium text-center border-b-2 active-tab">我的提示词</button>
            <button data-tab="public-prompts" class="tab-btn flex-1 py-3 text-sm font-medium text-center border-b-2 border-transparent text-gray-500 hover:text-gray-700">公共提示词</button>
        </nav>
        <main id="prompt-list-container" class="flex-1 overflow-y-auto p-4 space-y-3 bg-gray-50"></main>
        <div id="loading-indicator" class="hidden h-16 flex items-center justify-center"><div class="loader"></div></div>
    </aside>

    <!-- 模态框等 -->
    <div id="use-prompt-modal" class="hidden fixed inset-0 z-50 flex items-center justify-center modal-backdrop"><div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 flex flex-col" style="height: 75vh; max-height: 800px;"><div class="p-5 border-b"><h2 class="text-xl font-bold text-gray-900">使用提示词</h2></div><div class="flex-1 p-5 space-y-4 overflow-y-hidden flex flex-col"><div class="flex-shrink-0"><label class="block text-sm font-semibold text-gray-700 mb-2">系统提示词 (可编辑)</label><textarea id="system-prompt-input" rows="5" class="w-full p-3 bg-gray-100 rounded-md text-gray-700 text-sm border-gray-200 border focus:outline-none focus:ring-2 focus:ring-purple-400"></textarea></div><div class="flex-grow flex flex-col"><label for="user-prompt-input" class="block text-sm font-semibold text-gray-700 mb-2 flex-shrink-0">你的需求</label><textarea id="user-prompt-input" class="w-full h-full flex-grow p-3 bg-white rounded-md text-gray-800 border-gray-300 border focus:outline-none focus:ring-2 focus:ring-purple-400" placeholder="请在这里输入你的具体要求..."></textarea></div></div><div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3 border-t rounded-b-lg"><button type="button" id="use-modal-cancel-btn" class="py-2 px-4 rounded-md text-sm font-medium btn-secondary">取消</button><button type="button" id="use-modal-copy-btn" class="py-2 px-4 rounded-md text-sm font-medium btn-primary">复制组合内容</button></div></div></div>
    <div id="copy-feedback" class="hidden fixed top-5 left-1/2 -translate-x-1/2 bg-gray-900 text-white px-4 py-2 rounded-md shadow-lg text-sm"></div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {

        // --- DOM Elements ---
        const sidebar = document.getElementById('prompt-sidebar');
        const toggleBtn = document.getElementById('sidebar-toggle-btn');
        const sidebarHeader = document.getElementById('sidebar-header');
        const promptListContainer = document.getElementById('prompt-list-container');
        const searchInput = document.getElementById('search-input');
        const tabs = document.querySelectorAll('.tab-btn');
        const settingsPanel = document.getElementById('settings-panel');
        const toggleFloatingBtn = document.getElementById('toggle-floating-btn');
        const usePromptModal = document.getElementById('use-prompt-modal');
        const systemPromptInput = document.getElementById('system-prompt-input');
        const userPromptInput = document.getElementById('user-prompt-input');
        const useModalCancelBtn = document.getElementById('use-modal-cancel-btn');
        const useModalCopyBtn = document.getElementById('use-modal-copy-btn');
        const copyFeedback = document.getElementById('copy-feedback');
        const loadingIndicator = document.getElementById('loading-indicator');

        // --- Mock Data ---
        const generateMockData = (count, prefix) => Array.from({ length: count }, (_, i) => ({
            id: i + 1,
            title: `${prefix}提示词 #${i + 1}`,
            content: `这是${prefix}提示词 #${i + 1} 的详细内容。它旨在帮助用户解决特定领域的问题，通过结构化的指令引导AI模型生成高质量的输出。`
        }));
        
        const allMyPrompts = generateMockData(30, '我的');
        const allPublicPrompts = generateMockData(50, '公共');

        // --- State ---
        let isLoggedIn = false;
        let currentTab = 'public-prompts';
        const PAGE_SIZE = 6;
        let pagesLoaded = { my: 0, public: 0 };
        let isLoading = false;
        let hasMore = { my: true, public: true };

        // --- Core Functions ---
        const renderHeader = () => {
            const brandHTML = `<div class="flex items-center"><svg class="w-8 h-8 mr-2 text-purple-600" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path d="M512 0C229.2 0 0 229.2 0 512s229.2 512 512 512 512-229.2 512-512S794.8 0 512 0zm213.2 266.8l-184.4 74.8c-4.8 2-8.8 5.6-11.6 10.4-11.2 18.8-28.8 47.6-32.4 54-2.8 4.8-10.4 4.8-13.2 0-3.6-6.4-21.2-35.2-32.4-54-2.8-4.8-6.8-8.4-11.6-10.4l-184.4-74.8c-14-5.6-22 12-12.4 23.6l121.2 148.8c4.4 5.2 6.8 12 6.8 18.8v229.2c0 14 16.4 21.6 27.2 12l104.8-93.2c4.8-4.4 12-4.4 16.8 0l104.8 93.2c10.8 9.6 27.2 2 27.2-12V458c0-6.8 2.4-13.6 6.8-18.8l121.2-148.8c9.6-11.6 1.6-29.2-12.4-23.6z"></path></svg><h1 class="text-lg font-bold text-gray-800">PromptHub 助手</h1></div>`;
            let rightSideHTML;
            if (isLoggedIn) {
                // [恢复] 设置菜单项
                rightSideHTML = `<div class="relative"><button id="user-menu-btn" class="flex items-center"><img src="https://placehold.co/32x32/E9D5FF/6D28D9?text=P" alt="User Avatar" class="w-8 h-8 rounded-full"></button><div id="user-dropdown" class="dropdown-menu hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 origin-top-right ring-1 ring-black ring-opacity-5 focus:outline-none transform opacity-0 scale-95"><div class="py-1"><a href="#add-on-website" target="_blank" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">新增提示词</a><a href="#" id="settings-menu-item" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">设置</a><div class="border-t border-gray-100 my-1"></div><a href="#" id="logout-btn" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50">退出登录</a></div></div></div>`;
            } else {
                rightSideHTML = `<button id="login-btn" class="btn-primary text-sm font-semibold px-4 py-1.5 rounded-md">登录</button>`;
            }
            sidebarHeader.innerHTML = `${brandHTML}<div class="flex items-center space-x-2">${rightSideHTML}<button id="sidebar-close-btn" class="text-gray-500 hover:text-red-600 p-2 rounded-full hover:bg-red-50" title="关闭">&times;</button></div>`;
            addHeaderEventListeners();
        };

        const appendPrompts = (promptsToAppend) => {
            const html = promptsToAppend.map(prompt => {
                const myPromptsButtons = `<a href="#edit-on-website/${prompt.id}" target="_blank" data-action="edit-link" class="text-sm font-medium text-gray-600 hover:text-purple-600">编辑</a><button data-action="copy" class="text-sm font-medium btn-secondary py-1.5 px-3 rounded-md">复制</button><button data-action="use" class="rounded-md px-4 py-1.5 text-sm font-semibold flex items-center btn-primary">使用</button>`;
                const publicPromptsButtons = `<button data-action="copy" class="text-sm font-medium btn-secondary py-1.5 px-3 rounded-md">复制</button><button data-action="use" class="rounded-md px-4 py-1.5 text-sm font-semibold flex items-center btn-primary">使用</button>`;
                return `<div class="card bg-white p-3 rounded-lg border border-gray-200 shadow-sm" data-id="${prompt.id}"><div class="card-body cursor-pointer" data-action="use"><h3 class="font-bold text-gray-800 mb-1.5 text-sm pointer-events-none">${prompt.title}</h3><p class="text-gray-500 text-xs mb-3 line-clamp-2 pointer-events-none">${prompt.content}</p></div><div class="flex items-center justify-end space-x-3 pt-2 border-t border-gray-100">${currentTab === 'my-prompts' ? myPromptsButtons : publicPromptsButtons}</div></div>`;
            }).join('');
            promptListContainer.insertAdjacentHTML('beforeend', html);
        };
        
        const fetchPrompts = async (page) => {
            isLoading = true; loadingIndicator.classList.remove('hidden');
            return new Promise(resolve => {
                setTimeout(() => {
                    const source = currentTab === 'my-prompts' ? allMyPrompts : allPublicPrompts;
                    const start = (page - 1) * PAGE_SIZE; const end = start + PAGE_SIZE;
                    const newPrompts = source.slice(start, end);
                    isLoading = false; loadingIndicator.classList.add('hidden');
                    const currentHasMoreKey = currentTab === 'my-prompts' ? 'my' : 'public';
                    if (newPrompts.length < PAGE_SIZE) { hasMore[currentHasMoreKey] = false; }
                    resolve(newPrompts);
                }, 500);
            });
        };

        const loadInitialPrompts = async () => {
            promptListContainer.innerHTML = '';
            const pageKey = currentTab === 'my-prompts' ? 'my' : 'public';
            pagesLoaded[pageKey] = 1; hasMore[pageKey] = true;
            if (currentTab === 'my-prompts' && !isLoggedIn) {
                promptListContainer.innerHTML = `<div class="text-center text-gray-500 py-16 px-4"><svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1"><path stroke-linecap="round" stroke-linejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" /></svg><h3 class="mt-2 text-lg font-medium text-gray-900">请先登录</h3><p class="mt-1 text-sm text-gray-500">登录后即可查看和管理你的个人提示词。</p><div class="mt-6"><button type="button" id="login-in-content-btn" class="btn-primary text-sm font-semibold px-4 py-2 rounded-md">前往登录</button></div></div>`;
                document.getElementById('login-in-content-btn').addEventListener('click', simulateLogin); return;
            }
            const prompts = await fetchPrompts(1); appendPrompts(prompts);
        };
        
        const switchTab = (tabName) => {
            if (tabName === 'my-prompts' && !isLoggedIn) { showCopyFeedback("请先登录以查看“我的提示词”", true); return; }
            currentTab = tabName;
            tabs.forEach(tab => { tab.classList.toggle('active-tab', tab.dataset.tab === tabName); tab.classList.toggle('border-transparent', tab.dataset.tab !== tabName); tab.classList.toggle('text-gray-500', tab.dataset.tab !== tabName); });
            loadInitialPrompts();
        };

        const showCopyFeedback = (message = "已复制到剪贴板!", isError = false) => { copyFeedback.textContent = message; copyFeedback.className = `fixed top-5 left-1/2 -translate-x-1/2 text-white px-4 py-2 rounded-md shadow-lg text-sm ${isError ? 'bg-red-600' : 'bg-gray-900'}`; copyFeedback.classList.remove('hidden'); setTimeout(() => copyFeedback.classList.add('hidden'), 2500); };
        const copyToClipboard = (text) => { const textarea = document.createElement('textarea'); textarea.value = text; document.body.appendChild(textarea); textarea.select(); try { document.execCommand('copy'); showCopyFeedback(); } catch (err) { showCopyFeedback('复制失败', true); } document.body.removeChild(textarea); };
        const openModal = (modal) => { modal.classList.remove('hidden'); document.body.classList.add('no-scroll'); };
        const closeModal = (modal) => { modal.classList.add('hidden'); document.body.classList.remove('no-scroll'); };
        const showUsePromptModal = (prompt) => { systemPromptInput.value = prompt.content; userPromptInput.value = ''; openModal(usePromptModal); };
        const simulateLogin = () => { isLoggedIn = true; switchTab('my-prompts'); renderHeader(); };
        const simulateLogout = () => { isLoggedIn = false; switchTab('public-prompts'); renderHeader(); };
        
        // --- Event Listeners ---
        const openSidebar = () => {
            sidebar.classList.remove('sidebar-hidden');
            toggleBtn.style.display = 'none'; // Use display none to remove it from layout
        }
        const closeSidebar = () => {
            sidebar.classList.add('sidebar-hidden');
            if (toggleFloatingBtn.checked) {
                toggleBtn.style.display = 'flex';
            }
        }

        const addHeaderEventListeners = () => {
            document.getElementById('sidebar-close-btn').addEventListener('click', closeSidebar);
            const loginBtn = document.getElementById('login-btn');
            if (loginBtn) loginBtn.addEventListener('click', simulateLogin);
            const userMenuBtn = document.getElementById('user-menu-btn');
            if (userMenuBtn) {
                const dropdown = document.getElementById('user-dropdown');
                userMenuBtn.addEventListener('click', (e) => { e.stopPropagation(); const isHidden = dropdown.classList.contains('hidden'); if (isHidden) { dropdown.classList.remove('hidden', 'opacity-0', 'scale-95'); dropdown.classList.add('opacity-100', 'scale-100'); } else { dropdown.classList.add('opacity-0', 'scale-95'); setTimeout(() => dropdown.classList.add('hidden'), 200); }});
                document.getElementById('settings-menu-item').addEventListener('click', () => settingsPanel.classList.toggle('hidden'));
                document.getElementById('logout-btn').addEventListener('click', simulateLogout);
            }
        };
        
        document.addEventListener('click', (e) => { const dropdown = document.getElementById('user-dropdown'); const userMenuBtn = document.getElementById('user-menu-btn'); if (dropdown && !dropdown.classList.contains('hidden') && userMenuBtn && !userMenuBtn.contains(e.target)) { dropdown.classList.add('opacity-0', 'scale-95'); setTimeout(() => dropdown.classList.add('hidden'), 200); }});
        
        toggleBtn.addEventListener('click', (e) => {
            // Prevent click from firing during drag
            if(e.detail > 0) openSidebar();
        });
        
        tabs.forEach(tab => tab.addEventListener('click', () => switchTab(tab.dataset.tab)));
        searchInput.addEventListener('input', () => { loadInitialPrompts(); });
        toggleFloatingBtn.addEventListener('change', (e) => {
            toggleBtn.style.display = e.target.checked ? 'flex' : 'none';
        });

        promptListContainer.addEventListener('click', (e) => {
            const target = e.target; const actionTarget = target.closest('[data-action]'); if (!actionTarget) return;
            const card = target.closest('.card'); const promptId = parseInt(card.dataset.id);
            const sourceData = currentTab === 'my-prompts' ? allMyPrompts : allPublicPrompts;
            const prompt = sourceData.find(p => p.id === promptId);
            const action = actionTarget.dataset.action;
            if (action === 'use') { showUsePromptModal(prompt); } 
            else if (action === 'copy') { copyToClipboard(prompt.content); }
        });
        
        useModalCancelBtn.addEventListener('click', () => closeModal(usePromptModal));
        useModalCopyBtn.addEventListener('click', () => { const combinedPrompt = `${systemPromptInput.value}\n\n---\n\n${userPromptInput.value}`; copyToClipboard(combinedPrompt); closeModal(usePromptModal); });
        
        // --- Draggable Button Logic ---
        let isDragging = false;
        let offsetX, offsetY;
        let clickTimeout;

        toggleBtn.addEventListener('mousedown', (e) => {
            isDragging = false;
            clickTimeout = setTimeout(() => { // Differentiate click from drag
                isDragging = true;
                toggleBtn.classList.add('is-dragging');
                offsetX = e.clientX - toggleBtn.getBoundingClientRect().left;
                offsetY = e.clientY - toggleBtn.getBoundingClientRect().top;
                document.addEventListener('mousemove', onMouseMove);
            }, 150);
            
            document.addEventListener('mouseup', onMouseUp, { once: true });
        });

        function onMouseMove(e) {
            if (!isDragging) return;
            toggleBtn.classList.remove('is-snapped', 'is-snapped-left', 'is-snapped-right');
            let newX = e.clientX - offsetX;
            let newY = e.clientY - offsetY;
            
            const btnRect = toggleBtn.getBoundingClientRect();
            const maxX = window.innerWidth - btnRect.width;
            const maxY = window.innerHeight - btnRect.height;

            newX = Math.max(0, Math.min(newX, maxX));
            newY = Math.max(0, Math.min(newY, maxY));
            
            toggleBtn.style.left = `${newX}px`;
            toggleBtn.style.top = `${newY}px`;
            toggleBtn.style.right = 'auto';
            toggleBtn.style.bottom = 'auto';
            toggleBtn.style.transform = 'none';
        }

        function onMouseUp(e) {
            clearTimeout(clickTimeout);
            if (!isDragging) return;
            
            isDragging = false;
            toggleBtn.classList.remove('is-dragging');
            document.removeEventListener('mousemove', onMouseMove);

            const btnRect = toggleBtn.getBoundingClientRect();
            const snapThreshold = 50;

            if (btnRect.left < snapThreshold) {
                toggleBtn.classList.add('is-snapped', 'is-snapped-left');
                toggleBtn.style.left = `0px`;
            } else if (window.innerWidth - btnRect.right < snapThreshold) {
                toggleBtn.classList.add('is-snapped', 'is-snapped-right');
                toggleBtn.style.left = `${window.innerWidth - btnRect.width}px`;
            }
        }

        // Infinite scroll observer
        const observer = new IntersectionObserver(async (entries) => {
            if (entries[0].isIntersecting && !isLoading) {
                const pageKey = currentTab === 'my-prompts' ? 'my' : 'public';
                if(hasMore[pageKey]) { pagesLoaded[pageKey]++; const newPrompts = await fetchPrompts(pagesLoaded[pageKey]); if (newPrompts.length > 0) { appendPrompts(newPrompts); } }
            }
        }, { root: promptListContainer, rootMargin: "0px", threshold: 0.1 });
        observer.observe(loadingIndicator);
        
        // --- Initial Render ---
        renderHeader();
        switchTab(isLoggedIn ? 'my-prompts' : 'public-prompts');
    });
    </script>
</body>
</html>

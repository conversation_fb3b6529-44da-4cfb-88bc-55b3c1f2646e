# PromptHub 开发指南

## 📋 目录
- [开发环境搭建](#开发环境搭建)
- [项目架构](#项目架构)
- [开发规范](#开发规范)
- [分阶段开发计划](#分阶段开发计划)
- [技术选型说明](#技术选型说明)

## 🛠️ 开发环境搭建

### 1. 环境要求
- **Node.js**: >= 18.0.0
- **Docker**: >= 20.0.0
- **Git**: 最新版本
- **VS Code**: 推荐 IDE

### 2. 克隆项目
```bash
git clone https://github.com/your-org/prompt-hub.git
cd prompt-hub
```

### 3. 安装依赖
```bash
# 安装根目录依赖
npm install

# 安装所有子项目依赖
npm run install:all
```

### 4. 启动开发环境
```bash
# 启动基础服务 (数据库、Redis、Elasticsearch等)
npm run docker:up

# 启动后端 API 开发服务器
npm run dev:api

# 启动前端网站开发服务器
npm run dev:web

# 启动浏览器插件开发
npm run dev:extension
```

### 5. 访问服务
- **前端网站**: http://localhost:3000
- **后端 API**: http://localhost:8000
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
- **Elasticsearch**: http://localhost:9200
- **Kibana**: http://localhost:5601
- **MinIO**: http://localhost:9001
- **MailHog**: http://localhost:8025

## 🏗️ 项目架构

### 前端架构
```
frontend/
├── web/                    # React 网站
│   ├── src/
│   │   ├── components/     # 可复用组件
│   │   ├── pages/         # 页面组件
│   │   ├── hooks/         # 自定义 Hooks
│   │   ├── store/         # 状态管理
│   │   ├── services/      # API 服务
│   │   ├── utils/         # 工具函数
│   │   └── types/         # TypeScript 类型定义
│   └── public/            # 静态资源
└── browser-extension/      # Vue 浏览器插件
    ├── src/
    │   ├── components/     # 插件组件
    │   ├── content/       # Content Scripts
    │   ├── background/    # Background Scripts
    │   ├── popup/         # 插件弹窗
    │   └── options/       # 插件设置页
    └── manifest/          # 插件配置文件
```

### 后端架构
```
backend/
├── api/                   # 主 API 服务
│   ├── src/
│   │   ├── routes/        # 路由定义
│   │   ├── controllers/   # 控制器
│   │   ├── services/      # 业务逻辑
│   │   ├── models/        # 数据模型
│   │   ├── middleware/    # 中间件
│   │   ├── utils/         # 工具函数
│   │   └── types/         # TypeScript 类型
│   └── config/            # 配置文件
└── services/              # 微服务模块
    ├── auth/              # 认证服务
    ├── content/           # 内容管理
    ├── search/            # 搜索服务
    └── notification/      # 通知服务
```

## 📏 开发规范

### 1. 代码规范
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **TypeScript**: 类型安全
- **Husky**: Git Hooks 管理

### 2. 命名规范
- **文件名**: kebab-case (user-profile.tsx)
- **组件名**: PascalCase (UserProfile)
- **变量名**: camelCase (userProfile)
- **常量名**: UPPER_SNAKE_CASE (API_BASE_URL)

### 3. Git 规范
```bash
# 提交信息格式
feat: 添加用户登录功能
fix: 修复提示词搜索bug
docs: 更新API文档
style: 代码格式调整
refactor: 重构用户模块
test: 添加单元测试
chore: 更新依赖包
```

### 4. 分支管理
- **main**: 主分支，用于生产环境
- **develop**: 开发分支，用于集成测试
- **feature/**: 功能分支
- **bugfix/**: 修复分支
- **release/**: 发布分支

## 🚀 分阶段开发计划

### Phase 1: MVP 版本 (3个月)

#### 第1个月：基础架构搭建
**目标**: 搭建完整的开发环境和基础架构

**后端任务**:
- [ ] 数据库设计和建表
- [ ] 用户认证系统 (JWT)
- [ ] 基础 API 框架 (Fastify + TypeScript)
- [ ] 数据库连接和 ORM 设置
- [ ] 基础中间件 (日志、错误处理、CORS)

**前端任务**:
- [ ] React 项目初始化
- [ ] 基础路由配置
- [ ] UI 组件库选择和配置
- [ ] 全局状态管理设置
- [ ] API 客户端配置

**技术细节**:
```typescript
// 后端 API 结构示例
// backend/api/src/routes/auth.ts
import { FastifyInstance } from 'fastify'

export async function authRoutes(fastify: FastifyInstance) {
  fastify.post('/login', async (request, reply) => {
    // 登录逻辑
  })
  
  fastify.post('/register', async (request, reply) => {
    // 注册逻辑
  })
}
```

#### 第2个月：核心功能开发
**目标**: 实现提示词的基础 CRUD 功能

**后端任务**:
- [ ] 提示词模型和API
- [ ] 分类管理功能
- [ ] 文件上传服务
- [ ] 基础搜索功能
- [ ] 权限控制中间件

**前端任务**:
- [ ] 用户登录/注册页面
- [ ] 提示词列表页面
- [ ] 提示词详情页面
- [ ] 提示词创建/编辑页面
- [ ] 个人工作台

#### 第3个月：完善和测试
**目标**: 功能完善，准备上线

**任务**:
- [ ] 单元测试和集成测试
- [ ] 性能优化
- [ ] UI/UX 优化
- [ ] 部署配置
- [ ] 文档完善

### Phase 2: 完善版本 (2个月)

#### 第4个月：高级功能
- [ ] 浏览器插件开发
- [ ] Elasticsearch 集成
- [ ] 智能推荐算法
- [ ] 社区功能 (评论、点赞)
- [ ] 移动端适配

#### 第5个月：用户体验优化
- [ ] 性能监控
- [ ] 错误追踪
- [ ] A/B 测试
- [ ] 用户反馈系统
- [ ] 多语言支持

### Phase 3: 商业化版本 (2个月)

#### 第6个月：商业功能
- [ ] 付费会员系统
- [ ] 支付集成
- [ ] 企业级功能
- [ ] API 限流和计费
- [ ] 数据分析后台

#### 第7个月：上线和运营
- [ ] 生产环境部署
- [ ] 监控和告警
- [ ] 用户运营工具
- [ ] 客服系统
- [ ] 营销功能

## 🔧 技术选型说明

### 前端技术栈

#### 网站端：React 生态
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "typescript": "^5.0.0",
    "vite": "^4.4.0",
    "tailwindcss": "^3.3.0",
    "zustand": "^4.4.0",
    "react-router-dom": "^6.15.0",
    "react-query": "^3.39.0",
    "axios": "^1.5.0"
  }
}
```

**选择理由**:
- **React**: 成熟的生态，组件化开发
- **TypeScript**: 类型安全，减少运行时错误
- **Vite**: 快速的构建工具
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Zustand**: 轻量级状态管理

#### 插件端：Vue 生态
```json
{
  "dependencies": {
    "vue": "^3.3.0",
    "typescript": "^5.0.0",
    "vite": "^4.4.0",
    "@crxjs/vite-plugin": "^2.0.0",
    "webextension-polyfill": "^0.10.0"
  }
}
```

**选择理由**:
- **Vue 3**: 轻量级，适合插件开发
- **CRXJS**: 现代化插件开发工具
- **TypeScript**: 类型安全

### 后端技术栈

#### API 服务：Node.js 生态
```json
{
  "dependencies": {
    "fastify": "^4.23.0",
    "typescript": "^5.0.0",
    "prisma": "^5.3.0",
    "jsonwebtoken": "^9.0.0",
    "bcrypt": "^5.1.0",
    "redis": "^4.6.0",
    "@elastic/elasticsearch": "^8.10.0"
  }
}
```

**选择理由**:
- **Fastify**: 高性能 Web 框架
- **Prisma**: 现代化 ORM，类型安全
- **JWT**: 无状态认证
- **Redis**: 缓存和会话存储
- **Elasticsearch**: 全文搜索

### 数据库设计

#### PostgreSQL 主数据库
```sql
-- 用户表
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  username VARCHAR(100) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  avatar_url VARCHAR(500),
  subscription_type VARCHAR(20) DEFAULT 'free',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 提示词表
CREATE TABLE prompts (
  id SERIAL PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  content TEXT NOT NULL,
  type VARCHAR(20) NOT NULL, -- 'image', 'agent', 'mcp'
  category_id INTEGER REFERENCES categories(id),
  user_id INTEGER REFERENCES users(id),
  status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'published', 'archived'
  usage_count INTEGER DEFAULT 0,
  rating DECIMAL(3,2) DEFAULT 0,
  tags TEXT[], -- PostgreSQL 数组类型
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📝 开发流程

### 1. 功能开发流程
1. 从 develop 分支创建 feature 分支
2. 开发功能并编写测试
3. 提交代码并推送到远程仓库
4. 创建 Pull Request 到 develop 分支
5. 代码审查和测试
6. 合并到 develop 分支

### 2. 发布流程
1. 从 develop 分支创建 release 分支
2. 最终测试和修复
3. 合并到 main 分支并打 tag
4. 部署到生产环境

### 3. 调试和测试
```bash
# 运行单元测试
npm run test

# 运行 E2E 测试
npm run test:e2e

# 代码覆盖率
npm run test:coverage

# 性能测试
npm run test:performance
```

## 🚀 部署说明

### 开发环境
- 本地 Docker 容器
- 热重载开发

### 测试环境
- Docker Compose 部署
- 自动化测试

### 生产环境
- Kubernetes 集群
- CI/CD 自动部署
- 监控和日志

## 📞 技术支持

如有技术问题，请参考：
- [API 文档](./api/README.md)
- [前端组件文档](./frontend/README.md)
- [部署指南](./deployment/README.md)

或联系开发团队：
- 技术负责人：<EMAIL>
- 开发团队：<EMAIL> 
# 🇨🇳 PromptHub 中国部署指南

由于Supabase在中国的使用限制，本指南提供适合中国用户的替代部署方案。

## 📋 推荐方案对比

| 方案 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| 腾讯云 + PostgreSQL | 国内访问快，服务稳定 | 需要自建认证 | 商业项目 |
| 阿里云 + MySQL | 生态完善，文档丰富 | 数据库差异 | 企业级应用 |
| Vercel + PlanetScale | 部署简单，全球CDN | PlanetScale已停止免费计划 | 小型项目 |
| 自建 Docker | 完全可控，成本低 | 运维复杂 | 技术团队 |

## 🚀 方案1：腾讯云部署 (推荐)

### 1. 云服务配置

#### 数据库服务
```bash
# 腾讯云 PostgreSQL
# 1. 在腾讯云控制台创建 PostgreSQL 实例
# 2. 配置安全组，允许应用服务器访问
# 3. 执行 database/schema.sql 创建表结构
```

#### 认证服务替代
```typescript
// src/lib/auth/tencent-auth.ts
import { sign, verify } from 'jsonwebtoken'

export class TencentAuth {
  private secretKey: string

  constructor(secretKey: string) {
    this.secretKey = secretKey
  }

  // 生成JWT Token
  generateToken(payload: any): string {
    return sign(payload, this.secretKey, { expiresIn: '7d' })
  }

  // 验证Token
  verifyToken(token: string): any {
    return verify(token, this.secretKey)
  }

  // 微信登录
  async wechatLogin(code: string) {
    const response = await fetch(`https://api.weixin.qq.com/sns/oauth2/access_token`, {
      method: 'POST',
      body: JSON.stringify({
        appid: process.env.WECHAT_APP_ID,
        secret: process.env.WECHAT_APP_SECRET,
        code,
        grant_type: 'authorization_code'
      })
    })
    return response.json()
  }
}
```

#### 环境变量配置
```env
# .env.local (腾讯云版本)
# 数据库配置
DATABASE_URL=postgresql://username:<EMAIL>:5432/prompthub

# JWT密钥
JWT_SECRET=your-super-secret-key

# 微信登录配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 腾讯云存储配置
TENCENT_COS_SECRET_ID=your_secret_id
TENCENT_COS_SECRET_KEY=your_secret_key
TENCENT_COS_BUCKET=your-bucket-name
TENCENT_COS_REGION=ap-guangzhou

# 应用配置
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

### 2. 数据库适配

#### 创建数据库适配器
```typescript
// src/lib/db/tencent-adapter.ts
import { Pool } from 'pg'

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
})

export class TencentDBAdapter {
  // 查询提示词
  async getPrompts(filters: any) {
    const query = `
      SELECT p.*, u.username, u.full_name, u.avatar_url
      FROM prompts p
      LEFT JOIN user_profiles u ON p.user_id = u.id
      WHERE p.status = 'published'
      ORDER BY p.created_at DESC
      LIMIT $1 OFFSET $2
    `
    const result = await pool.query(query, [filters.limit, filters.offset])
    return result.rows
  }

  // 创建提示词
  async createPrompt(promptData: any) {
    const query = `
      INSERT INTO prompts (title, content, description, type, category, tags, user_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `
    const values = [
      promptData.title,
      promptData.content,
      promptData.description,
      promptData.type,
      promptData.category,
      promptData.tags,
      promptData.user_id
    ]
    const result = await pool.query(query, values)
    return result.rows[0]
  }

  // 用户认证
  async createUser(userData: any) {
    const query = `
      INSERT INTO user_profiles (id, username, full_name, avatar_url, email)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (id) DO UPDATE SET
        username = $2,
        full_name = $3,
        avatar_url = $4,
        updated_at = NOW()
      RETURNING *
    `
    const values = [
      userData.id,
      userData.username,
      userData.full_name,
      userData.avatar_url,
      userData.email
    ]
    const result = await pool.query(query, values)
    return result.rows[0]
  }
}

export const db = new TencentDBAdapter()
```

### 3. 认证系统重构

#### 更新认证组件
```typescript
// src/components/auth/WechatAuthButton.tsx
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { MessageSquare } from 'lucide-react'

export default function WechatAuthButton() {
  const [loading, setLoading] = useState(false)

  const handleWechatLogin = async () => {
    setLoading(true)
    try {
      // 跳转到微信授权页面
      const appId = process.env.NEXT_PUBLIC_WECHAT_APP_ID
      const redirectUri = encodeURIComponent(`${window.location.origin}/auth/wechat/callback`)
      const state = Math.random().toString(36).substring(7)
      
      window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_userinfo&state=${state}#wechat_redirect`
    } catch (error) {
      console.error('微信登录失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Button 
      onClick={handleWechatLogin} 
      disabled={loading}
      className="gap-2 bg-green-500 hover:bg-green-600"
    >
      <MessageSquare className="w-4 h-4" />
      {loading ? '登录中...' : '微信登录'}
    </Button>
  )
}
```

#### 微信回调处理
```typescript
// src/app/auth/wechat/callback/page.tsx
'use client'

import { useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

export default function WechatCallback() {
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handleCallback = async () => {
      const code = searchParams.get('code')
      const state = searchParams.get('state')

      if (!code) {
        router.push('/?error=wechat_auth_failed')
        return
      }

      try {
        const response = await fetch('/api/auth/wechat', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ code, state })
        })

        if (response.ok) {
          const { token } = await response.json()
          localStorage.setItem('auth_token', token)
          router.push('/')
        } else {
          router.push('/?error=wechat_auth_failed')
        }
      } catch (error) {
        console.error('微信登录处理失败:', error)
        router.push('/?error=wechat_auth_failed')
      }
    }

    handleCallback()
  }, [router, searchParams])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
        <p className="mt-4 text-lg">正在处理微信登录...</p>
      </div>
    </div>
  )
}
```

### 4. API路由更新

```typescript
// src/app/api/auth/wechat/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { TencentAuth } from '@/lib/auth/tencent-auth'
import { db } from '@/lib/db/tencent-adapter'

const auth = new TencentAuth(process.env.JWT_SECRET!)

export async function POST(request: NextRequest) {
  try {
    const { code } = await request.json()

    // 1. 使用code获取微信用户信息
    const wechatResponse = await fetch(`https://api.weixin.qq.com/sns/oauth2/access_token?appid=${process.env.WECHAT_APP_ID}&secret=${process.env.WECHAT_APP_SECRET}&code=${code}&grant_type=authorization_code`)
    const { access_token, openid } = await wechatResponse.json()

    // 2. 获取用户详细信息
    const userResponse = await fetch(`https://api.weixin.qq.com/sns/userinfo?access_token=${access_token}&openid=${openid}&lang=zh_CN`)
    const userInfo = await userResponse.json()

    // 3. 创建或更新用户
    const user = await db.createUser({
      id: openid,
      username: userInfo.nickname,
      full_name: userInfo.nickname,
      avatar_url: userInfo.headimgurl,
      email: null
    })

    // 4. 生成JWT token
    const token = auth.generateToken({
      userId: user.id,
      username: user.username
    })

    return NextResponse.json({ token, user })
  } catch (error) {
    console.error('微信登录失败:', error)
    return NextResponse.json({ error: '登录失败' }, { status: 500 })
  }
}
```

## 🚀 方案2：阿里云部署

### 数据库选择
```bash
# 阿里云 RDS MySQL 8.0
# 修改 schema.sql 适配 MySQL 语法
```

### 主要差异处理
```sql
-- MySQL 版本的 schema 调整
-- 1. UUID 类型替换为 CHAR(36)
-- 2. JSONB 替换为 JSON
-- 3. 数组类型使用 JSON 存储
```

## 🚀 方案3：完全自建

### Docker Compose配置
```yaml
# docker-compose.china.yml
version: '3.8'
services:
  app:
    build: ./frontend/web
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**************************************/prompthub
      - JWT_SECRET=your-secret-key
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=prompthub
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - ./database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app

volumes:
  postgres_data:
```

## 📦 部署步骤

### 1. 准备工作
```bash
# 克隆项目
git clone your-repo
cd prompt

# 选择中国部署配置
cp docs/china-deployment-guide.md ./
cp .env.china.example .env.local
```

### 2. 配置服务
```bash
# 配置数据库连接
# 配置微信登录
# 配置云存储
```

### 3. 部署应用
```bash
# Docker 部署
docker-compose -f docker-compose.china.yml up -d

# 或手动部署
npm install
npm run build
npm start
```

## 🔧 性能优化

### CDN配置
- 使用腾讯云CDN或阿里云CDN
- 配置静态资源缓存
- 启用GZIP压缩

### 缓存策略
```typescript
// Redis 缓存配置
import Redis from 'ioredis'

const redis = new Redis(process.env.REDIS_URL)

export const cache = {
  async get(key: string) {
    return await redis.get(key)
  },
  
  async set(key: string, value: any, ttl = 3600) {
    return await redis.setex(key, ttl, JSON.stringify(value))
  }
}
```

## 📱 微信生态集成

### 微信小程序
- 开发配套小程序版本
- 共享后端API
- 微信支付集成

### 微信公众号
- 内容推送
- 用户运营
- 社群建设

## 🎯 总结

对于中国用户，推荐使用**腾讯云 + PostgreSQL + 微信登录**的方案：

1. **稳定性好** - 国内访问速度快
2. **合规性强** - 满足数据本地化要求
3. **生态完善** - 可接入微信生态
4. **成本可控** - 按需付费，性价比高

这个方案可以提供与Supabase相似的开发体验，同时确保在中国的稳定运行。 
# 🔐 CORS 配置详细指南

## 什么是 CORS？

CORS（Cross-Origin Resource Sharing，跨域资源共享）是一个Web安全机制，用于控制哪些外部网站可以访问你的API。

## 为什么需要 CORS？

### 浏览器同源策略
浏览器默认只允许相同来源的请求：
- 相同协议 (http/https)
- 相同域名 (localhost/example.com)  
- 相同端口 (3000/9000)

### 跨域场景
```javascript
// ❌ 以下情况会被同源策略阻止：
前端: http://localhost:3000
后端: http://localhost:9000    // 端口不同 = 跨域

前端: https://app.example.com
后端: https://api.example.com  // 子域名不同 = 跨域

前端: http://example.com
后端: https://example.com      // 协议不同 = 跨域
```

## 你的项目配置解析

### 当前配置
```javascript
// backend/config.js
CORS: {
  ALLOWED_ORIGINS: [
    'http://localhost:3000',    // Next.js 默认端口
    'http://localhost:3001',    // Next.js 备用端口
    'http://127.0.0.1:3000',    // IP形式的本地地址
    'http://127.0.0.1:3001'
  ],
  ALLOWED_METHODS: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  ALLOWED_HEADERS: ['Content-Type', 'Authorization'],
  CREDENTIALS: true
}
```

### 配置说明

#### ALLOWED_ORIGINS
指定允许访问API的前端地址：
```bash
# 开发环境
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# 生产环境  
ALLOWED_ORIGINS=https://prompthub.com,https://www.prompthub.com

# 测试环境
ALLOWED_ORIGINS=https://test.prompthub.com
```

#### ALLOWED_METHODS
允许的HTTP方法：
- `GET`: 获取数据
- `POST`: 创建数据
- `PUT`: 更新数据
- `DELETE`: 删除数据
- `OPTIONS`: 预检请求

#### ALLOWED_HEADERS
允许的请求头：
- `Content-Type`: 内容类型 (application/json)
- `Authorization`: 认证头 (Bearer token)

#### CREDENTIALS
是否允许发送认证信息：
- `true`: 允许发送 cookies 和 Authorization 头
- `false`: 不允许发送认证信息

## 不同环境的配置

### 开发环境
```bash
# .env.development
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000,http://127.0.0.1:3001
```

### 测试环境
```bash
# .env.staging  
ALLOWED_ORIGINS=https://test.prompthub.com,https://staging.prompthub.com
```

### 生产环境
```bash
# .env.production
ALLOWED_ORIGINS=https://prompthub.com,https://www.prompthub.com
```

## CORS 请求流程

### 简单请求
```mermaid
sequenceDiagram
    participant F as 前端
    participant B as 后端
    
    F->>B: GET /api/prompts (带Origin头)
    B->>F: 200 OK (带CORS头)
```

### 复杂请求（需要预检）
```mermaid
sequenceDiagram
    participant F as 前端
    participant B as 后端
    
    F->>B: OPTIONS /api/prompts (预检)
    B->>F: 204 No Content (CORS头)
    F->>B: POST /api/prompts (实际请求)
    B->>F: 201 Created (数据+CORS头)
```

## 常见错误和解决方案

### 错误1: CORS policy blocked
```
Access to fetch at 'http://localhost:9000/api/prompts' 
from origin 'http://localhost:3001' has been blocked by CORS policy
```

**原因**: ALLOWED_ORIGINS 不包含前端地址  
**解决**: 添加前端地址到 ALLOWED_ORIGINS

### 错误2: 认证请求失败
```
Request header field authorization is not allowed by Access-Control-Allow-Headers
```

**原因**: ALLOWED_HEADERS 不包含 Authorization  
**解决**: 确保配置了 `Authorization` 头

### 错误3: 无法发送 cookies
```
Credentials mode is 'include', but the CORS header 'Access-Control-Allow-Credentials' is absent
```

**原因**: 未设置 `credentials: true`  
**解决**: 设置 `CREDENTIALS: true`

## 安全最佳实践

### ✅ 安全的配置
```javascript
// 生产环境 - 只允许特定域名
ALLOWED_ORIGINS: ['https://yourdomain.com']

// 最小权限原则
ALLOWED_METHODS: ['GET', 'POST']  // 只允许需要的方法
ALLOWED_HEADERS: ['Content-Type'] // 只允许需要的头部
```

### ❌ 危险的配置
```javascript
// 允许所有来源 - 非常危险！
ALLOWED_ORIGINS: ['*']

// 允许所有头部 - 安全风险
ALLOWED_HEADERS: ['*']
```

## 调试 CORS 问题

### 使用浏览器开发者工具
1. 打开 Network 面板
2. 查看 OPTIONS 预检请求
3. 检查响应头中的 CORS 信息

### 使用 curl 测试
```bash
# 测试预检请求
curl -v -H "Origin: http://localhost:3001" \
     -H "Access-Control-Request-Method: GET" \
     -X OPTIONS http://localhost:9000/api/health

# 测试实际请求
curl -v -H "Origin: http://localhost:3001" \
     http://localhost:9000/api/health
```

## 总结

CORS 是前后端分离架构中必不可少的安全配置：

1. **开发阶段**: 配置宽松，方便调试
2. **生产阶段**: 配置严格，保证安全
3. **不同环境**: 使用不同的域名配置
4. **安全第一**: 遵循最小权限原则

通过合理的 CORS 配置，既能保证开发效率，又能确保生产环境的安全性。 
# PromptHub Web 应用

这是 PromptHub AI 提示词平台的 Web 前端应用，基于 Next.js 15 构建。

## 🚀 快速开始

### 前置要求

- Node.js 18+
- npm 或 yarn
- 后端 API 服务运行在 localhost:9000

### 安装依赖

```bash
npm install
```

### 环境配置

1. 创建环境变量文件：
```bash
cp .env.local.example .env.local
```

2. 在 `.env.local` 文件中配置 API 地址：
```env
NEXT_PUBLIC_API_URL=http://localhost:9000/api
```

### 运行开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📁 项目结构

```
src/
├── app/                    # Next.js 13+ App Router
│   ├── api/               # API 路由
│   ├── auth/              # 认证页面
│   ├── create/            # 创建提示词页面
│   ├── prompts/           # 提示词相关页面
│   ├── categories/        # 分类页面
│   └── layout.tsx         # 根布局
├── components/            # React 组件
│   ├── ui/               # UI 基础组件 (shadcn/ui)
│   ├── auth/             # 认证相关组件
│   ├── layout/           # 布局组件
│   └── prompt/           # 提示词相关组件
└── lib/                  # 工具函数和配置
    ├── api-client.ts     # API 客户端
    ├── auth.js           # 认证服务
    └── utils.ts          # 通用工具函数
```

## 🔧 技术栈

- **框架**: Next.js 15 (App Router)
- **样式**: Tailwind CSS
- **UI 组件**: shadcn/ui
- **后端**: Node.js + Express + MySQL
- **认证**: JWT + 自定义认证系统
- **类型**: TypeScript
- **图标**: Lucide React

## 📦 主要功能

- ✅ 用户认证 (注册/登录)
- ✅ 提示词浏览和搜索
- ✅ 提示词创建和编辑
- ✅ 分类系统
- ✅ 用户个人资料
- ✅ 收藏和点赞
- ✅ 响应式设计
- ✅ 管理员后台

## 🚧 开发计划

- [ ] 评论系统
- [ ] 评分系统
- [ ] 热门推荐
- [ ] 搜索优化
- [ ] 深色模式
- [ ] 国际化支持

## 📖 API 文档

### GET /api/prompts
获取提示词列表

参数：
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `category`: 分类筛选
- `type`: 类型筛选 ('image' | 'agent' | 'mcp')
- `search`: 搜索关键词
- `sort`: 排序字段 ('created_at' | 'rating' | 'usage_count')
- `order`: 排序方向 ('asc' | 'desc')

### POST /api/prompts
创建新提示词 (需要认证)

请求体：
```json
{
  "title": "string",
  "content": "string", 
  "description": "string",
  "type": "image" | "agent" | "mcp",
  "category": "string",
  "tags": ["string"]
}
```

## 🔐 认证配置

前端使用 JWT 认证系统，与后端 API 进行交互：

1. 用户注册/登录通过 `/api/auth/register` 和 `/api/auth/login` 接口
2. JWT token 存储在 localStorage 中
3. 每次 API 请求自动携带 Authorization header

## 🐛 故障排除

### 认证问题
- 确保后端 API 服务正在运行
- 检查 API 地址配置
- 验证 JWT token 有效性

### API 连接问题
- 确保后端服务运行在正确端口 (9000)
- 检查 CORS 配置
- 验证网络连接

### 构建问题
- 清除 `.next` 缓存: `rm -rf .next`
- 重新安装依赖: `rm -rf node_modules && npm install`

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 创建 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源。详见 [LICENSE](../../LICENSE) 文件。

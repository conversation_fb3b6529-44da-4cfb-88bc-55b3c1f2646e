// 分类映射工具 - 统一管理分类显示逻辑
// 解决数据库分类标识符与前端显示名称不一致的问题

export interface Category {
  id: number
  name: string        // 英文标识符，存储在数据库中
  description: string // 中文显示名称，用于前端显示
  icon: string
  color: string
  prompt_count?: number
  sort_order?: number
  isActive?: boolean
}

// 分类映射表 - 从数据库英文标识符到中文显示名称
export const categoryMapping: { [key: string]: string } = {
  'ai-assistant': '生产力',
  'copywriting': '创意写作',
  'programming': '编程开发',
  'design': '图像生成',
  'data-analysis': '数据分析',
  'education': '教育学习',
  'business-analysis': '商业营销',
  'lifestyle': '生活娱乐',
  // 兼容旧数据
  'mcp-tools': 'MCP工具',
  'business': '商业分析',
  'image': '图像生成',
  'agent': '生产力',
  'mcp': 'MCP工具',
  'writing': '创意写作'
}

// 反向映射表 - 从中文显示名称到数据库英文标识符
export const reverseCategoryMapping: { [key: string]: string } = {
  '生产力': 'ai-assistant',
  '创意写作': 'copywriting',
  '编程开发': 'programming',
  '图像生成': 'design',
  '数据分析': 'data-analysis',
  '教育学习': 'education',
  '商业营销': 'business-analysis',
  '生活娱乐': 'lifestyle',
  // 兼容旧数据
  'MCP工具': 'mcp-tools',
  '商业分析': 'business-analysis'
}

/**
 * 获取分类的中文显示名称
 * @param categoryKey 数据库中的英文标识符
 * @returns 中文显示名称
 */
export function getCategoryDisplayName(categoryKey: string): string {
  return categoryMapping[categoryKey] || categoryKey || '未分类'
}

/**
 * 获取分类的数据库标识符
 * @param displayName 前端显示的中文名称
 * @returns 数据库中的英文标识符
 */
export function getCategoryKey(displayName: string): string {
  return reverseCategoryMapping[displayName] || displayName
}

/**
 * 转换分类数组，添加显示名称和值字段
 * @param categories 从API获取的分类数组
 * @returns 转换后的分类数组，包含displayName和value字段
 */
export function transformCategoriesForUI(categories: Category[]): (Category & { 
  displayName: string; 
  value: string; 
  label: string 
})[] {
  return categories.map(category => ({
    ...category,
    displayName: category.description || getCategoryDisplayName(category.name),
    value: category.name,
    label: category.description || getCategoryDisplayName(category.name)
  }))
}

/**
 * 验证分类是否有效
 * @param categoryKey 分类标识符（英文或中文）
 * @returns 是否为有效分类
 */
export function isValidCategory(categoryKey: string): boolean {
  return !!(categoryMapping[categoryKey] || reverseCategoryMapping[categoryKey])
}

/**
 * 标准化分类标识符 - 确保返回数据库中的英文标识符
 * @param category 可能是英文标识符或中文显示名称
 * @returns 标准化后的英文标识符
 */
export function normalizeCategoryKey(category: string): string {
  // 如果是中文显示名称，转换为英文标识符
  if (reverseCategoryMapping[category]) {
    return reverseCategoryMapping[category]
  }
  
  // 如果是英文标识符且有效，直接返回
  if (categoryMapping[category]) {
    return category
  }
  
  // 未知分类，返回原值
  return category
} 
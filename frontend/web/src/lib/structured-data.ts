// 结构化数据配置文件 - 为搜索引擎提供更丰富的网站信息

export const organizationStructuredData = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "PromptHub",
  "description": "PromptHub是一个面向AI爱好者的提示词社区，提供海量优质ChatGPT、Midjourney等AI模型的提示词。",
  "url": "https://www.prompthub.xin",
  "logo": "https://www.prompthub.xin/icon-512x512.png",
  "sameAs": [
    "https://github.com/prompthub",
    "https://twitter.com/prompthub"
  ],
  "contactPoint": {
    "@type": "ContactPoint",
    "contactType": "customer service",
    "email": "<EMAIL>"
  }
}

export const websiteStructuredData = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "PromptHub",
  "alternateName": ["AI提示词大全", "免费提示词库", "ChatGPT提示词", "提示词工程"],
  "description": "免费AI提示词大全，提供10000+优质ChatGPT、Claude、Midjourney提示词，专业提示词工程教程",
  "url": "https://www.prompthub.xin",
  "keywords": "提示词,AI提示词,免费提示词,ChatGPT提示词,Claude提示词,Midjourney提示词,提示词工程,Prompt Engineering,AI指令,提示词模板,提示词库,AI助手,人工智能,AIGC",
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": "https://www.prompthub.xin/prompts?search={search_term_string}"
    },
    "query-input": "required name=search_term_string"
  },
  "mainEntity": {
    "@type": "ItemList",
    "name": "AI提示词库",
    "description": "精选的AI提示词集合，涵盖ChatGPT、Claude、Midjourney等主流AI工具，包含编程、写作、绘画、营销等多个专业领域",
    "itemListElement": [
      {"@type": "Thing", "name": "ChatGPT提示词", "description": "专业ChatGPT提示词模板，涵盖对话、写作、分析等场景"},
      {"@type": "Thing", "name": "Claude提示词", "description": "高质量Claude AI提示词，适用于复杂推理和创作任务"},
      {"@type": "Thing", "name": "Midjourney提示词", "description": "AI绘画Midjourney提示词，助您创作精美图像"},
      {"@type": "Thing", "name": "编程提示词", "description": "AI编程助手提示词，提升代码质量和开发效率"},
      {"@type": "Thing", "name": "写作提示词", "description": "AI写作助手提示词，激发创意灵感"},
      {"@type": "Thing", "name": "营销提示词", "description": "AI营销助手提示词，优化营销策略和文案"}
    ]
  }
}

export const softwareApplicationStructuredData = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "PromptHub",
  "applicationCategory": "WebApplication",
  "operatingSystem": "Any",
  "description": "AI提示词分享、创作与学习平台，为AI爱好者提供优质的提示词资源。",
  "url": "https://www.prompthub.xin",
  "author": {
    "@type": "Organization",
    "name": "PromptHub Team"
  },
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "CNY",
    "availability": "https://schema.org/InStock"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8",
    "reviewCount": "1000",
    "bestRating": "5",
    "worstRating": "1"
  }
}

// 为提示词页面生成结构化数据
export function generatePromptStructuredData(prompt: any) {
  return {
    "@context": "https://schema.org",
    "@type": "CreativeWork",
    "name": prompt.title,
    "description": prompt.description,
    "text": prompt.content,
    "author": {
      "@type": "Person",
      "name": prompt.authorName || "PromptHub用户"
    },
    "dateCreated": prompt.createdAt,
    "dateModified": prompt.updatedAt,
    "category": prompt.category,
    "keywords": prompt.tags?.join(", ") || "",
    "url": `https://www.prompthub.xin/prompts/${prompt.id}`,
    "isPartOf": {
      "@type": "WebSite",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "interactionStatistic": [
      {
        "@type": "InteractionCounter",
        "interactionType": "https://schema.org/LikeAction",
        "userInteractionCount": prompt.likes || 0
      },
      {
        "@type": "InteractionCounter", 
        "interactionType": "https://schema.org/ViewAction",
        "userInteractionCount": prompt.views || 0
      }
    ]
  }
}

// 为分类页面生成结构化数据
export function generateCategoryStructuredData(category: string, prompts: any[]) {
  return {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": `${category} - AI提示词集合`,
    "description": `精选的${category}类AI提示词，助您在该领域发挥AI的最大潜能。`,
    "url": `https://www.prompthub.xin/prompts?category=${category}`,
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": prompts.length,
      "itemListElement": prompts.map((prompt, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "CreativeWork",
          "name": prompt.title,
          "description": prompt.description,
          "url": `https://www.prompthub.xin/prompts/${prompt.id}`
        }
      }))
    }
  }
}

// 为解决方案页面生成FAQ结构化数据
export function generateSolutionFAQStructuredData(faqs: Array<{question: string, answer: string}>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }
}

// 为教程页面生成HowTo结构化数据
export function generateHowToStructuredData(title: string, description: string, steps: Array<{name: string, text: string}>) {
  return {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": title,
    "description": description,
    "step": steps.map((step, index) => ({
      "@type": "HowToStep",
      "position": index + 1,
      "name": step.name,
      "text": step.text
    }))
  }
}

// 为职业专区生成结构化数据
export function generateProfessionStructuredData(profession: string, description: string, tools: any[]) {
  return {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": `${profession}专区 - PromptHub`,
    "description": description,
    "url": `https://www.prompthub.xin/for/${profession.toLowerCase()}`,
    "about": {
      "@type": "Thing",
      "name": profession,
      "description": `专为${profession}设计的AI工具和提示词`
    },
    "mainEntity": {
      "@type": "ItemList",
      "name": `${profession}专用AI工具`,
      "numberOfItems": tools.length,
      "itemListElement": tools.map((tool, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "SoftwareApplication",
          "name": tool.name,
          "description": tool.description,
          "applicationCategory": "AI Tool"
        }
      }))
    }
  }
}



// 面包屑导航结构化数据
export function generateBreadcrumbStructuredData(items: Array<{name: string, url: string}>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  }
} 
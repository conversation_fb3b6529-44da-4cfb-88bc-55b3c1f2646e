export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      categories: {
        Row: {
          id: string
          name: string
          description: string | null
          icon: string | null
          color: string | null
          prompt_count: number
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          icon?: string | null
          color?: string | null
          prompt_count?: number
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          icon?: string | null
          color?: string | null
          prompt_count?: number
          created_at?: string
        }
      }
      comments: {
        Row: {
          id: string
          prompt_id: string
          user_id: string
          content: string
          rating: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          prompt_id: string
          user_id: string
          content: string
          rating?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          prompt_id?: string
          user_id?: string
          content?: string
          rating?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      favorites: {
        Row: {
          id: string
          user_id: string
          prompt_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          prompt_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          prompt_id?: string
          created_at?: string
        }
      }
      likes: {
        Row: {
          id: string
          user_id: string
          prompt_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          prompt_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          prompt_id?: string
          created_at?: string
        }
      }
      prompts: {
        Row: {
          id: string
          title: string
          content: string
          description: string | null
          type: 'image' | 'agent' | 'mcp'
          category: string
          tags: string[]
          user_id: string
          status: 'draft' | 'published' | 'archived'
          usage_count: number
          rating: number
          view_count: number
          like_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          content: string
          description?: string | null
          type: 'image' | 'agent' | 'mcp'
          category: string
          tags?: string[]
          user_id: string
          status?: 'draft' | 'published' | 'archived'
          usage_count?: number
          rating?: number
          view_count?: number
          like_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          content?: string
          description?: string | null
          type?: 'image' | 'agent' | 'mcp'
          category?: string
          tags?: string[]
          user_id?: string
          status?: 'draft' | 'published' | 'archived'
          usage_count?: number
          rating?: number
          view_count?: number
          like_count?: number
          created_at?: string
          updated_at?: string
        }
      }
      usage_history: {
        Row: {
          id: string
          user_id: string
          prompt_id: string
          used_at: string
        }
        Insert: {
          id?: string
          user_id: string
          prompt_id: string
          used_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          prompt_id?: string
          used_at?: string
        }
      }
      user_profiles: {
        Row: {
          id: string
          username: string | null
          full_name: string | null
          avatar_url: string | null
          bio: string | null
          website: string | null
          twitter: string | null
          github: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          website?: string | null
          twitter?: string | null
          github?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          website?: string | null
          twitter?: string | null
          github?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      prompt_status: 'draft' | 'published' | 'archived'
      prompt_type: 'image' | 'agent' | 'mcp'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// 便捷类型定义
export type Prompt = Database['public']['Tables']['prompts']['Row']
export type PromptInsert = Database['public']['Tables']['prompts']['Insert']
export type PromptUpdate = Database['public']['Tables']['prompts']['Update']

export type UserProfile = Database['public']['Tables']['user_profiles']['Row']
export type UserProfileInsert = Database['public']['Tables']['user_profiles']['Insert']
export type UserProfileUpdate = Database['public']['Tables']['user_profiles']['Update']

export type Category = Database['public']['Tables']['categories']['Row']
export type Comment = Database['public']['Tables']['comments']['Row']
export type Favorite = Database['public']['Tables']['favorites']['Row']
export type Like = Database['public']['Tables']['likes']['Row']
export type UsageHistory = Database['public']['Tables']['usage_history']['Row']

export type PromptType = Database['public']['Enums']['prompt_type']
export type PromptStatus = Database['public']['Enums']['prompt_status']

// 扩展类型，包含关联数据
export interface PromptWithAuthor extends Prompt {
  user_profiles?: UserProfile
  is_favorited?: boolean
  is_liked?: boolean
  comments_count?: number
}

export interface CommentWithAuthor extends Comment {
  user_profiles?: UserProfile
} 
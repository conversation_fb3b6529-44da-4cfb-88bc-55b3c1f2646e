// 确保在服务端和客户端都能正确解析 API URL
const getApiBaseUrl = () => {
  // 在生产环境中，强制使用相对路径
  if (process.env.NODE_ENV === 'production') {
    if (typeof window === 'undefined') {
      // 服务端渲染时，使用完整的内部 URL
      const backendPort = process.env.BACKEND_PORT || '4000'
      return `http://127.0.0.1:${backendPort}/api`
    } else {
      // 客户端，使用相对路径（通过 Nginx 代理）
      return '/api'
    }
  }

  // 开发环境使用环境变量或默认值
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL
  }

  const backendPort = process.env.BACKEND_PORT || '4000'
  return typeof window === 'undefined' ? `http://127.0.0.1:${backendPort}/api` : '/api'
}

export const API_BASE_URL = getApiBaseUrl()

export const api = {
  prompts: {
    public: `${API_BASE_URL}/prompts/public`,
    list: `${API_BASE_URL}/prompts`,
    myList: `${API_BASE_URL}/prompts/my`,
    create: `${API_BASE_URL}/prompts`,
    update: (id: string) => `${API_BASE_URL}/prompts/${id}`,
    delete: (id: string) => `${API_BASE_URL}/prompts/${id}`,
  },
  auth: {
    login: `${API_BASE_URL}/auth/login`,
    register: `${API_BASE_URL}/auth/register`,
  },
  categories: {
    list: `${API_BASE_URL}/categories`,
    stats: `${API_BASE_URL}/categories/stats`,
  }
} 
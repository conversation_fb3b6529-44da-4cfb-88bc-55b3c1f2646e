// 确保在服务端和客户端都能正确解析 API URL
const getApiBaseUrl = () => {
  // 在生产环境中，强制使用相对路径
  if (process.env.NODE_ENV === 'production') {
    if (typeof window === 'undefined') {
      // 服务端渲染时，使用完整的内部 URL
      const backendPort = process.env.BACKEND_PORT || '4000'
      return `http://127.0.0.1:${backendPort}/api`
    } else {
      // 客户端，使用相对路径（通过 Nginx 代理）
      return '/api'
    }
  }

  // 开发环境使用环境变量或默认值
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL
  }

  const backendPort = process.env.BACKEND_PORT || '4000'
  return typeof window === 'undefined' ? `http://127.0.0.1:${backendPort}/api` : '/api'
}

const API_BASE_URL = getApiBaseUrl()

// 内存备份存储
let memoryStorage = {
  authToken: null,
  userData: null
}

// Cookie 辅助函数
const setCookie = (name, value, days = 30) => {
  if (typeof document === 'undefined') return
  
  try {
    const expires = new Date()
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000))
    document.cookie = `${name}=${encodeURIComponent(value)};expires=${expires.toUTCString()};path=/;SameSite=Lax`
    console.log(`🍪 Cookie设置成功: ${name}`)
  } catch (error) {
    console.error('❌ Cookie设置失败:', error)
  }
}

const getCookie = (name) => {
  if (typeof document === 'undefined') return null
  
  try {
    const nameEQ = name + "="
    const ca = document.cookie.split(';')
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i]
      while (c.charAt(0) === ' ') c = c.substring(1, c.length)
      if (c.indexOf(nameEQ) === 0) {
        const value = decodeURIComponent(c.substring(nameEQ.length, c.length))
        console.log(`🍪 Cookie读取成功: ${name}`)
        return value
      }
    }
  } catch (error) {
    console.error('❌ Cookie读取失败:', error)
  }
  return null
}

const removeCookie = (name) => {
  if (typeof document === 'undefined') return
  
  try {
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`
    console.log(`🍪 Cookie删除成功: ${name}`)
  } catch (error) {
    console.error('❌ Cookie删除失败:', error)
  }
}

// 网页端不需要存储认证信息 - 完全依赖HttpOnly Cookie
const setSecureStorage = (key, value) => {
  if (typeof window === 'undefined') return

  const stringValue = typeof value === 'string' ? value : JSON.stringify(value)

  // 只在内存中临时存储用户信息，用于UI显示
  // 不存储token，认证完全依赖HttpOnly Cookie
  if (key === 'userData') {
    memoryStorage[key] = stringValue
    console.log(`💾 用户信息临时存储: ${JSON.parse(stringValue).username}`)
  } else if (key === 'authToken') {
    // 不存储token，认证完全依赖HttpOnly Cookie
    console.log(`🍪 认证依赖HttpOnly Cookie，不存储token`)
  }
}

const getSecureStorage = (key) => {
  // 网页端认证逻辑：
  // - authToken: 不存储，完全依赖HttpOnly Cookie
  // - userData: 只在内存中临时存储，用于UI显示

  if (key === 'authToken') {
    // 网页端不存储token，认证完全依赖HttpOnly Cookie
    console.log(`🍪 Token认证依赖HttpOnly Cookie`)
    return null
  }

  if (key === 'userData' && memoryStorage[key]) {
    console.log(`📖 从内存读取用户信息`)
    return memoryStorage[key]
  }

  return null
}

const clearSecureStorage = (key) => {
  // 只清除内存存储
  memoryStorage[key] = null
  console.log(`🗑️ 内存清除: ${key}`)
}

class AuthService {
  constructor() {
    console.log('🔧 AuthService初始化开始...')
    this.authChannel = null

    // 异步初始化，不阻塞构造函数
    this.initializeStorage().catch(error => {
      console.error('❌ AuthService初始化失败:', error)
    })

    // 初始化BroadcastChannel
    this.initializeBroadcastChannel()
  }

  async initializeStorage() {
    if (typeof window === 'undefined') return

    console.log('🔧 AuthService初始化: 检查HttpOnly Cookie认证状态')

    // 尝试通过Cookie获取用户信息
    try {
      const userInfo = await this.fetchUserInfo()
      if (userInfo) {
        // 存储用户信息，用于UI显示
        this.setAuth('cookie-auth', userInfo)
        console.log('✅ 通过Cookie恢复登录状态:', userInfo.username)
      } else {
        console.log('ℹ️ 无有效Cookie，用户未登录')
      }
    } catch (error) {
      // 静默处理Cookie认证失败
      console.log('ℹ️ Cookie认证失败，用户未登录')
    }
  }

  // 主动检查Cookie认证状态
  async checkCookieAuth() {
    console.log('🍪 主动检查HttpOnly Cookie认证状态...')
    try {
      const userInfo = await this.fetchUserInfo()
      if (userInfo) {
        // 只存储用户信息到内存，用于UI显示
        this.setAuth('cookie-auth', userInfo)
        console.log('✅ 通过Cookie恢复登录状态:', userInfo.username)
        return userInfo
      }
    } catch (error) {
      console.log('ℹ️ 无有效Cookie，用户未登录')
      return null
    }
    return null
  }



  // 初始化BroadcastChannel
  initializeBroadcastChannel() {
    if (typeof window === 'undefined') return

    try {
      this.authChannel = new BroadcastChannel('prompthub-auth')
      console.log('📡 网页端BroadcastChannel已创建')

      // 监听插件的认证状态变化
      this.authChannel.onmessage = (event) => {
        console.log('📡 网页端收到认证状态变化:', event.data)
        this.handleAuthStateChange(event.data)
      }
    } catch (error) {
      console.warn('⚠️ 网页端BroadcastChannel创建失败:', error)
    }
  }

  // 处理认证状态变化
  async handleAuthStateChange(data) {
    // 防止重复处理 - 添加处理标记
    if (this._isHandlingAuthChange) {
      console.log('⚠️ 正在处理认证状态变化，跳过重复处理')
      return
    }

    this._isHandlingAuthChange = true

    try {
      if (data.type === 'login') {
        console.log('🔐 插件登录，同步到网页端')

        // 使用专门的方法检查Cookie认证
        const userInfo = await this.checkCookieAuth()
        if (userInfo) {
          // 触发页面刷新或状态更新
          window.dispatchEvent(new CustomEvent('auth-state-changed', {
            detail: { type: 'login', user: userInfo }
          }))
          console.log('✅ 插件登录同步完成:', userInfo.username)
        } else {
          console.error('❌ 同步插件登录失败：无法获取用户信息')
        }
      } else if (data.type === 'logout') {
        console.log('🚪 插件退出登录，同步到网页端')
        // 使用特殊标记避免通知插件
        this.clearAuthSilently()
        // 设置标记，防止重新初始化时尝试Cookie认证
        this._justLoggedOutByPlugin = true
        // 触发页面刷新或状态更新
        window.dispatchEvent(new CustomEvent('auth-state-changed', {
          detail: { type: 'logout' }
        }))
      }
    } finally {
      // 延迟重置标记，避免快速连续的消息
      setTimeout(() => {
        this._isHandlingAuthChange = false
        // 清除插件退出登录标记
        if (this._justLoggedOutByPlugin) {
          setTimeout(() => {
            this._justLoggedOutByPlugin = false
          }, 2000) // 2秒后清除标记
        }
      }, 100)
    }
  }

  // 注册
  async register(userData) {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || '注册失败')
      }

      if (data.success && data.data) {
        this.setAuth(data.data.token, data.data.user)
        return data.data
      } else {
        throw new Error('注册响应格式错误')
      }
    } catch (error) {
      console.error('注册请求失败:', error)
      throw error
    }
  }

  // 登录
  async login(credentials) {
    try {
      // 转换前端的email字段为后端的identifier字段
      const loginData = {
        identifier: credentials.email || credentials.username || credentials.identifier,
        password: credentials.password
      }

      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginData),
        credentials: 'include' // 包含Cookie
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || '登录失败')
      }

      if (data.success && data.data) {
        // 网页端登录：后端设置HttpOnly Cookie，前端只保存用户信息
        this.setAuth(null, data.data.user)
        return data.data
      } else {
        throw new Error('登录响应格式错误')
      }
    } catch (error) {
      console.error('登录请求失败:', error)
      throw error
    }
  }

  // 获取当前用户信息
  async fetchUserInfo() {
    try {
      const token = this.getToken()

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json',
      }

      // 如果有token且不是cookie-auth标识，则添加Authorization头
      if (token && token !== 'cookie-auth') {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        method: 'GET',
        headers,
        credentials: 'include' // 包含Cookie，支持Cookie认证
      })

      const data = await response.json()

      if (!response.ok) {
        if (response.status === 401) {
          // 认证失败，清除认证信息
          this.clearAuth()
          throw new Error('登录已过期，请重新登录')
        }
        throw new Error(data.message || '获取用户信息失败')
      }

      if (data.success && data.data) {
        setSecureStorage('userData', data.data)
        console.log('💾 更新用户数据:', data.data.username)
        return data.data
      } else {
        throw new Error('用户信息响应格式错误')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 退出登录
  async logout() {
    try {
      const token = this.getToken()
      if (token && token !== 'cookie-auth') {
        // 调用后端登出接口
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          credentials: 'include' // 包含Cookie
        })
      } else {
        // 对于Cookie认证，直接调用登出接口
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include' // 包含Cookie
        })
      }
    } catch (error) {
      console.error('登出请求失败:', error)
      // 即使请求失败也要清除本地认证信息
    } finally {
      this.clearAuth()

      // 通知插件退出登录
      if (this.authChannel && !this._isHandlingAuthChange) {
        try {
          this.authChannel.postMessage({ type: 'logout' })
          console.log('📡 已通知插件退出登录')
        } catch (error) {
          console.warn('⚠️ 通知插件退出登录失败:', error)
        }
      }
    }
  }



  // 设置认证信息（网页端只存储用户信息，不存储token）
  setAuth(token, user) {
    console.log('🚀 网页端登录:', { username: user.username })

    // 只存储用户信息用于UI显示
    setSecureStorage('userData', user)

    // 通知插件登录成功（通过BroadcastChannel）
    if (this.authChannel && !this._isHandlingAuthChange) {
      try {
        this.authChannel.postMessage({
          type: 'login',
          user: user
        })
        console.log('📡 已通知插件登录成功')
      } catch (error) {
        console.warn('⚠️ 通知插件登录失败:', error)
      }
    }

    console.log('✅ 网页端登录完成')
    return true
  }

  // 静默设置认证信息（不通知插件）
  setAuthSilently(token, user) {
    console.log('🔇 静默执行登录:', { username: user.username, hasToken: !!token })

    setSecureStorage('authToken', token)
    setSecureStorage('userData', user)

    console.log('✅ 静默登录数据保存完成')
    return true
  }

  // 保存用户数据
  saveUserData() {
    const user = this.getUser()
    if (typeof window !== 'undefined' && user) {
      try {
        setSecureStorage('userData', user)
        console.log('💾 更新用户数据:', user.username)
      } catch (error) {
        console.error('❌ 保存用户数据失败:', error)
      }
    }
  }

  // 清除认证信息
  clearAuth() {
    console.log('🚪 执行登出')

    clearSecureStorage('authToken')
    clearSecureStorage('userData')

    console.log('✅ 登出完成')
  }

  // 静默清除认证信息（不通知插件）
  clearAuthSilently() {
    console.log('🔇 静默执行登出')

    clearSecureStorage('authToken')
    clearSecureStorage('userData')

    console.log('✅ 静默登出完成')
  }

  // 获取当前用户
  getCurrentUser() {
    return this.getUser()
  }

  // 检查是否已登录（网页端基于用户信息判断，认证依赖HttpOnly Cookie）
  isAuthenticated() {
    const user = this.getUser()
    const isAuth = !!user

    console.log('🔍 网页端认证检查:', {
      hasUser: !!user,
      isAuthenticated: isAuth,
      username: user ? user.username : 'N/A'
    })

    return isAuth
  }

  // 获取认证头
  getAuthHeader() {
    const token = this.getToken()
    return token ? { Authorization: `Bearer ${token}` } : {}
  }

  // 验证认证状态（网页端通过调用/auth/me接口验证HttpOnly Cookie）
  async validateToken() {
    try {
      const userInfo = await this.fetchUserInfo()
      return !!userInfo
    } catch (error) {
      return false
    }
  }

  // 获取token
  getToken() {
    return getSecureStorage('authToken')
  }

  // 获取用户数据
  getUser() {
    const userData = getSecureStorage('userData')
    if (userData) {
      try {
        return typeof userData === 'string' ? JSON.parse(userData) : userData
      } catch (error) {
        console.error('❌ 用户数据解析失败:', error)
        return null
      }
    }
    return null
  }

  // 获取存储状态调试信息
  getStorageDebugInfo() {
    return {
      memory: {
        authToken: !!memoryStorage.authToken,
        userData: !!memoryStorage.userData
      },
      localStorage: {
        authToken: !!localStorage.getItem('authToken'),
        userData: !!localStorage.getItem('userData')
      },
      sessionStorage: {
        authToken: !!sessionStorage.getItem('authToken'),
        userData: !!sessionStorage.getItem('userData')
      },
      cookies: {
        authToken: !!getCookie('auth_token_backup'),
        userData: !!getCookie('user_data_backup')
      }
    }
  }
}

const authService = new AuthService()
export default authService 
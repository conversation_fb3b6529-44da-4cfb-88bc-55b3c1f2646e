// 百度推送配置
// 从环境变量或配置文件中读取百度推送相关配置

export const BAIDU_PUSH_CONFIG = {
  // 网站域名 - 必须与百度站长工具中验证的域名一致
  site: process.env.BAIDU_SITE || 'https://www.prompthub.xin',

  // 推送token - 从百度站长工具获取
  // 可以通过环境变量 BAIDU_PUSH_TOKEN 设置
  token: process.env.BAIDU_PUSH_TOKEN || 'bVucnxKseTp3X1Hh',

  // 百度推送API地址
  pushUrl: 'http://data.zz.baidu.com/urls',

  // 推送配置
  batchSize: 2000, // 每批次最大URL数量
  batchDelay: 1000, // 批次间延迟时间(ms)

  // 请求配置
  timeout: 30000, // 请求超时时间(ms)
  userAgent: 'PromptHub-Baidu-Push/1.0',

  // 开发环境配置
  enableInDevelopment: process.env.ENABLE_BAIDU_PUSH_DEV === 'true'
};

// 验证配置是否完整
export function validateBaiduConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!BAIDU_PUSH_CONFIG.site) {
    errors.push('缺少网站域名配置');
  }
  
  if (!BAIDU_PUSH_CONFIG.token) {
    errors.push('缺少百度推送token');
  }
  
  if (!BAIDU_PUSH_CONFIG.site.startsWith('http')) {
    errors.push('网站域名必须包含协议(http/https)');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

// 构建推送URL
export function buildPushUrl(): string {
  return `${BAIDU_PUSH_CONFIG.pushUrl}?site=${encodeURIComponent(BAIDU_PUSH_CONFIG.site)}&token=${BAIDU_PUSH_CONFIG.token}`;
}

// 获取配置信息（隐藏敏感信息）
export function getConfigInfo() {
  return {
    site: BAIDU_PUSH_CONFIG.site,
    pushUrl: BAIDU_PUSH_CONFIG.pushUrl,
    hasToken: !!BAIDU_PUSH_CONFIG.token,
    tokenPreview: BAIDU_PUSH_CONFIG.token ? `${BAIDU_PUSH_CONFIG.token.slice(0, 4)}****` : null,
    batchSize: BAIDU_PUSH_CONFIG.batchSize,
    batchDelay: BAIDU_PUSH_CONFIG.batchDelay
  };
}

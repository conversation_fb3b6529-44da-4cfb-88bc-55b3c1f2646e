interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
}

interface ApiError extends Error {
  status?: number
  isAuthError?: boolean
}

class ApiClient {
  private baseUrl: string

  constructor() {
    // 在生产环境中，强制使用正确的URL
    if (process.env.NODE_ENV === 'production') {
      if (typeof window === 'undefined') {
        // 服务器端环境（构建时）
        const backendPort = process.env.BACKEND_PORT || '4000'
        this.baseUrl = `http://127.0.0.1:${backendPort}/api`
      } else {
        // 客户端环境 - 使用相对路径
        this.baseUrl = '/api'
      }
    } else {
      // 开发环境使用环境变量或默认值
      if (process.env.NEXT_PUBLIC_API_URL) {
        this.baseUrl = process.env.NEXT_PUBLIC_API_URL
      } else if (typeof window !== 'undefined') {
        this.baseUrl = '/api'
      } else {
        const backendPort = process.env.BACKEND_PORT || '4000'
        this.baseUrl = `http://127.0.0.1:${backendPort}/api`
      }
    }
    console.log('🔧 API Client initialized with baseUrl:', this.baseUrl)
  }

  private getAuthHeaders(): Record<string, string> {
    // 网页端完全依赖HttpOnly Cookie认证，不使用Authorization头
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }

    console.log('🍪 使用Cookie认证')
    return headers
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    try {
      const data: ApiResponse<T> = await response.json()
      
      if (!response.ok) {
        const error = new Error(data.message || `HTTP ${response.status}`) as ApiError
        error.status = response.status
        error.isAuthError = response.status === 401
        
        // 记录认证错误但不在这里清除状态
        if (response.status === 401) {
          console.warn('🚫 API请求认证失败 (401)，token可能已过期')
        }
        
        throw error
      }

      if (!data.success) {
        throw new Error(data.message || '请求失败')
      }

      return data.data as T
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw new Error('服务器响应格式错误')
      }
      throw error
    }
  }

  async get<T>(endpoint: string, requireAuth = false): Promise<T> {
    try {
      const headers = requireAuth ? this.getAuthHeaders() : { 'Content-Type': 'application/json' }

      console.log(`📡 GET ${endpoint}`, { requireAuth, hasAuthHeader: !!headers.Authorization })

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'GET',
        headers,
        credentials: 'include' // 包含Cookie
      })

      return await this.handleResponse<T>(response)
    } catch (error) {
      if (error instanceof Error && error.message === 'Failed to fetch') {
        throw new Error('网络连接失败，请检查网络或稍后重试')
      }
      throw error
    }
  }

  async post<T>(endpoint: string, data?: any, requireAuth = false): Promise<T> {
    try {
      const headers = requireAuth ? this.getAuthHeaders() : { 'Content-Type': 'application/json' }

      console.log(`📡 POST ${endpoint}`, { requireAuth, hasAuthHeader: !!headers.Authorization, hasData: !!data })

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers,
        body: data ? JSON.stringify(data) : undefined,
        credentials: 'include' // 包含Cookie
      })

      return await this.handleResponse<T>(response)
    } catch (error) {
      if (error instanceof Error && error.message === 'Failed to fetch') {
        throw new Error('网络连接失败，请检查网络或稍后重试')
      }
      throw error
    }
  }

  async put<T>(endpoint: string, data?: any, requireAuth = true): Promise<T> {
    try {
      const headers = requireAuth ? this.getAuthHeaders() : { 'Content-Type': 'application/json' }

      console.log(`📡 PUT ${endpoint}`, { requireAuth, hasAuthHeader: !!headers.Authorization })

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'PUT',
        headers,
        body: data ? JSON.stringify(data) : undefined,
        credentials: 'include' // 包含Cookie
      })

      return await this.handleResponse<T>(response)
    } catch (error) {
      if (error instanceof Error && error.message === 'Failed to fetch') {
        throw new Error('网络连接失败，请检查网络或稍后重试')
      }
      throw error
    }
  }

  async delete<T>(endpoint: string, requireAuth = true): Promise<T> {
    try {
      const headers = requireAuth ? this.getAuthHeaders() : { 'Content-Type': 'application/json' }

      console.log(`📡 DELETE ${endpoint}`, { requireAuth, hasAuthHeader: !!headers.Authorization })

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'DELETE',
        headers,
        credentials: 'include' // 包含Cookie
      })

      return await this.handleResponse<T>(response)
    } catch (error) {
      if (error instanceof Error && error.message === 'Failed to fetch') {
        throw new Error('网络连接失败，请检查网络或稍后重试')
      }
      throw error
    }
  }

  // 特定的API方法
  async getPublicPrompts() {
    return this.get('/prompts/public')
  }

  async getMyPrompts() {
    return this.get('/prompts/my', true)
  }

  async createPrompt(promptData: any) {
    return this.post('/prompts', promptData, true)
  }

  async updatePrompt(id: string, promptData: any) {
    return this.put(`/prompts/${id}`, promptData, true)
  }

  async deletePrompt(id: string) {
    return this.delete(`/prompts/${id}`, true)
  }

  async getCurrentUser() {
    return this.get('/auth/me', true)
  }

  // 专门用于验证token的方法
  async validateToken(): Promise<boolean> {
    try {
      await this.getCurrentUser()
      return true
    } catch (error) {
      const apiError = error as ApiError
      if (apiError.status === 401) {
        console.log('🔒 Token验证失败：401 Unauthorized')
        return false
      }
      // 其他错误（如网络错误）不视为token无效
      console.log('🌐 Token验证时遇到网络或其他错误，假设token有效')
      return true
    }
  }
}

export const apiClient = new ApiClient()
export type { ApiError } 
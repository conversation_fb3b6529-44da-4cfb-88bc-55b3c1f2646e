'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { PromptCard } from '@/components/prompt/PromptCard';
import {
  Search,
  Users,
  BookOpen,
  TrendingUp,
  Target,
  Cpu,
  Wand2,
  Heart,
  Sparkles,
  Chrome,
  Download,
  Star,
  MousePointer,
  Zap,
  Keyboard,
} from 'lucide-react';
import Link from 'next/link';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { API_BASE_URL } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { HomePageSEO } from '@/components/seo/HiddenSEOContent';

// 定义数据类型
interface PromptItem {
  id: string | number;
  title: string;
  description: string;
  content: string;
  category: string;
  tags: string[];
  authorName?: string;
  likes?: number;
  usage_count?: number;
  created_at?: string;
  isLiked?: boolean;
  downloads?: number;
  views?: number;
}

interface Stats {
  totalPrompts: number;
  totalUsers: number;
  totalCategories: number;
  dailyActive: number;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  hasNext: boolean;
}

interface HomePageClientProps {
  initialPrompts: PromptItem[];
  initialStats: Stats;
  initialPagination: Pagination;
}

export default function HomePageClient({ initialPrompts, initialStats, initialPagination }: HomePageClientProps) {
  const { user, loading: authLoading } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('hot');
  const [prompts, setPrompts] = useState<PromptItem[]>(initialPrompts);
  const [stats, setStats] = useState<Stats>(initialStats);
  const [pagination, setPagination] = useState<Pagination>(initialPagination);
  const [loading, setLoading] = useState(false);

  // 仅在客户端加载更多时需要
  const fetchPrompts = async (page: number, append: boolean = false) => {
    try {
      setLoading(true);

      // 映射前端tab值到后端sortBy参数
      let sortBy = 'newest'; // 默认值
      switch (activeTab) {
        case 'hot':
          sortBy = 'downloads'; // 热门推荐 = 按下载量排序
          break;
        case 'likes':
          sortBy = 'popular'; // 最受欢迎 = 按点赞排序
          break;
        case 'newest':
          sortBy = 'newest'; // 最新上架 = 按时间排序
          break;
      }

      // 获取认证token（如果有）
      const token = localStorage.getItem('authToken');
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      // 如果有token且不是cookie-auth标识，添加认证头
      if (token && token !== 'cookie-auth') {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE_URL}/prompts/public?page=${page}&limit=6&sortBy=${sortBy}`, {
        headers,
        credentials: 'include' // 支持Cookie认证
      });
      if (!response.ok) throw new Error('Failed to fetch');

      const result = await response.json();
      if (result.success && result.data) {
        setPrompts(prev => append ? [...prev, ...result.data] : result.data);
        setPagination(result.pagination);
      }
    } catch (error) {
      console.error('Failed to fetch more prompts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // 重新获取该分类的第一页数据
    fetchPrompts(1, false);
  };

  // 只在tab切换时重新获取数据，避免首次加载时重复请求
  useEffect(() => {
    fetchPrompts(1, false);
  }, [activeTab]);

  // 监听用户登录状态变化，当用户登录后重新获取数据以获取点赞状态
  useEffect(() => {
    if (!authLoading && user) {
      // 用户刚登录，重新获取当前页面的数据以获取点赞状态
      fetchPrompts(1, false);
    }
  }, [authLoading, user]);
  
  const loadMorePrompts = () => {
    if (pagination.hasNext && !loading) {
      fetchPrompts(pagination.page + 1, true);
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      window.location.href = `/prompts?search=${encodeURIComponent(searchQuery)}`;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* 隐藏的SEO内容组件 */}
      <HomePageSEO />

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-900 via-blue-900 to-slate-900">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.3),transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(29,78,216,0.2),transparent_50%)]"></div>
        </div>
        <div className="relative px-4 py-12 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-7xl">
            {/* 左右布局容器 */}
            <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[600px]">
              {/* 左侧内容区域 */}
              <div className="text-white lg:text-left text-center">
                <div className="mb-8 flex lg:justify-start justify-center">
                  <div className="inline-flex items-center rounded-full bg-white/10 px-6 py-3 text-sm font-medium backdrop-blur-lg border border-white/20 shadow-xl">
                    <Cpu className="mr-2 h-4 w-4 text-cyan-400" />
                    <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent font-semibold">
                      AI 驱动创作平台
                    </span>
                  </div>
                </div>
                <h1 className="mb-6 text-4xl font-extrabold tracking-tight sm:text-5xl lg:text-6xl">
                  <span className="block mb-4">免费AI提示词插件</span>
                  <span className="bg-gradient-to-r from-cyan-400 via-violet-400 to-purple-500 bg-clip-text text-transparent">
                    任意AI工具一键使用
                  </span>
                </h1>
                <p className="mb-8 text-lg leading-8 text-slate-300 sm:text-xl lg:max-w-none max-w-3xl mx-auto lg:mx-0">
                  汇聚全球顶尖 AI 提示词，让每一次对话都创造价值
                  <br className="hidden sm:block" />
                  <span className="text-cyan-400 font-semibold">{stats.totalUsers.toLocaleString()}+ 创作者</span> 的智慧结晶，助你驾驭 AI 新时代
                </p>
                <div className="mb-8 lg:max-w-2xl max-w-4xl mx-auto lg:mx-0">
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400 via-violet-500 to-purple-600 rounded-3xl blur-xl opacity-20 group-hover:opacity-40 transition-all duration-500"></div>
                    <div className="relative bg-white/98 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/50 group-hover:shadow-3xl transition-all duration-300">
                      <div className="flex items-center">
                        <div className="absolute left-6 z-10">
                          <Search className="h-5 w-5 text-gray-400 group-hover:text-cyan-500 transition-colors duration-300" />
                        </div>
                        <Input
                          type="text"
                          placeholder="搜索你AI提示词，如「GPT提示词」「抖音脚本」「cursor规则」「爆款文案提示词」..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                          className="h-16 w-full border-0 bg-transparent pl-14 pr-32 text-lg placeholder:text-gray-400 focus:outline-none focus:ring-0 focus-visible:border-0 focus-visible:ring-0"
                        />
                        <div className="absolute right-2 top-2">
                          <Button onClick={handleSearch} className="h-12 px-6 rounded-2xl bg-gradient-to-r from-cyan-500 to-violet-600 hover:from-cyan-600 hover:to-violet-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300">
                            <Wand2 className="mr-2 h-4 w-4" />
                            <span className="hidden sm:inline">探索</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 右侧图片区域 */}
              <div className="lg:flex hidden justify-center items-center">
                <div className="relative w-full max-w-4xl">
                  {/* 背景装饰 */}
                  <div className="absolute -inset-8 bg-gradient-to-r from-cyan-400/20 via-violet-500/20 to-purple-600/20 rounded-3xl blur-2xl"></div>
                  <div className="absolute -inset-4 bg-gradient-to-r from-white/10 to-white/5 rounded-2xl backdrop-blur-sm border border-white/20"></div>

                  {/* 主图片容器 */}
                  <div className="relative bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/50 p-6">
                    <Link href="/extension" target="_blank" className="block cursor-pointer">
                      <img
                        src="/hero-demo.png"
                        alt="PromptHub AI提示词平台演示 - ChatGPT Claude Midjourney提示词工具"
                        className="w-full h-auto rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 scale-105"
                        title="打开GPT、Gemini、DeepSeek、豆包等任意ai工具，提示词库一键调用。"
                      />
                    </Link>

                    {/* 浮动标签 */}
                    <div className="absolute -top-5 -right-5 bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-4 rounded-full text-lg font-semibold shadow-lg">
                      <span className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                        实时在线
                      </span>
                    </div>

                    {/* 底部下载按钮 */}
                    <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
                      <Link href="/extension" target="_blank">
                        <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-base font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2">
                          <Download className="w-4 h-4" />
                          免费下载插件
                          <span className="text-xs bg-blue-500 px-2 py-0.5 rounded ml-2">v2.0.0</span>
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Prompts Section */}
      <section className="bg-gradient-to-br from-slate-50 to-blue-50 px-4 py-16 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="flex justify-center mb-8">
            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full max-w-xs sm:max-w-sm">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="hot">
                  <TrendingUp className="mr-1 sm:mr-2 h-4 w-4" /> 热门推荐
                </TabsTrigger>
                <TabsTrigger value="likes">
                  <Heart className="mr-1 sm:mr-2 h-4 w-4" /> 最受欢迎
                </TabsTrigger>
                <TabsTrigger value="newest">
                  <Wand2 className="mr-1 sm:mr-2 h-4 w-4" /> 最新上架
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          
          <div className="grid gap-6 lg:grid-cols-2 xl:grid-cols-3">
            {prompts.map((prompt) => (
              <PromptCard key={prompt.id} prompt={prompt} />
            ))}
          </div>
          
          {loading && (
            <div className="mt-8 text-center text-gray-600">加载中...</div>
          )}

          {!loading && pagination.hasNext && (
            <div className="flex justify-center mt-8">
              <Button
                onClick={loadMorePrompts}
                disabled={loading}
                variant="outline"
                size="lg"
                className="flex items-center gap-2 px-8 py-3 text-violet-600 border-violet-200 hover:border-violet-300 hover:bg-violet-50"
              >
                {loading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-violet-600 border-t-transparent rounded-full animate-spin"></div>
                    <span>加载中...</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="w-5 h-5" />
                    <span>加载更多提示词</span>
                  </>
                )}
              </Button>
            </div>
          )}

          {!loading && !pagination.hasNext && prompts.length > 0 && (
            <div className="mt-12 text-center">
              <Link href="/prompts">
                <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl shadow-lg">
                  探索更多提示词 →
                </Button>
              </Link>
            </div>
          )}
        </div>
      </section>

      {/* Browser Extension Section */}
      <section className="bg-gradient-to-r from-violet-600 to-cyan-600 px-4 py-20 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="text-center text-white">
            <div className="mb-8 flex justify-center">
              <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/30">
                <img
                  src="/extension-icon.png"
                  alt="PromptHub免费AI提示词浏览器插件 - ChatGPT Claude Midjourney提示词工具"
                  className="w-12 h-12 rounded-lg"
                  title="免费AI提示词插件，支持ChatGPT、Claude、Midjourney等主流AI工具"
                />
              </div>
            </div>
            <h2 className="mb-6 text-4xl font-bold">
              免费AI提示词浏览器插件
            </h2>
            <p className="mx-auto mb-8 max-w-3xl text-xl text-white/90 leading-relaxed">
              安装PromptHub浏览器插件，在任何网页上都能快速访问10000+免费提示词。
              支持ChatGPT、Claude等主流AI工具，让提示词工程更加高效便捷。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Link href="/extension">
                <Button size="lg" className="bg-yellow-500 text-black hover:bg-yellow-400 px-8 py-4 text-lg gap-3 shadow-xl font-bold border-2 border-yellow-300">
                  <Download className="w-5 h-5" />
                  免费下载插件
                </Button>
              </Link>
              <div className="flex items-center gap-6 text-white/80">
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span className="text-sm">10,000+ 用户使用</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm">4.8 评分</span>
                </div>
              </div>
            </div>
            
            {/* Features */}
            <div className="grid gap-6 md:grid-cols-3 max-w-4xl mx-auto">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4 mx-auto">
                  <MousePointer className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold mb-2">拖拽悬浮按钮</h3>
                <p className="text-white/80 text-sm">随时随地快速访问，支持自定义位置</p>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4 mx-auto">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold mb-2">一键复制使用</h3>
                <p className="text-white/80 text-sm">选中文本，右键即可创建提示词</p>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4 mx-auto">
                  <Keyboard className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold mb-2">快捷键支持</h3>
                <p className="text-white/80 text-sm">Ctrl+Shift+P 快速开启侧边栏</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-gradient-to-br from-slate-50 to-blue-50 px-4 py-20 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="mb-12 text-center">
            <h2 className="mb-4 text-4xl font-bold text-gray-900">AI提示词平台数据</h2>
            <p className="text-lg text-gray-600">免费提示词库，助力AI提示词工程学习</p>
          </div>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {[
              { label: '优质提示词', value: stats.totalPrompts, icon: BookOpen, gradient: 'from-cyan-500 to-blue-600' },
              { label: '活跃创作者', value: stats.totalUsers, icon: Users, gradient: 'from-violet-500 to-purple-600' },
              { label: '专业领域', value: stats.totalCategories, icon: Target, gradient: 'from-orange-500 to-red-500' },
              { label: '每日使用', value: stats.dailyActive, icon: TrendingUp, gradient: 'from-emerald-500 to-green-600' }
            ].map((stat, index) => (
              <Card key={index} className="group border-0 bg-white/90 backdrop-blur-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <CardContent className="p-8 text-center">
                  <div className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r ${stat.gradient} group-hover:scale-110 transition-transform`}>
                    <stat.icon className="h-8 w-8 text-white" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-1">{stat.value.toLocaleString()}+</div>
                  <div className="text-sm font-medium text-gray-600">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
} 
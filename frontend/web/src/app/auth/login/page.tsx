'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { User, Lock, LogIn, Sparkles } from 'lucide-react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { API_BASE_URL } from '@/lib/api'

export default function LoginPage() {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()
  const searchParams = useSearchParams()
  const { login } = useAuth()
  
  // 检查是否来自浏览器扩展
  const isFromExtension = searchParams.get('extension') === 'true'
  const callbackType = searchParams.get('callback')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    try {
      // 检查是否为扩展登录
      const isExtensionLogin = searchParams.get('extension') === 'true'

      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(isExtensionLogin && { 'x-requested-with': 'extension' })
        },
        body: JSON.stringify({ identifier: username, password }),
        credentials: 'include' // 重要：包含Cookie以接收服务器设置的Cookie
      })

      const data = await response.json()
      
      if (response.ok && data.success) {
        const callbackType = searchParams.get('callback')

        // 保存用户信息到localStorage
        if (data.data && data.data.user) {
          localStorage.setItem('user', JSON.stringify(data.data.user))
        }

        if (isExtensionLogin) {
          console.log('🔌 检测到扩展登录，准备跳转回调页面...')
          console.log('📍 回调类型:', callbackType)

          // 扩展登录需要token
          if (data.data && data.data.token) {
            // 保存认证信息到localStorage（插件需要）
            localStorage.setItem('authToken', data.data.token)
            localStorage.setItem('token', data.data.token) // 兼容字段
            localStorage.setItem('auth_token', data.data.token) // 插件兼容字段

            console.log('✅ 扩展登录成功，Token已保存到localStorage')
            console.log('🔐 保存的Token字段: authToken, token, auth_token')

            // 使用绝对URL确保跳转成功
            const callbackUrl = `${window.location.origin}/auth/extension-callback?token=${encodeURIComponent(data.data.token)}&source=login`
            console.log('📍 回调URL:', callbackUrl)

            // 延迟跳转，确保localStorage保存完成
            setTimeout(() => {
              window.location.href = callbackUrl
            }, 100)

            return
          } else {
            console.error('❌ 扩展登录失败：未收到token')
            setError('扩展登录失败，请重试')
            return
          }
        }

        // 普通网页登录，使用Cookie认证
        console.log('✅ 网页登录成功，使用Cookie认证')
        window.location.href = '/'
      } else {
        console.error('❌ 登录失败:', data)
        setError(data.message || '登录失败，请检查邮箱和密码')
      }
    } catch (error) {
      console.error('❌ 登录请求失败:', error)
      setError('网络错误，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50 overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-violet-100/50 via-transparent to-cyan-100/50"></div>
      <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-violet-300/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyan-300/20 rounded-full blur-3xl"></div>

      <div className="relative flex items-center justify-center h-full px-4">
        <div className="w-full max-w-6xl grid lg:grid-cols-2 gap-12 items-center">
          
          {/* 左侧品牌展示区域 - 使用 PromptHub 风格 */}
          <div className="hidden lg:flex lg:items-center lg:justify-start">
            <div className="space-y-6">
              <div className="space-y-5">
                {/* PromptHub Logo */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className="relative">
                    <div className="w-16 h-16 bg-gradient-to-br from-violet-600 to-cyan-600 rounded-xl flex items-center justify-center">
                      <Sparkles className="w-10 h-10 text-white" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent">
                      PromptHub
                    </h1>
                    <div className="text-lg text-gray-500 font-medium">AI 提示词平台</div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h2 className="text-2xl font-bold text-gray-900">
                    欢迎回到 PromptHub
                  </h2>
                  <div className="space-y-2 text-gray-600 text-base leading-relaxed">
                    <p>🚀 汇聚全球最优秀的 AI 提示词资源</p>
                    <p>🎨 涵盖图像生成、智能代理、MCP工具等多个领域</p>
                    <p>🤝 加入 50,000+ 创作者社区</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧登录表单 */}
          <div className="w-full max-w-md mx-auto lg:ml-16 lg:mr-0">
            <Card className="shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
              <CardContent className="p-6">
                {/* 登录表单头部 */}
                <div className="text-center mb-6">
                  <div className="inline-flex items-center justify-center w-14 h-14 bg-gradient-to-br from-violet-100 to-cyan-100 rounded-full mb-3">
                    <User className="w-7 h-7 bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent" />
                  </div>
                  <h2 className="text-xl font-bold text-gray-900 mb-1">
                    {isFromExtension ? '浏览器扩展登录' : '欢迎登录'}
                  </h2>
                  <p className="text-gray-600 text-sm">
                    {isFromExtension ? '登录后将自动同步到您的浏览器扩展' : '请输入您的账户信息'}
                  </p>
                  {isFromExtension && (
                    <div className="mt-3 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 text-xs">
                      🔌 来自 PromptHub 浏览器扩展
                    </div>
                  )}
                </div>

                {/* 错误提示 */}
                {error && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md text-sm">
                    {error}
                  </div>
                )}

                {/* 登录表单 */}
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-1">
                    <Label htmlFor="username" className="text-sm font-medium text-gray-700">
                      用户名或邮箱
                    </Label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <Input
                        id="username"
                        type="text"
                        placeholder="请输入用户名或邮箱"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        className="pl-10 h-11 border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-1">
                    <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                      密码
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <Input
                        id="password"
                        type="password"
                        placeholder="请输入密码"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="pl-10 h-11 border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                        required
                      />
                    </div>
                  </div>

                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="w-full h-11 bg-gradient-to-r from-violet-600 to-cyan-600 hover:from-violet-700 hover:to-cyan-700 text-white font-medium text-base rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl mt-5"
                  >
                    {isLoading ? (
                      <div className="flex items-center">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        登录中...
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <LogIn className="w-4 h-4 mr-2" />
                        登录系统
                      </div>
                    )}
                  </Button>

                  <div className="text-center pt-3">
                    <span className="text-gray-600 text-sm">还没有账号？</span>
                    <Link href="/auth/register" className="text-violet-600 hover:text-violet-700 font-medium ml-1 text-sm">
                      立即注册
                    </Link>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
} 
'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { User, Mail, Lock, UserPlus, Sparkles } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'

export default function RegisterPage() {
  const [username, setUsername] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()
  const { register } = useAuth()

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    
    // 基本验证
    if (password !== confirmPassword) {
      setError('两次输入的密码不一致')
      setIsLoading(false)
      return
    }

    if (password.length < 6) {
      setError('密码长度至少为6位')
      setIsLoading(false)
      return
    }

    try {
      console.log('🔑 尝试注册:', { username, email })
      
      // 正确传递注册数据
      // @ts-ignore - useAuth.js 是 JavaScript 文件，TypeScript 无法正确推断函数参数类型
      await register({
        username: username,
        email: email,
        password: password
      })
      
      console.log('✅ 注册成功')
      // 跳转到首页
      router.push('/')
    } catch (error: unknown) {
      console.error('❌ 注册失败:', error)
      setError(error instanceof Error ? error.message : '注册失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50 overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-violet-100/50 via-transparent to-cyan-100/50"></div>
      <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-violet-300/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyan-300/20 rounded-full blur-3xl"></div>

      <div className="relative flex items-center justify-center h-full px-4">
        <div className="w-full max-w-6xl grid lg:grid-cols-2 gap-12 items-center">
          
          {/* 左侧品牌展示区域 */}
          <div className="hidden lg:flex lg:items-center lg:justify-start">
            <div className="space-y-6">
              <div className="space-y-5">
                {/* PromptHub Logo */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className="relative">
                    <div className="w-16 h-16 bg-gradient-to-br from-violet-600 to-cyan-600 rounded-xl flex items-center justify-center">
                      <Sparkles className="w-10 h-10 text-white" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent">
                      PromptHub
                    </h1>
                    <div className="text-lg text-gray-500 font-medium">AI 提示词平台</div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h2 className="text-2xl font-bold text-gray-900">
                    加入 PromptHub 社区
                  </h2>
                  <div className="space-y-2 text-gray-600 text-base leading-relaxed">
                    <p>🚀 免费使用全球最优秀的 AI 提示词</p>
                    <p>🎨 分享你的创意提示词给世界</p>
                    <p>🤝 与 50,000+ 创作者互动交流</p>
                    <p>✨ 享受个性化的提示词推荐</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧注册表单 */}
          <div className="w-full max-w-md mx-auto lg:ml-16 lg:mr-0">
            <Card className="shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
              <CardContent className="p-6">
                {/* 注册表单头部 */}
                <div className="text-center mb-6">
                  <div className="inline-flex items-center justify-center w-14 h-14 bg-gradient-to-br from-violet-100 to-cyan-100 rounded-full mb-3">
                    <UserPlus className="w-7 h-7 bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent" />
                  </div>
                  <h2 className="text-xl font-bold text-gray-900 mb-1">创建账户</h2>
                  <p className="text-gray-600 text-sm">填写以下信息完成注册</p>
                </div>

                {/* 错误提示 */}
                {error && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md text-sm">
                    {error}
                  </div>
                )}

                {/* 注册表单 */}
                <form onSubmit={handleRegister} className="space-y-4">
                  <div className="space-y-1">
                    <Label htmlFor="username" className="text-sm font-medium text-gray-700">
                      用户名
                    </Label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <Input
                        id="username"
                        type="text"
                        placeholder="请输入用户名"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        className="pl-10 h-11 border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-1">
                    <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                      邮箱地址
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="请输入邮箱地址"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="pl-10 h-11 border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-1">
                    <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                      密码
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <Input
                        id="password"
                        type="password"
                        placeholder="请输入密码（至少6位）"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="pl-10 h-11 border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                        required
                        minLength={6}
                      />
                    </div>
                  </div>

                  <div className="space-y-1">
                    <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                      确认密码
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <Input
                        id="confirmPassword"
                        type="password"
                        placeholder="请再次输入密码"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="pl-10 h-11 border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                        required
                      />
                    </div>
                  </div>

                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="w-full h-11 bg-gradient-to-r from-violet-600 to-cyan-600 hover:from-violet-700 hover:to-cyan-700 text-white font-medium text-base rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl mt-5"
                  >
                    {isLoading ? (
                      <div className="flex items-center">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        注册中...
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <UserPlus className="w-4 h-4 mr-2" />
                        创建账户
                      </div>
                    )}
                  </Button>

                  <div className="text-center pt-3">
                    <span className="text-gray-600 text-sm">已有账号？</span>
                    <Link href="/auth/login" className="text-violet-600 hover:text-violet-700 font-medium ml-1 text-sm">
                      立即登录
                    </Link>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
} 
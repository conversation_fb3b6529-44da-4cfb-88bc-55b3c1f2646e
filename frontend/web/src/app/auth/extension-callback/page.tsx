'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'

export default function ExtensionCallback() {
  const searchParams = useSearchParams()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('正在处理登录...')

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // 检查URL参数
        const token = searchParams.get('token')
        const error = searchParams.get('error')
        
        if (error) {
          setStatus('error')
          setMessage(`登录失败: ${error}`)
          return
        }

        // 检查token或Cookie认证
        const API_BASE_URL = process.env.NODE_ENV === 'production' ? 'https://prompthub.xin/api' : 'http://localhost:4000/api'

        let response
        let authMethod = 'cookie'

        if (token) {
          // 如果有token，使用token认证
          authMethod = 'token'
          response = await fetch(`${API_BASE_URL}/auth/me`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })
        } else {
          // 否则使用Cookie认证
          response = await fetch(`${API_BASE_URL}/auth/me`, {
            credentials: 'include'
          })
        }

        if (response.ok) {
          const userData = await response.json()
          console.log(`✅ 插件登录成功 (${authMethod}):`, userData)

          setStatus('success')
          setMessage(`欢迎回来，${userData.data?.username || '用户'}！`)

          // 如果使用token认证，保存到localStorage
          if (token) {
            localStorage.setItem('authToken', token)
            localStorage.setItem('token', token)
            localStorage.setItem('auth_token', token)
            localStorage.setItem('userData', JSON.stringify(userData.data))
            localStorage.setItem('user', JSON.stringify(userData.data))
            console.log('🔐 Token已保存到localStorage')
          }

          // 通知主站刷新认证状态
          try {
            // 发送自定义事件通知主站刷新
            window.dispatchEvent(new CustomEvent('auth-state-changed', {
              detail: {
                type: 'LOGIN_SUCCESS',
                user: userData.data,
                token: token || null
              }
            }))
            console.log('📢 已通知主站刷新认证状态')
          } catch (error) {
            console.warn('⚠️ 通知主站失败:', error)
          }

          // 通知插件登录成功 - 使用多种方式确保消息传递
          const loginMessage = {
            type: 'LOGIN_SUCCESS',
            user: userData.data,
            token: token || null,
            timestamp: Date.now()
          }

          // 方式1: 尝试直接发送Chrome消息（如果在插件环境中）
          if (typeof window !== 'undefined' && (window as any).chrome && (window as any).chrome.runtime && (window as any).chrome.runtime.sendMessage) {
            try {
              (window as any).chrome.runtime.sendMessage(loginMessage)
              console.log('✅ Chrome消息已发送')
            } catch (error) {
              console.log('⚠️ Chrome消息发送失败:', error)
            }
          }

          // 方式2: 通过content script发送消息给插件
          try {
            // 发送自定义事件给content script
            const customEvent = new CustomEvent('prompthub-login-success', {
              detail: loginMessage
            })
            window.dispatchEvent(customEvent)
            console.log('🎯 CustomEvent已发送给content script')
          } catch (error) {
            console.log('⚠️ CustomEvent发送失败:', error)
          }

          // 方式3: 使用postMessage通知所有窗口（包括插件）
          try {
            window.postMessage(loginMessage, '*')
            console.log('📢 PostMessage已发送')
          } catch (error) {
            console.log('⚠️ PostMessage发送失败:', error)
          }

          // 方式4: 使用localStorage触发storage事件
          try {
            localStorage.setItem('prompthub-login-event', JSON.stringify(loginMessage))
            // 立即删除，只是为了触发storage事件
            setTimeout(() => {
              localStorage.removeItem('prompthub-login-event')
            }, 100)
            console.log('📦 Storage事件已触发')
          } catch (error) {
            console.log('⚠️ Storage事件触发失败:', error)
          }

          // 3秒后自动关闭
          setTimeout(() => {
            window.close()
          }, 3000)

        } else {
          throw new Error(`认证检查失败 (${authMethod})`)
        }

      } catch (error) {
        console.error('❌ 插件回调处理失败:', error)
        setStatus('error')
        setMessage('认证处理失败，请重试')
      }
    }

    handleCallback()
  }, [searchParams])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
        <div className="mb-6">
          <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            {status === 'loading' && (
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            )}
            {status === 'success' && (
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            )}
            {status === 'error' && (
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            )}
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            PromptHub 插件认证
          </h1>
          
          <p className="text-gray-600">
            {message}
          </p>
        </div>

        {status === 'success' && (
          <div className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-800">
                    插件已成功连接到您的账户
                  </p>
                  <p className="text-sm text-green-700 mt-1">
                    现在您可以在插件中直接使用所有功能
                  </p>
                </div>
              </div>
            </div>
            
            <p className="text-sm text-gray-500">
              此窗口将在几秒钟后自动关闭...
            </p>
          </div>
        )}

        {status === 'error' && (
          <div className="space-y-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-red-800">
                    插件连接失败
                  </p>
                  <p className="text-sm text-red-700 mt-1">
                    请返回插件重新尝试登录
                  </p>
                </div>
              </div>
            </div>
            
            <button
              onClick={() => window.close()}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              关闭窗口
            </button>
          </div>
        )}

        {status === 'loading' && (
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-sm text-blue-700">
                正在验证您的登录状态，请稍候...
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 
import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Palette,
  TrendingUp,
  Lightbulb,
  MessageSquare,
  Briefcase,
  Users,
  ArrowRight,
  Star,
  Copy,
  Download,
  Eye
} from 'lucide-react'

// 获取设计师相关提示词数据
async function getDesignerPrompts() {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:4000/api'

    // 获取精选推荐（搜索设计师相关提示词，按复制量排序）
    const featuredResponse = await fetch(`${API_BASE_URL}/prompts/public?search=设计师&sortBy=downloads&limit=3`, {
      next: { revalidate: 300 }, // 5分钟缓存
      signal: AbortSignal.timeout(5000)
    })

    let featuredPrompts = []
    if (featuredResponse.ok) {
      const featuredResult = await featuredResponse.json()
      if (featuredResult.success && featuredResult.data) {
        featuredPrompts = featuredResult.data
      }
    }

    return { featuredPrompts }
  } catch (error) {
    console.error('获取设计师提示词数据失败:', error)
    // 返回模拟数据作为后备
    return {
      featuredPrompts: [
        {
          id: 'designer-demo-1',
          title: 'UI设计说明生成器',
          description: '为UI设计方案生成专业的设计说明文档',
          categoryDisplayName: '设计说明',
          downloads: 3456,
          likes: 234,
          views: 12890
        },
        {
          id: 'designer-demo-2',
          title: '品牌视觉方案助手',
          description: '创建完整的品牌视觉识别系统方案',
          categoryDisplayName: '品牌设计',
          downloads: 2987,
          likes: 198,
          views: 10567
        },
        {
          id: 'designer-demo-3',
          title: '设计评审意见模板',
          description: '生成专业的设计评审和反馈意见',
          categoryDisplayName: '客户沟通',
          downloads: 2654,
          likes: 176,
          views: 9234
        }
      ]
    }
  }
}

// 根据标签获取分类提示词
async function getPromptsByTags(tags: string[], limit = 3) {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:4000/api'

    // 使用标签筛选功能来查找包含特定标签的提示词
    const response = await fetch(`${API_BASE_URL}/prompts/public?tags=${encodeURIComponent(tags.join(','))}&sortBy=downloads&limit=${limit}`, {
      next: { revalidate: 300 },
      signal: AbortSignal.timeout(5000)
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        return result.data
      }
    }

    return []
  } catch (error) {
    console.error('获取标签提示词失败:', error)
    // 返回模拟数据作为后备
    const mockData = {
      '创意灵感': [
        { id: 'idea-1', title: '设计概念生成器', description: '根据项目需求生成创新的设计概念', downloads: 3456, likes: 234, views: 12890, tags: ['设计概念', '创意思维', '灵感启发'] },
        { id: 'idea-2', title: '色彩搭配建议', description: '提供专业的色彩搭配方案和建议', downloads: 2987, likes: 198, views: 10567, tags: ['色彩搭配', '视觉设计', '配色方案'] },
        { id: 'idea-3', title: '设计趋势分析', description: '分析当前设计趋势和流行元素', downloads: 2654, likes: 176, views: 9234, tags: ['设计趋势', '流行元素', '市场分析'] }
      ],
      '设计说明': [
        { id: 'desc-1', title: '设计理念阐述', description: '清晰阐述设计理念和创作思路', downloads: 3789, likes: 245, views: 13456, tags: ['设计理念', '创作思路', '设计说明'] },
        { id: 'desc-2', title: '视觉元素解释', description: '详细解释设计中的视觉元素运用', downloads: 3234, likes: 212, views: 11234, tags: ['视觉元素', '设计解释', '元素分析'] },
        { id: 'desc-3', title: '设计规范文档', description: '制定完整的设计规范和标准', downloads: 2876, likes: 187, views: 9876, tags: ['设计规范', '标准制定', '规范文档'] }
      ],
      '客户沟通': [
        { id: 'comm-1', title: '设计提案撰写', description: '撰写专业的设计提案和方案书', downloads: 3567, likes: 234, views: 12456, tags: ['设计提案', '方案书', '客户沟通'] },
        { id: 'comm-2', title: '修改意见回复', description: '专业回复客户的修改意见和建议', downloads: 3123, likes: 201, views: 10789, tags: ['修改意见', '客户反馈', '沟通技巧'] },
        { id: 'comm-3', title: '项目进度汇报', description: '制作清晰的项目进度汇报材料', downloads: 2789, likes: 178, views: 9456, tags: ['进度汇报', '项目管理', '客户汇报'] }
      ],
      '品牌策划': [
        { id: 'brand-1', title: '品牌故事创作', description: '创作引人入胜的品牌故事和理念', downloads: 3890, likes: 256, views: 13567, tags: ['品牌故事', '品牌理念', '故事创作'] },
        { id: 'brand-2', title: '视觉识别系统', description: '设计完整的品牌视觉识别系统', downloads: 3456, likes: 223, views: 12123, tags: ['视觉识别', '品牌系统', 'VI设计'] },
        { id: 'brand-3', title: '品牌定位分析', description: '分析品牌定位和市场竞争优势', downloads: 3123, likes: 198, views: 10890, tags: ['品牌定位', '市场分析', '竞争优势'] }
      ]
    }

    // 根据标签匹配返回对应的模拟数据
    const firstTag = tags[0]
    if (firstTag.includes('创意') || firstTag.includes('灵感') || firstTag.includes('概念')) {
      return mockData['创意灵感']
    } else if (firstTag.includes('设计说明') || firstTag.includes('理念') || firstTag.includes('规范')) {
      return mockData['设计说明']
    } else if (firstTag.includes('客户') || firstTag.includes('沟通') || firstTag.includes('提案')) {
      return mockData['客户沟通']
    } else if (firstTag.includes('品牌') || firstTag.includes('策划') || firstTag.includes('VI')) {
      return mockData['品牌策划']
    }
    return mockData['创意灵感'] || []
  }
}

export const metadata: Metadata = {
  title: '设计师专区 - 创意灵感、设计说明、客户沟通AI提示词库',
  description: '专为设计师打造的AI提示词库，包含设计理念阐述、创意方案生成、客户提案撰写、品牌故事创作等专业工具。',
  keywords: [
    // 设计师核心关键词
    '设计师AI提示词', '创意灵感AI助手', '设计说明生成工具',
    '客户沟通Prompt', '设计AI工具', '创意方案生成器',
    
    // 创意灵感
    '设计概念生成Prompt', '创意思维AI',
    '色彩搭配建议', '设计趋势分析AI',
    '灵感启发工具', '创意方案助手',
    
    // 设计说明
    '设计理念阐述AI', '视觉元素解释',
    '设计规范文档', '设计说明模板',
    '创作思路生成器', '设计解释助手',
    
    // 客户沟通
    '设计提案撰写AI', '客户沟通模板',
    '修改意见回复', '项目进度汇报',
    '客户反馈处理', '沟通技巧助手',
    
    // 品牌策划
    '品牌故事创作AI', '视觉识别系统',
    '品牌定位分析', 'VI设计助手',
    '品牌理念生成器', '品牌策划方案'
  ],
  openGraph: {
    title: '设计师专区 - 创意灵感、设计说明、客户沟通AI提示词库',
    description: '专为设计师打造的AI提示词库，包含设计理念阐述、创意方案生成、客户提案撰写等专业工具。',
    type: 'article',
    url: 'https://www.prompthub.xin/for/designers',
    siteName: 'PromptHub',
    locale: 'zh_CN',
  },
  twitter: {
    card: 'summary_large_image',
    title: '设计师专区 - 创意灵感、设计说明、客户沟通AI提示词库',
    description: '专为设计师打造的AI提示词库，包含设计理念阐述、创意方案生成、客户提案撰写等专业工具。',
  },
  alternates: {
    canonical: 'https://www.prompthub.xin/for/designers',
  },
}

// 定义分类配置（用于获取对应标签的提示词）
const categories = [
  {
    title: '创意灵感',
    description: '设计概念、色彩搭配、趋势分析工具',
    icon: Lightbulb,
    color: 'from-orange-500 to-amber-500',
    tags: ['创意灵感', '设计概念', '色彩搭配', '设计趋势', '灵感启发', '创意思维']
  },
  {
    title: '设计说明',
    description: '设计理念、视觉元素、规范文档工具',
    icon: Palette,
    color: 'from-purple-500 to-violet-500',
    tags: ['设计说明', '设计理念', '视觉元素', '设计规范', '创作思路', '设计解释']
  },
  {
    title: '客户沟通',
    description: '设计提案、修改回复、进度汇报工具',
    icon: MessageSquare,
    color: 'from-blue-500 to-cyan-500',
    tags: ['客户沟通', '设计提案', '修改意见', '项目进度', '客户反馈', '沟通技巧']
  },
  {
    title: '品牌策划',
    description: '品牌故事、视觉识别、定位分析工具',
    icon: Briefcase,
    color: 'from-green-500 to-emerald-500',
    tags: ['品牌策划', '品牌故事', '视觉识别', '品牌定位', 'VI设计', '品牌理念']
  }
]

export default async function DesignersPage() {
  // 获取真实数据
  const { featuredPrompts } = await getDesignerPrompts()

  // 为每个分类获取对应的提示词
  const categoriesWithPrompts = await Promise.all(
    categories.map(async (category) => ({
      ...category,
      prompts: await getPromptsByTags(category.tags, 3)
    }))
  )

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面头部 */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-orange-500 to-amber-500 text-white mb-4">
          <Palette className="h-8 w-8" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          设计师专区
        </h1>
        <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
          专为设计师打造的AI提示词库，涵盖创意灵感、设计说明、客户沟通、品牌策划等各个环节，
          让AI成为你的设计利器。
        </p>

        {/* 统计数据 */}
        <div className="flex items-center justify-center gap-8 mt-6 text-sm text-gray-600">
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-1" />
            1.5万+ 设计师在使用
          </div>
          <div className="flex items-center">
            <Star className="h-4 w-4 mr-1 text-yellow-500" />
            300+ 专业提示词
          </div>
          <div className="flex items-center">
            <TrendingUp className="h-4 w-4 mr-1 text-green-500" />
            每日新增使用
          </div>
        </div>
      </div>

      {/* 精选提示词 */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Star className="h-6 w-6 text-yellow-500 mr-2" />
            精选推荐
          </h2>
          <Link
            href="/prompts?search=设计师"
            className="text-purple-600 hover:text-purple-700 transition-colors text-sm font-medium"
          >
            查看全部 →
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {featuredPrompts.length > 0 ? featuredPrompts.map((prompt, index) => (
            <Card key={prompt.id || index} className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm h-[280px] flex flex-col">
              <CardHeader className="pb-4 flex-shrink-0">
                <div className="flex items-start justify-between mb-2">
                  <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                    精选
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {prompt.categoryDisplayName || '视觉设计'}
                  </Badge>
                </div>
                <CardTitle className="text-lg group-hover:text-purple-600 transition-colors line-clamp-1">
                  {prompt.title}
                </CardTitle>
                <CardDescription className="text-sm text-gray-600 leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                  {prompt.description || '专业的设计AI提示词，助力您的设计工作'}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0 flex-grow flex flex-col justify-end">
                <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <Copy className="h-3 w-3 mr-1" />
                      {prompt.downloads || 0}
                    </span>
                    <span className="flex items-center">
                      <Star className="h-3 w-3 mr-1" />
                      {prompt.likes || 0}
                    </span>
                    <span className="flex items-center">
                      <Eye className="h-3 w-3 mr-1" />
                      {prompt.views || 0}
                    </span>
                  </div>
                </div>

                <Link href={`/prompts/${prompt.id}`}>
                  <Button className="w-full bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white border-0">
                    立即使用
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )) : (
            // 空状态显示
            Array.from({ length: 3 }).map((_, index) => (
              <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm h-[280px] flex flex-col">
                <CardHeader className="pb-4 flex-shrink-0">
                  <div className="flex items-start justify-between mb-2">
                    <Badge className="bg-gray-100 text-gray-500">
                      暂无数据
                    </Badge>
                  </div>
                  <CardTitle className="text-lg text-gray-400 line-clamp-2">
                    正在加载精选提示词...
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-400 line-clamp-3 h-[60px] overflow-hidden">
                    请稍候，我们正在为您准备最优质的设计提示词
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0 flex-grow flex flex-col justify-end">
                  <div className="flex items-center justify-between text-xs text-gray-400 mb-4">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <Copy className="h-3 w-3 mr-1" />
                        --
                      </span>
                      <span className="flex items-center">
                        <Star className="h-3 w-3 mr-1" />
                        --
                      </span>
                      <span className="flex items-center">
                        <Eye className="h-3 w-3 mr-1" />
                        --
                      </span>
                    </div>
                  </div>

                  <Button disabled className="w-full bg-gray-200 text-gray-400 border-0">
                    暂无数据
                  </Button>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* 分类提示词 */}
      <div className="space-y-8">
        <h2 className="text-2xl font-bold text-gray-900">
          分类提示词库
        </h2>

        {categoriesWithPrompts.map((category, categoryIndex) => {
          const Icon = category.icon
          return (
            <div key={categoryIndex} className="space-y-4">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color} text-white mr-3`}>
                  <Icon className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{category.title}</h3>
                  <p className="text-sm text-gray-600">{category.description}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {category.prompts.length > 0 ? category.prompts.map((prompt, promptIndex) => (
                  <Card key={prompt.id || promptIndex} className="hover:shadow-md transition-shadow border-0 bg-white/60 backdrop-blur-sm h-[220px] flex flex-col">
                    <CardHeader className="pb-3 flex-shrink-0">
                      <CardTitle className="text-base line-clamp-2">{prompt.title}</CardTitle>
                      <CardDescription className="text-sm leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                        {prompt.description || '专业的AI提示词，提升您的工作效率'}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-3 flex-grow flex flex-col justify-end">
                      <div className="flex flex-wrap gap-1">
                        {(prompt.tags || []).slice(0, 2).map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>

                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center space-x-3">
                          <span className="flex items-center">
                            <Copy className="h-3 w-3 mr-1" />
                            {prompt.downloads || 0}
                          </span>
                          <span className="flex items-center">
                            <Star className="h-3 w-3 mr-1" />
                            {prompt.likes || 0}
                          </span>
                        </div>
                        <Link href={`/prompts/${prompt.id}`}>
                          <Button size="sm" variant="outline" className="h-7 text-xs">
                            使用
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                )) : (
                  // 空状态显示
                  Array.from({ length: 3 }).map((_, emptyIndex) => (
                    <Card key={emptyIndex} className="border-0 bg-white/40 backdrop-blur-sm h-[220px] flex flex-col">
                      <CardHeader className="pb-3 flex-shrink-0">
                        <CardTitle className="text-base text-gray-400 line-clamp-2">
                          正在加载{category.title}提示词...
                        </CardTitle>
                        <CardDescription className="text-sm text-gray-400 line-clamp-3 h-[58px] overflow-hidden">
                          请稍候，我们正在为您准备专业的{category.title}工具
                        </CardDescription>
                      </CardHeader>

                      <CardContent className="space-y-3 flex-grow flex flex-col justify-end">
                        <div className="flex flex-wrap gap-1">
                          <Badge variant="outline" className="text-xs text-gray-400">
                            {category.title}
                          </Badge>
                        </div>

                        <div className="flex items-center justify-between text-xs text-gray-400">
                          <div className="flex items-center space-x-3">
                            <span className="flex items-center">
                              <Copy className="h-3 w-3 mr-1" />
                              --
                            </span>
                            <span className="flex items-center">
                              <Star className="h-3 w-3 mr-1" />
                              --
                            </span>
                          </div>
                          <Button size="sm" variant="outline" className="h-7 text-xs" disabled>
                            暂无
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* 底部CTA */}
      <div className="text-center py-12">
        <div className="bg-gradient-to-r from-orange-600 to-amber-600 rounded-2xl p-8 text-white">
          <h2 className="text-2xl font-bold mb-4">
            开始你的AI设计之旅
          </h2>
          <p className="text-orange-100 mb-6 max-w-2xl mx-auto">
            安装PromptHub浏览器插件，随时随地使用专业的设计AI提示词，
            让你的设计工作更高效、更有创意。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/extension"
              className="inline-flex items-center px-6 py-3 bg-white text-orange-600 rounded-lg font-medium hover:bg-gray-50 transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              安装浏览器插件
            </Link>
            <Link
              href="/prompts?search=设计师"
              className="inline-flex items-center px-6 py-3 bg-orange-500 text-white rounded-lg font-medium hover:bg-orange-400 transition-colors"
            >
              浏览更多提示词
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </div>
        </div>
      </div>

      {/* 导航 */}
      <div className="flex justify-between items-center pt-8 border-t">
        <Link
          href="/for"
          className="inline-flex items-center text-purple-600 hover:text-purple-700 transition-colors"
        >
          ← 返回职业专区
        </Link>
        <Link
          href="/for/hr"
          className="inline-flex items-center text-purple-600 hover:text-purple-700 transition-colors"
        >
          下一个：人力资源专区
          <ArrowRight className="h-4 w-4 ml-1" />
        </Link>
      </div>
    </div>
  )
}

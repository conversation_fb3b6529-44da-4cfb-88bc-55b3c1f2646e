import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Users,
  TrendingUp,
  UserCheck,
  BookOpen,
  BarChart3,
  MessageCircle,
  ArrowRight,
  Star,
  Copy,
  Download,
  Eye
} from 'lucide-react'

// 获取人力资源相关提示词数据
async function getHRPrompts() {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:4000/api'

    // 获取精选推荐（搜索人力资源相关提示词，按复制量排序）
    const featuredResponse = await fetch(`${API_BASE_URL}/prompts/public?search=人力资源&sortBy=downloads&limit=3`, {
      next: { revalidate: 300 }, // 5分钟缓存
      signal: AbortSignal.timeout(5000)
    })

    let featuredPrompts = []
    if (featuredResponse.ok) {
      const featuredResult = await featuredResponse.json()
      if (featuredResult.success && featuredResult.data) {
        featuredPrompts = featuredResult.data
      }
    }

    return { featuredPrompts }
  } catch (error) {
    console.error('获取人力资源提示词数据失败:', error)
    // 返回模拟数据作为后备
    return {
      featuredPrompts: [
        {
          id: 'hr-demo-1',
          title: '招聘JD生成器',
          description: '快速生成专业的职位描述和招聘要求',
          categoryDisplayName: '招聘管理',
          downloads: 2789,
          likes: 189,
          views: 9876
        },
        {
          id: 'hr-demo-2',
          title: '面试评估表模板',
          description: '制作结构化的面试评估和打分表',
          categoryDisplayName: '面试管理',
          downloads: 2456,
          likes: 167,
          views: 8456
        },
        {
          id: 'hr-demo-3',
          title: '员工培训计划助手',
          description: '设计个性化的员工培训和发展计划',
          categoryDisplayName: '培训管理',
          downloads: 2234,
          likes: 145,
          views: 7234
        }
      ]
    }
  }
}

// 根据标签获取分类提示词
async function getPromptsByTags(tags: string[], limit = 3) {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:4000/api'

    // 使用标签筛选功能来查找包含特定标签的提示词
    const response = await fetch(`${API_BASE_URL}/prompts/public?tags=${encodeURIComponent(tags.join(','))}&sortBy=downloads&limit=${limit}`, {
      next: { revalidate: 300 },
      signal: AbortSignal.timeout(5000)
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        return result.data
      }
    }

    return []
  } catch (error) {
    console.error('获取标签提示词失败:', error)
    // 返回模拟数据作为后备
    const mockData = {
      '招聘面试': [
        { id: 'recruit-1', title: 'JD职位描述生成', description: '根据岗位需求生成专业的职位描述', downloads: 2789, likes: 189, views: 9876, tags: ['职位描述', '招聘需求', 'JD撰写'] },
        { id: 'recruit-2', title: '面试问题设计', description: '设计针对性的面试问题和评估标准', downloads: 2456, likes: 167, views: 8456, tags: ['面试问题', '评估标准', '面试技巧'] },
        { id: 'recruit-3', title: '候选人评估', description: '制作候选人综合评估和对比分析', downloads: 2234, likes: 145, views: 7234, tags: ['候选人评估', '人才筛选', '评估报告'] }
      ],
      '培训管理': [
        { id: 'train-1', title: '培训方案制定', description: '制定个性化的员工培训发展方案', downloads: 2567, likes: 178, views: 8901, tags: ['培训方案', '员工发展', '技能提升'] },
        { id: 'train-2', title: '培训效果评估', description: '评估培训效果和员工能力提升', downloads: 2123, likes: 143, views: 7456, tags: ['培训评估', '效果分析', '能力测评'] },
        { id: 'train-3', title: '学习路径规划', description: '为员工规划个性化的学习成长路径', downloads: 1987, likes: 134, views: 6789, tags: ['学习路径', '职业规划', '成长计划'] }
      ],
      '绩效评估': [
        { id: 'perf-1', title: '绩效评估模板', description: '制作全面的员工绩效评估表格', downloads: 2345, likes: 156, views: 8234, tags: ['绩效评估', '考核标准', '评估模板'] },
        { id: 'perf-2', title: '目标设定指导', description: '帮助员工设定SMART工作目标', downloads: 2098, likes: 142, views: 7123, tags: ['目标设定', 'SMART原则', '工作计划'] },
        { id: 'perf-3', title: '反馈沟通技巧', description: '提供有效的绩效反馈沟通方法', downloads: 1876, likes: 125, views: 6456, tags: ['绩效反馈', '沟通技巧', '员工辅导'] }
      ],
      '员工沟通': [
        { id: 'comm-1', title: '员工关怀方案', description: '设计贴心的员工关怀和福利方案', downloads: 2234, likes: 148, views: 7890, tags: ['员工关怀', '福利方案', '员工满意度'] },
        { id: 'comm-2', title: '离职面谈指南', description: '进行有效的离职面谈和原因分析', downloads: 1987, likes: 132, views: 6789, tags: ['离职面谈', '原因分析', '员工保留'] },
        { id: 'comm-3', title: '团队建设活动', description: '策划有趣的团队建设和文化活动', downloads: 1765, likes: 118, views: 5678, tags: ['团队建设', '企业文化', '员工活动'] }
      ]
    }

    // 根据标签匹配返回对应的模拟数据
    const firstTag = tags[0]
    if (firstTag.includes('招聘') || firstTag.includes('面试') || firstTag.includes('JD')) {
      return mockData['招聘面试']
    } else if (firstTag.includes('培训') || firstTag.includes('学习') || firstTag.includes('发展')) {
      return mockData['培训管理']
    } else if (firstTag.includes('绩效') || firstTag.includes('评估') || firstTag.includes('考核')) {
      return mockData['绩效评估']
    } else if (firstTag.includes('员工') || firstTag.includes('沟通') || firstTag.includes('关怀')) {
      return mockData['员工沟通']
    }
    return mockData['招聘面试'] || []
  }
}

export const metadata: Metadata = {
  title: '人力资源专区 - 招聘面试、培训管理、员工沟通AI提示词库',
  description: '专为HR打造的AI提示词库，包含JD职位描述生成、面试问题设计、培训方案制定、绩效评估模板等专业工具。',
  keywords: [
    // 人力资源核心关键词
    '人力资源AI提示词', '招聘面试AI助手', '培训管理工具',
    '绩效评估Prompt', 'HR AI工具', '员工管理生成器',
    
    // 招聘面试
    'JD职位描述生成Prompt', '面试问题设计AI',
    '候选人评估模板', '招聘需求分析',
    '面试技巧指导', '人才筛选助手',
    
    // 培训管理
    '培训方案制定AI', '员工发展计划',
    '培训效果评估', '学习路径规划',
    '技能提升方案', '职业规划助手',
    
    // 绩效评估
    '绩效评估模板AI', '目标设定指导',
    'SMART目标制定', '绩效反馈技巧',
    '考核标准设计', '员工辅导方案',
    
    // 员工沟通
    '员工关怀方案AI', '离职面谈指南',
    '团队建设活动', '企业文化建设',
    '员工满意度调研', '沟通技巧培训'
  ],
  openGraph: {
    title: '人力资源专区 - 招聘面试、培训管理、员工沟通AI提示词库',
    description: '专为HR打造的AI提示词库，包含JD职位描述生成、面试问题设计、培训方案制定等专业工具。',
    type: 'article',
    url: 'https://www.prompthub.xin/for/hr',
    siteName: 'PromptHub',
    locale: 'zh_CN',
  },
  twitter: {
    card: 'summary_large_image',
    title: '人力资源专区 - 招聘面试、培训管理、员工沟通AI提示词库',
    description: '专为HR打造的AI提示词库，包含JD职位描述生成、面试问题设计、培训方案制定等专业工具。',
  },
  alternates: {
    canonical: 'https://www.prompthub.xin/for/hr',
  },
}

// 定义分类配置（用于获取对应标签的提示词）
const categories = [
  {
    title: '招聘面试',
    description: 'JD撰写、面试问题、候选人评估工具',
    icon: UserCheck,
    color: 'from-teal-500 to-cyan-500',
    tags: ['招聘面试', '职位描述', '面试问题', '候选人评估', 'JD撰写', '人才筛选']
  },
  {
    title: '培训管理',
    description: '培训方案、效果评估、学习路径工具',
    icon: BookOpen,
    color: 'from-blue-500 to-indigo-500',
    tags: ['培训管理', '培训方案', '员工发展', '学习路径', '技能提升', '培训评估']
  },
  {
    title: '绩效评估',
    description: '绩效模板、目标设定、反馈沟通工具',
    icon: BarChart3,
    color: 'from-purple-500 to-violet-500',
    tags: ['绩效评估', '目标设定', '绩效反馈', '考核标准', '员工辅导', 'SMART原则']
  },
  {
    title: '员工沟通',
    description: '员工关怀、离职面谈、团队建设工具',
    icon: MessageCircle,
    color: 'from-green-500 to-emerald-500',
    tags: ['员工沟通', '员工关怀', '离职面谈', '团队建设', '企业文化', '员工活动']
  }
]

export default async function HRPage() {
  // 获取真实数据
  const { featuredPrompts } = await getHRPrompts()

  // 为每个分类获取对应的提示词
  const categoriesWithPrompts = await Promise.all(
    categories.map(async (category) => ({
      ...category,
      prompts: await getPromptsByTags(category.tags, 3)
    }))
  )

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面头部 */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-teal-500 to-cyan-500 text-white mb-4">
          <Users className="h-8 w-8" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          人力资源专区
        </h1>
        <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
          专为HR打造的AI提示词库，涵盖招聘面试、培训管理、绩效评估、员工沟通等各个环节，
          让AI成为你的人力资源管理利器。
        </p>

        {/* 统计数据 */}
        <div className="flex items-center justify-center gap-8 mt-6 text-sm text-gray-600">
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-1" />
            1.2万+ HR在使用
          </div>
          <div className="flex items-center">
            <Star className="h-4 w-4 mr-1 text-yellow-500" />
            250+ 专业提示词
          </div>
          <div className="flex items-center">
            <TrendingUp className="h-4 w-4 mr-1 text-green-500" />
            每日新增使用
          </div>
        </div>
      </div>

      {/* 精选提示词 */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Star className="h-6 w-6 text-yellow-500 mr-2" />
            精选推荐
          </h2>
          <Link
            href="/prompts?search=人力资源"
            className="text-purple-600 hover:text-purple-700 transition-colors text-sm font-medium"
          >
            查看全部 →
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {featuredPrompts.length > 0 ? featuredPrompts.map((prompt, index) => (
            <Card key={prompt.id || index} className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm h-[280px] flex flex-col">
              <CardHeader className="pb-4 flex-shrink-0">
                <div className="flex items-start justify-between mb-2">
                  <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                    精选
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {prompt.categoryDisplayName || '人力资源'}
                  </Badge>
                </div>
                <CardTitle className="text-lg group-hover:text-purple-600 transition-colors line-clamp-1">
                  {prompt.title}
                </CardTitle>
                <CardDescription className="text-sm text-gray-600 leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                  {prompt.description || '专业的HR AI提示词，助力您的人力资源工作'}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0 flex-grow flex flex-col justify-end">
                <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <Copy className="h-3 w-3 mr-1" />
                      {prompt.downloads || 0}
                    </span>
                    <span className="flex items-center">
                      <Star className="h-3 w-3 mr-1" />
                      {prompt.likes || 0}
                    </span>
                    <span className="flex items-center">
                      <Eye className="h-3 w-3 mr-1" />
                      {prompt.views || 0}
                    </span>
                  </div>
                </div>

                <Link href={`/prompts/${prompt.id}`}>
                  <Button className="w-full bg-gradient-to-r from-teal-500 to-cyan-500 hover:from-teal-600 hover:to-cyan-600 text-white border-0">
                    立即使用
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )) : (
            // 空状态显示
            Array.from({ length: 3 }).map((_, index) => (
              <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm h-[280px] flex flex-col">
                <CardHeader className="pb-4 flex-shrink-0">
                  <div className="flex items-start justify-between mb-2">
                    <Badge className="bg-gray-100 text-gray-500">
                      暂无数据
                    </Badge>
                  </div>
                  <CardTitle className="text-lg text-gray-400 line-clamp-2">
                    正在加载精选提示词...
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-400 line-clamp-3 h-[60px] overflow-hidden">
                    请稍候，我们正在为您准备最优质的HR提示词
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0 flex-grow flex flex-col justify-end">
                  <div className="flex items-center justify-between text-xs text-gray-400 mb-4">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <Copy className="h-3 w-3 mr-1" />
                        --
                      </span>
                      <span className="flex items-center">
                        <Star className="h-3 w-3 mr-1" />
                        --
                      </span>
                      <span className="flex items-center">
                        <Eye className="h-3 w-3 mr-1" />
                        --
                      </span>
                    </div>
                  </div>

                  <Button disabled className="w-full bg-gray-200 text-gray-400 border-0">
                    暂无数据
                  </Button>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* 分类提示词 */}
      <div className="space-y-8">
        <h2 className="text-2xl font-bold text-gray-900">
          分类提示词库
        </h2>

        {categoriesWithPrompts.map((category, categoryIndex) => {
          const Icon = category.icon
          return (
            <div key={categoryIndex} className="space-y-4">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color} text-white mr-3`}>
                  <Icon className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{category.title}</h3>
                  <p className="text-sm text-gray-600">{category.description}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {category.prompts.length > 0 ? category.prompts.map((prompt, promptIndex) => (
                  <Card key={prompt.id || promptIndex} className="hover:shadow-md transition-shadow border-0 bg-white/60 backdrop-blur-sm h-[220px] flex flex-col">
                    <CardHeader className="pb-3 flex-shrink-0">
                      <CardTitle className="text-base line-clamp-2">{prompt.title}</CardTitle>
                      <CardDescription className="text-sm leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                        {prompt.description || '专业的AI提示词，提升您的工作效率'}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-3 flex-grow flex flex-col justify-end">
                      <div className="flex flex-wrap gap-1">
                        {(prompt.tags || []).slice(0, 2).map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>

                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center space-x-3">
                          <span className="flex items-center">
                            <Copy className="h-3 w-3 mr-1" />
                            {prompt.downloads || 0}
                          </span>
                          <span className="flex items-center">
                            <Star className="h-3 w-3 mr-1" />
                            {prompt.likes || 0}
                          </span>
                        </div>
                        <Link href={`/prompts/${prompt.id}`}>
                          <Button size="sm" variant="outline" className="h-7 text-xs">
                            使用
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                )) : (
                  // 空状态显示
                  Array.from({ length: 3 }).map((_, emptyIndex) => (
                    <Card key={emptyIndex} className="border-0 bg-white/40 backdrop-blur-sm h-[220px] flex flex-col">
                      <CardHeader className="pb-3 flex-shrink-0">
                        <CardTitle className="text-base text-gray-400 line-clamp-2">
                          正在加载{category.title}提示词...
                        </CardTitle>
                        <CardDescription className="text-sm text-gray-400 line-clamp-3 h-[58px] overflow-hidden">
                          请稍候，我们正在为您准备专业的{category.title}工具
                        </CardDescription>
                      </CardHeader>

                      <CardContent className="space-y-3 flex-grow flex flex-col justify-end">
                        <div className="flex flex-wrap gap-1">
                          <Badge variant="outline" className="text-xs text-gray-400">
                            {category.title}
                          </Badge>
                        </div>

                        <div className="flex items-center justify-between text-xs text-gray-400">
                          <div className="flex items-center space-x-3">
                            <span className="flex items-center">
                              <Copy className="h-3 w-3 mr-1" />
                              --
                            </span>
                            <span className="flex items-center">
                              <Star className="h-3 w-3 mr-1" />
                              --
                            </span>
                          </div>
                          <Button size="sm" variant="outline" className="h-7 text-xs" disabled>
                            暂无
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* 底部CTA */}
      <div className="text-center py-12">
        <div className="bg-gradient-to-r from-teal-600 to-cyan-600 rounded-2xl p-8 text-white">
          <h2 className="text-2xl font-bold mb-4">
            开始你的AI人力资源之旅
          </h2>
          <p className="text-teal-100 mb-6 max-w-2xl mx-auto">
            安装PromptHub浏览器插件，随时随地使用专业的HR AI提示词，
            让你的人力资源工作更高效、更专业。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/extension"
              className="inline-flex items-center px-6 py-3 bg-white text-teal-600 rounded-lg font-medium hover:bg-gray-50 transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              安装浏览器插件
            </Link>
            <Link
              href="/prompts?search=人力资源"
              className="inline-flex items-center px-6 py-3 bg-teal-500 text-white rounded-lg font-medium hover:bg-teal-400 transition-colors"
            >
              浏览更多提示词
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </div>
        </div>
      </div>

      {/* 导航 */}
      <div className="flex justify-between items-center pt-8 border-t">
        <Link
          href="/for"
          className="inline-flex items-center text-purple-600 hover:text-purple-700 transition-colors"
        >
          ← 返回职业专区
        </Link>
        <Link
          href="/for/marketers"
          className="inline-flex items-center text-purple-600 hover:text-purple-700 transition-colors"
        >
          下一个：营销人员专区
          <ArrowRight className="h-4 w-4 ml-1" />
        </Link>
      </div>
    </div>
  )
}

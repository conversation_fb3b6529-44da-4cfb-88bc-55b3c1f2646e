import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  PenTool,
  TrendingUp,
  Video,
  Share2,
  Search,
  Users,
  ArrowRight,
  Star,
  Copy,
  Download,
  Eye
} from 'lucide-react'

// 获取内容创作者相关提示词数据
async function getCreatorPrompts() {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:4000/api'

    // 获取精选推荐（搜索内容创作者相关提示词，按复制量排序）
    const featuredResponse = await fetch(`${API_BASE_URL}/prompts/public?search=内容创作者&sortBy=downloads&limit=3`, {
      next: { revalidate: 300 }, // 5分钟缓存
      signal: AbortSignal.timeout(5000)
    })

    let featuredPrompts = []
    if (featuredResponse.ok) {
      const featuredResult = await featuredResponse.json()
      if (featuredResult.success && featuredResult.data) {
        featuredPrompts = featuredResult.data
      }
    }

    return { featuredPrompts }
  } catch (error) {
    console.error('获取内容创作者提示词数据失败:', error)
    // 返回模拟数据作为后备
    return {
      featuredPrompts: [
        {
          id: 'creator-demo-1',
          title: '小红书爆款文案模板',
          description: '生成符合小红书调性的爆款种草文案',
          categoryDisplayName: '社媒运营',
          downloads: 4567,
          likes: 312,
          views: 15678
        },
        {
          id: 'creator-demo-2',
          title: '抖音短视频脚本生成',
          description: '创作吸引人的短视频脚本和分镜头',
          categoryDisplayName: '视频创作',
          downloads: 3987,
          likes: 267,
          views: 13456
        },
        {
          id: 'creator-demo-3',
          title: '公众号文章写作助手',
          description: '创建结构化的公众号文章和标题',
          categoryDisplayName: '文案写作',
          downloads: 3654,
          likes: 234,
          views: 12345
        }
      ]
    }
  }
}

// 根据标签获取分类提示词
async function getPromptsByTags(tags: string[], limit = 3) {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:4000/api'

    // 使用标签筛选功能来查找包含特定标签的提示词
    const response = await fetch(`${API_BASE_URL}/prompts/public?tags=${encodeURIComponent(tags.join(','))}&sortBy=downloads&limit=${limit}`, {
      next: { revalidate: 300 },
      signal: AbortSignal.timeout(5000)
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        return result.data
      }
    }

    return []
  } catch (error) {
    console.error('获取标签提示词失败:', error)
    // 返回模拟数据作为后备
    const mockData = {
      '文案写作': [
        { id: 'copy-1', title: '爆款标题生成器', description: '创作吸引眼球的文章标题和封面文案', downloads: 3456, likes: 234, views: 12890, tags: ['标题优化', '文案创作', '内容营销'] },
        { id: 'copy-2', title: '产品文案优化', description: '优化产品介绍文案，提升转化效果', downloads: 2987, likes: 198, views: 10567, tags: ['产品文案', '转化优化', '营销文案'] },
        { id: 'copy-3', title: '情感文案创作', description: '创作触动人心的情感化文案内容', downloads: 2654, likes: 176, views: 9234, tags: ['情感文案', '品牌故事', '内容创作'] }
      ],
      '视频脚本': [
        { id: 'video-1', title: '短视频脚本模板', description: '创作15-60秒短视频的完整脚本', downloads: 4123, likes: 278, views: 14567, tags: ['短视频', '脚本创作', '内容策划'] },
        { id: 'video-2', title: '直播话术生成', description: '生成专业的直播带货和互动话术', downloads: 3567, likes: 234, views: 12456, tags: ['直播话术', '互动文案', '带货脚本'] },
        { id: 'video-3', title: '教学视频大纲', description: '制作结构化的教学视频内容大纲', downloads: 2890, likes: 189, views: 9876, tags: ['教学视频', '内容大纲', '知识分享'] }
      ],
      '社媒运营': [
        { id: 'social-1', title: '社媒内容日历', description: '制定完整的社交媒体内容发布计划', downloads: 3789, likes: 245, views: 13456, tags: ['内容规划', '社媒运营', '发布策略'] },
        { id: 'social-2', title: '互动话题策划', description: '设计引发用户参与的话题和活动', downloads: 3234, likes: 212, views: 11234, tags: ['话题策划', '用户互动', '社区运营'] },
        { id: 'social-3', title: '品牌人设打造', description: '塑造独特的品牌人设和内容风格', downloads: 2876, likes: 187, views: 9876, tags: ['品牌人设', '内容风格', '个人IP'] }
      ],
      'SEO优化': [
        { id: 'seo-1', title: 'SEO文章优化', description: '优化文章结构和关键词布局', downloads: 3456, likes: 223, views: 12345, tags: ['SEO优化', '关键词布局', '搜索排名'] },
        { id: 'seo-2', title: '长尾关键词挖掘', description: '发现高价值的长尾关键词机会', downloads: 2789, likes: 178, views: 9789, tags: ['关键词研究', '长尾词', 'SEO策略'] },
        { id: 'seo-3', title: '内容聚合策略', description: '制定内容聚合和内链优化策略', downloads: 2456, likes: 156, views: 8456, tags: ['内容聚合', '内链优化', 'SEO策略'] }
      ]
    }

    // 根据标签匹配返回对应的模拟数据
    const firstTag = tags[0]
    if (firstTag.includes('文案') || firstTag.includes('写作')) {
      return mockData['文案写作']
    } else if (firstTag.includes('视频') || firstTag.includes('脚本')) {
      return mockData['视频脚本']
    } else if (firstTag.includes('社媒') || firstTag.includes('运营') || firstTag.includes('社交')) {
      return mockData['社媒运营']
    } else if (firstTag.includes('SEO') || firstTag.includes('搜索') || firstTag.includes('优化')) {
      return mockData['SEO优化']
    }
    return mockData['文案写作'] || []
  }
}

export const metadata: Metadata = {
  title: '内容创作者专区 - 文案写作、视频脚本、社媒运营AI提示词库',
  description: '专为内容创作者打造的AI提示词库，包含爆款标题生成、视频脚本创作、社媒内容规划、SEO文章优化等专业工具。',
  keywords: [
    // 内容创作者核心关键词
    '内容创作者AI提示词', '文案写作AI助手', '视频脚本生成工具',
    '社媒运营Prompt', '内容创作AI工具', '创意文案生成器',
    
    // 文案写作
    '爆款标题生成Prompt', '文案创作AI',
    '产品文案优化', '情感文案创作AI',
    '营销文案模板', '品牌故事生成器',
    
    // 视频创作
    '短视频脚本生成AI', '直播话术模板',
    '教学视频大纲', '视频内容策划助手',
    '抖音脚本生成器', '视频创意Prompt',
    
    // 社媒运营
    '社媒内容规划AI', '小红书文案生成',
    '微博话题策划', '朋友圈文案助手',
    '社区运营Prompt', '用户互动策略',
    
    // SEO优化
    'SEO文章优化AI', '关键词布局助手',
    '长尾关键词挖掘', '内容聚合策略',
    '搜索排名优化', 'SEO内容生成器'
  ],
  openGraph: {
    title: '内容创作者专区 - 文案写作、视频脚本、社媒运营AI提示词库',
    description: '专为内容创作者打造的AI提示词库，包含爆款标题生成、视频脚本创作、社媒内容规划等专业工具。',
    type: 'article',
    url: 'https://www.prompthub.xin/for/creators',
    siteName: 'PromptHub',
    locale: 'zh_CN',
  },
  twitter: {
    card: 'summary_large_image',
    title: '内容创作者专区 - 文案写作、视频脚本、社媒运营AI提示词库',
    description: '专为内容创作者打造的AI提示词库，包含爆款标题生成、视频脚本创作、社媒内容规划等专业工具。',
  },
  alternates: {
    canonical: 'https://www.prompthub.xin/for/creators',
  },
}

// 定义分类配置（用于获取对应标签的提示词）
const categories = [
  {
    title: '文案写作',
    description: '爆款标题、产品文案、情感文案创作工具',
    icon: PenTool,
    color: 'from-purple-500 to-violet-500',
    tags: ['文案写作', '标题优化', '产品文案', '情感文案', '营销文案', '品牌故事']
  },
  {
    title: '视频脚本',
    description: '短视频、直播、教学视频脚本生成工具',
    icon: Video,
    color: 'from-red-500 to-pink-500',
    tags: ['视频脚本', '短视频', '直播话术', '教学视频', '脚本创作', '内容策划']
  },
  {
    title: '社媒运营',
    description: '社交媒体内容规划、话题策划、品牌人设工具',
    icon: Share2,
    color: 'from-blue-500 to-cyan-500',
    tags: ['社媒运营', '内容规划', '话题策划', '用户互动', '社区运营', '品牌人设']
  },
  {
    title: 'SEO优化',
    description: 'SEO文章、关键词优化、内容聚合工具',
    icon: Search,
    color: 'from-green-500 to-emerald-500',
    tags: ['SEO优化', '关键词布局', '长尾词', '内容聚合', '搜索排名', 'SEO策略']
  }
]

export default async function CreatorsPage() {
  // 获取真实数据
  const { featuredPrompts } = await getCreatorPrompts()

  // 为每个分类获取对应的提示词
  const categoriesWithPrompts = await Promise.all(
    categories.map(async (category) => ({
      ...category,
      prompts: await getPromptsByTags(category.tags, 3)
    }))
  )

  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "内容创作者专区",
    "description": "专为内容创作者打造的AI提示词库，包含爆款标题生成、视频脚本创作、社媒内容规划、SEO文章优化等专业工具。",
    "url": "https://www.prompthub.xin/for/creators",
    "about": {
      "@type": "Occupation",
      "name": "内容创作者",
      "alternateName": ["文案策划", "新媒体运营", "内容运营"],
      "description": "从事内容创作、文案写作、社媒运营和内容营销的专业人员"
    },
    "mainEntity": {
      "@type": "ItemList",
      "name": "内容创作者AI工具分类",
      "itemListElement": categories.map((category, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "name": category.title,
        "description": category.description
      }))
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://www.prompthub.xin"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "职业专区",
          "item": "https://www.prompthub.xin/for"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "内容创作者专区",
          "item": "https://www.prompthub.xin/for/creators"
        }
      ]
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面头部 */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-violet-500 text-white mb-4">
          <PenTool className="h-8 w-8" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          内容创作者专区
        </h1>
        <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
          专为内容创作者打造的AI提示词库，涵盖文案写作、视频脚本、社媒运营、SEO优化等各个环节，
          让AI成为你的创作利器。
        </p>

        {/* 统计数据 */}
        <div className="flex items-center justify-center gap-8 mt-6 text-sm text-gray-600">
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-1" />
            3.1万+ 创作者在使用
          </div>
          <div className="flex items-center">
            <Star className="h-4 w-4 mr-1 text-yellow-500" />
            600+ 专业提示词
          </div>
          <div className="flex items-center">
            <TrendingUp className="h-4 w-4 mr-1 text-green-500" />
            每日新增使用
          </div>
        </div>
      </div>

      {/* 精选提示词 */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Star className="h-6 w-6 text-yellow-500 mr-2" />
            精选推荐
          </h2>
          <Link
            href="/prompts?search=内容创作者"
            className="text-purple-600 hover:text-purple-700 transition-colors text-sm font-medium"
          >
            查看全部 →
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {featuredPrompts.length > 0 ? featuredPrompts.map((prompt, index) => (
            <Card key={prompt.id || index} className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm h-[280px] flex flex-col">
              <CardHeader className="pb-4 flex-shrink-0">
                <div className="flex items-start justify-between mb-2">
                  <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                    精选
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {prompt.categoryDisplayName || '内容创作'}
                  </Badge>
                </div>
                <CardTitle className="text-lg group-hover:text-purple-600 transition-colors line-clamp-1">
                  {prompt.title}
                </CardTitle>
                <CardDescription className="text-sm text-gray-600 leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                  {prompt.description || '专业的创作AI提示词，助力您的内容创作'}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0 flex-grow flex flex-col justify-end">
                <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <Copy className="h-3 w-3 mr-1" />
                      {prompt.downloads || 0}
                    </span>
                    <span className="flex items-center">
                      <Star className="h-3 w-3 mr-1" />
                      {prompt.likes || 0}
                    </span>
                    <span className="flex items-center">
                      <Eye className="h-3 w-3 mr-1" />
                      {prompt.views || 0}
                    </span>
                  </div>
                </div>

                <Link href={`/prompts/${prompt.id}`}>
                  <Button className="w-full bg-gradient-to-r from-purple-500 to-violet-500 hover:from-purple-600 hover:to-violet-600 text-white border-0">
                    立即使用
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )) : (
            // 空状态显示
            Array.from({ length: 3 }).map((_, index) => (
              <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm h-[280px] flex flex-col">
                <CardHeader className="pb-4 flex-shrink-0">
                  <div className="flex items-start justify-between mb-2">
                    <Badge className="bg-gray-100 text-gray-500">
                      暂无数据
                    </Badge>
                  </div>
                  <CardTitle className="text-lg text-gray-400 line-clamp-2">
                    正在加载精选提示词...
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-400 line-clamp-3 h-[60px] overflow-hidden">
                    请稍候，我们正在为您准备最优质的创作提示词
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0 flex-grow flex flex-col justify-end">
                  <div className="flex items-center justify-between text-xs text-gray-400 mb-4">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <Copy className="h-3 w-3 mr-1" />
                        --
                      </span>
                      <span className="flex items-center">
                        <Star className="h-3 w-3 mr-1" />
                        --
                      </span>
                      <span className="flex items-center">
                        <Eye className="h-3 w-3 mr-1" />
                        --
                      </span>
                    </div>
                  </div>

                  <Button disabled className="w-full bg-gray-200 text-gray-400 border-0">
                    暂无数据
                  </Button>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* 分类提示词 */}
      <div className="space-y-8">
        <h2 className="text-2xl font-bold text-gray-900">
          分类提示词库
        </h2>

        {categoriesWithPrompts.map((category, categoryIndex) => {
          const Icon = category.icon
          return (
            <div key={categoryIndex} className="space-y-4">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color} text-white mr-3`}>
                  <Icon className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{category.title}</h3>
                  <p className="text-sm text-gray-600">{category.description}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {category.prompts.length > 0 ? category.prompts.map((prompt, promptIndex) => (
                  <Card key={prompt.id || promptIndex} className="hover:shadow-md transition-shadow border-0 bg-white/60 backdrop-blur-sm h-[220px] flex flex-col">
                    <CardHeader className="pb-3 flex-shrink-0">
                      <CardTitle className="text-base line-clamp-2">{prompt.title}</CardTitle>
                      <CardDescription className="text-sm leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                        {prompt.description || '专业的AI提示词，提升您的工作效率'}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-3 flex-grow flex flex-col justify-end">
                      <div className="flex flex-wrap gap-1">
                        {(prompt.tags || []).slice(0, 2).map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>

                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center space-x-3">
                          <span className="flex items-center">
                            <Copy className="h-3 w-3 mr-1" />
                            {prompt.downloads || 0}
                          </span>
                          <span className="flex items-center">
                            <Star className="h-3 w-3 mr-1" />
                            {prompt.likes || 0}
                          </span>
                        </div>
                        <Link href={`/prompts/${prompt.id}`}>
                          <Button size="sm" variant="outline" className="h-7 text-xs">
                            使用
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                )) : (
                  // 空状态显示
                  Array.from({ length: 3 }).map((_, emptyIndex) => (
                    <Card key={emptyIndex} className="border-0 bg-white/40 backdrop-blur-sm h-[220px] flex flex-col">
                      <CardHeader className="pb-3 flex-shrink-0">
                        <CardTitle className="text-base text-gray-400 line-clamp-2">
                          正在加载{category.title}提示词...
                        </CardTitle>
                        <CardDescription className="text-sm text-gray-400 line-clamp-3 h-[58px] overflow-hidden">
                          请稍候，我们正在为您准备专业的{category.title}工具
                        </CardDescription>
                      </CardHeader>

                      <CardContent className="space-y-3 flex-grow flex flex-col justify-end">
                        <div className="flex flex-wrap gap-1">
                          <Badge variant="outline" className="text-xs text-gray-400">
                            {category.title}
                          </Badge>
                        </div>

                        <div className="flex items-center justify-between text-xs text-gray-400">
                          <div className="flex items-center space-x-3">
                            <span className="flex items-center">
                              <Copy className="h-3 w-3 mr-1" />
                              --
                            </span>
                            <span className="flex items-center">
                              <Star className="h-3 w-3 mr-1" />
                              --
                            </span>
                          </div>
                          <Button size="sm" variant="outline" className="h-7 text-xs" disabled>
                            暂无
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* 底部CTA */}
      <div className="text-center py-12">
        <div className="bg-gradient-to-r from-purple-600 to-violet-600 rounded-2xl p-8 text-white">
          <h2 className="text-2xl font-bold mb-4">
            开始你的AI创作之旅
          </h2>
          <p className="text-purple-100 mb-6 max-w-2xl mx-auto">
            安装PromptHub浏览器插件，随时随地使用专业的创作AI提示词，
            让你的内容创作更高效、更有创意。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/extension"
              className="inline-flex items-center px-6 py-3 bg-white text-purple-600 rounded-lg font-medium hover:bg-gray-50 transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              安装浏览器插件
            </Link>
            <Link
              href="/prompts?search=内容创作者"
              className="inline-flex items-center px-6 py-3 bg-purple-500 text-white rounded-lg font-medium hover:bg-purple-400 transition-colors"
            >
              浏览更多提示词
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </div>
        </div>
      </div>

      {/* 导航 */}
      <div className="flex justify-between items-center pt-8 border-t">
        <Link
          href="/for"
          className="inline-flex items-center text-purple-600 hover:text-purple-700 transition-colors"
        >
          ← 返回职业专区
        </Link>
        <Link
          href="/for/students"
          className="inline-flex items-center text-purple-600 hover:text-purple-700 transition-colors"
        >
          下一个：学生专区
          <ArrowRight className="h-4 w-4 ml-1" />
        </Link>
      </div>
    </div>
    </>
  )
}

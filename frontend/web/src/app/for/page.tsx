import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Megaphone, 
  Code, 
  PenTool, 
  GraduationCap, 
  Palette,
  Users,
  ArrowRight,
  Star,
  TrendingUp,
  Zap
} from 'lucide-react'

const professions = [
  {
    id: 'marketers',
    title: '营销人员',
    description: '电商运营、广告投放、内容营销专用AI提示词',
    icon: Megaphone,
    color: 'from-pink-500 to-rose-500',
    bgColor: 'from-pink-50 to-rose-50',
    userCount: '2.3万+',
    promptCount: '500+',
    tags: ['电商文案', '广告创意', '数据分析', '用户调研'],
    highlights: [
      '电商产品描述生成',
      '广告文案A/B测试',
      '用户画像分析',
      '营销策略规划'
    ],
    popular: [
      '小红书种草文案生成器',
      '电商详情页文案优化',
      '节日营销活动策划'
    ]
  },
  {
    id: 'developers',
    title: '程序员',
    description: '代码审查、技术文档、问题调试专用AI助手',
    icon: Code,
    color: 'from-blue-500 to-indigo-500',
    bgColor: 'from-blue-50 to-indigo-50',
    userCount: '1.8万+',
    promptCount: '400+',
    tags: ['代码审查', '文档生成', '调试助手', '架构设计'],
    highlights: [
      '代码质量检查',
      'API文档自动生成',
      '技术方案评估',
      '性能优化建议'
    ],
    popular: [
      '代码注释生成器',
      'SQL查询优化助手',
      '技术文档写作模板'
    ]
  },
  {
    id: 'creators',
    title: '内容创作者',
    description: '文案写作、视频脚本、社媒运营专用创作工具',
    icon: PenTool,
    color: 'from-purple-500 to-violet-500',
    bgColor: 'from-purple-50 to-violet-50',
    userCount: '3.1万+',
    promptCount: '600+',
    tags: ['文案创作', '视频脚本', '社媒运营', 'SEO优化'],
    highlights: [
      '爆款标题生成',
      '视频脚本创作',
      '社媒内容规划',
      'SEO文章优化'
    ],
    popular: [
      '小红书爆款文案模板',
      '抖音短视频脚本生成',
      '公众号文章写作助手'
    ]
  },
  {
    id: 'students',
    title: '学生',
    description: '学习辅导、论文写作、考试备考专用学习助手',
    icon: GraduationCap,
    color: 'from-green-500 to-emerald-500',
    bgColor: 'from-green-50 to-emerald-50',
    userCount: '4.2万+',
    promptCount: '450+',
    tags: ['论文写作', '学习笔记', '考试备考', '文献翻译'],
    highlights: [
      '论文大纲生成',
      '学习计划制定',
      '知识点总结',
      '外文文献翻译'
    ],
    popular: [
      '学术论文润色助手',
      '考试重点提取器',
      '学习笔记整理模板'
    ]
  },
  {
    id: 'designers',
    title: '设计师',
    description: '创意灵感、设计说明、客户沟通专用设计助手',
    icon: Palette,
    color: 'from-orange-500 to-amber-500',
    bgColor: 'from-orange-50 to-amber-50',
    userCount: '1.5万+',
    promptCount: '300+',
    tags: ['创意灵感', '设计说明', '客户沟通', '品牌策划'],
    highlights: [
      '设计理念阐述',
      '创意方案生成',
      '客户提案撰写',
      '品牌故事创作'
    ],
    popular: [
      'UI设计说明生成器',
      '品牌视觉方案助手',
      '设计评审意见模板'
    ]
  },
  {
    id: 'hr',
    title: '人力资源',
    description: '招聘面试、培训管理、员工沟通专用HR工具',
    icon: Users,
    color: 'from-teal-500 to-cyan-500',
    bgColor: 'from-teal-50 to-cyan-50',
    userCount: '1.2万+',
    promptCount: '250+',
    tags: ['招聘面试', '培训管理', '绩效评估', '员工沟通'],
    highlights: [
      'JD职位描述生成',
      '面试问题设计',
      '培训方案制定',
      '绩效评估模板'
    ],
    popular: [
      '招聘JD生成器',
      '面试评估表模板',
      '员工培训计划助手'
    ]
  }
]

export default function ForPage() {
  return (
    <div className="space-y-8">
      {/* 页面头部 */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          职业专区
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          为不同职业用户量身定制的AI提示词专区，
          让AI成为你专业领域的得力助手。
        </p>
      </div>

      {/* 职业专区网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {professions.map((profession) => {
          const Icon = profession.icon
          return (
            <Card key={profession.id} className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm overflow-hidden">
              {/* 渐变背景头部 */}
              <div className={`h-20 bg-gradient-to-r ${profession.color} relative`}>
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="absolute bottom-4 left-6">
                  <div className="p-2 rounded-lg bg-white/20 backdrop-blur-sm">
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </div>
              
              <CardHeader className="pb-4 -mt-2">
                <div className="flex items-center justify-between mb-2">
                  <CardTitle className="text-xl group-hover:text-purple-600 transition-colors">
                    {profession.title}
                  </CardTitle>
                  <Badge variant="secondary" className="bg-purple-100 text-purple-700 border-0">
                    热门
                  </Badge>
                </div>
                <CardDescription className="text-sm text-gray-600 leading-relaxed">
                  {profession.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* 统计数据 */}
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center text-gray-600">
                    <Users className="h-4 w-4 mr-1" />
                    {profession.userCount} 用户
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Zap className="h-4 w-4 mr-1" />
                    {profession.promptCount} 提示词
                  </div>
                </div>
                
                {/* 核心功能 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">核心功能</h4>
                  <div className="grid grid-cols-2 gap-1 text-xs">
                    {profession.highlights.map((highlight, index) => (
                      <div key={index} className="flex items-center text-gray-600">
                        <Star className="h-3 w-3 text-yellow-500 mr-1 flex-shrink-0" />
                        {highlight}
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* 热门提示词 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                    <TrendingUp className="h-4 w-4 mr-1 text-red-500" />
                    热门提示词
                  </h4>
                  <ul className="space-y-1">
                    {profession.popular.slice(0, 2).map((item, index) => (
                      <li key={index} className="text-xs text-gray-600 truncate">
                        • {item}
                      </li>
                    ))}
                  </ul>
                </div>
                
                {/* 标签 */}
                <div className="flex flex-wrap gap-1">
                  {profession.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {profession.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{profession.tags.length - 3}
                    </Badge>
                  )}
                </div>
                
                {/* 进入按钮 */}
                <Link 
                  href={`/for/${profession.id}`}
                  className="block w-full"
                >
                  <div className={`w-full p-3 rounded-lg bg-gradient-to-r ${profession.color} text-white text-center font-medium hover:shadow-lg transition-all duration-300 group-hover:scale-[1.02]`}>
                    <span className="flex items-center justify-center">
                      进入专区
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </span>
                  </div>
                </Link>
              </CardContent>
            </Card>
          )
        })}
      </div>
      
      {/* 底部说明 */}
      <div className="text-center py-12">
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-8 text-white">
          <h2 className="text-2xl font-bold mb-4">
            找不到你的职业？
          </h2>
          <p className="text-indigo-100 mb-6 max-w-2xl mx-auto">
            我们正在不断扩展职业专区，如果暂时没有找到适合你的专区，
            可以浏览通用提示词库或联系我们添加新的职业分类。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/prompts"
              className="inline-flex items-center px-6 py-3 bg-white text-indigo-600 rounded-lg font-medium hover:bg-gray-50 transition-colors"
            >
              浏览全部提示词
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
            <Link 
              href="/about"
              className="inline-flex items-center px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-400 transition-colors"
            >
              联系我们
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

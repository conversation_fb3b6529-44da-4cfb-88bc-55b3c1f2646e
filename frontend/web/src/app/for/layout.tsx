import { Metadata } from 'next'
import { headers } from 'next/headers'

export const metadata: Metadata = {
  title: {
    default: '职业专区 - PromptHub',
    template: '%s | PromptHub 职业专区'
  },
  description: '为不同职业用户量身定制的AI提示词专区，包含营销人员、程序员、内容创作者、学生等专业提示词库。',
  keywords: [
    // 职业角色关键词
    '营销人员AI提示词', '程序员AI助手', '内容创作者Prompt',
    '学生AI学习助手', '设计师AI工具', '教师AI教学助手',

    // 内容创作者专区
    '小红书爆款文案AI生成', '视频脚本创作AI助手',
    '公众号文章写作Prompt', '知乎高赞回答写作技巧AI',
    'SEO文章优化AI提示词', '短视频标题生成器',

    // 程序员专区
    '代码审查AI助手', '编程问题解决Prompt',
    'API文档生成AI', '代码注释生成器',
    '技术文档写作AI', '代码重构建议AI',

    // 营销人员专区
    '电商产品描述生成Prompt', '用户评论分析AI',
    '节日营销活动策划AI', '直播带货话术生成器',
    '广告文案优化AI', '市场调研分析Prompt',

    // 学生专区
    'AI辅助写论文大纲', '学术论文润色校对Prompt',
    '外文文献翻译和摘要AI', '学习笔记整理AI工具',
    'AI辅助备考和出题', '作业辅导AI助手'
  ],
  openGraph: {
    title: '职业专区 - PromptHub',
    description: '为不同职业用户量身定制的AI提示词专区，包含营销人员、程序员、内容创作者、学生等专业提示词库。',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: '职业专区 - PromptHub',
    description: '为不同职业用户量身定制的AI提示词专区，包含营销人员、程序员、内容创作者、学生等专业提示词库。',
  },
}

export default function ForLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {children}
      </div>
    </div>
  )
}

import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  GraduationCap,
  TrendingUp,
  BookOpen,
  FileText,
  Languages,
  Users,
  ArrowRight,
  Star,
  Copy,
  Download,
  Eye
} from 'lucide-react'

// 获取学生相关提示词数据
async function getStudentPrompts() {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:4000/api'

    // 获取精选推荐（搜索学生相关提示词，按复制量排序）
    const featuredResponse = await fetch(`${API_BASE_URL}/prompts/public?search=学生&sortBy=downloads&limit=3`, {
      next: { revalidate: 300 }, // 5分钟缓存
      signal: AbortSignal.timeout(5000)
    })

    let featuredPrompts = []
    if (featuredResponse.ok) {
      const featuredResult = await featuredResponse.json()
      if (featuredResult.success && featuredResult.data) {
        featuredPrompts = featuredResult.data
      }
    }

    return { featuredPrompts }
  } catch (error) {
    console.error('获取学生提示词数据失败:', error)
    // 返回模拟数据作为后备
    return {
      featuredPrompts: [
        {
          id: 'student-demo-1',
          title: '学术论文润色助手',
          description: '优化论文语言表达，提升学术写作质量',
          categoryDisplayName: '论文写作',
          downloads: 5678,
          likes: 389,
          views: 18901
        },
        {
          id: 'student-demo-2',
          title: '考试重点提取器',
          description: '从课程材料中提取考试重点和知识要点',
          categoryDisplayName: '考试备考',
          downloads: 4987,
          likes: 334,
          views: 16789
        },
        {
          id: 'student-demo-3',
          title: '学习笔记整理模板',
          description: '结构化整理课堂笔记和学习资料',
          categoryDisplayName: '学习辅导',
          downloads: 4321,
          likes: 287,
          views: 14567
        }
      ]
    }
  }
}

// 根据标签获取分类提示词
async function getPromptsByTags(tags: string[], limit = 3) {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:4000/api'

    // 使用标签筛选功能来查找包含特定标签的提示词
    const response = await fetch(`${API_BASE_URL}/prompts/public?tags=${encodeURIComponent(tags.join(','))}&sortBy=downloads&limit=${limit}`, {
      next: { revalidate: 300 },
      signal: AbortSignal.timeout(5000)
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        return result.data
      }
    }

    return []
  } catch (error) {
    console.error('获取标签提示词失败:', error)
    // 返回模拟数据作为后备
    const mockData = {
      '论文写作': [
        { id: 'paper-1', title: '论文大纲生成器', description: '根据研究主题生成详细的论文大纲结构', downloads: 4567, likes: 312, views: 15678, tags: ['论文大纲', '学术写作', '研究方法'] },
        { id: 'paper-2', title: '文献综述助手', description: '整理和分析相关文献，生成综述内容', downloads: 3987, likes: 267, views: 13456, tags: ['文献综述', '学术研究', '论文写作'] },
        { id: 'paper-3', title: '引用格式规范', description: '规范化处理各种引用格式和参考文献', downloads: 3654, likes: 234, views: 12345, tags: ['引用格式', '参考文献', '学术规范'] }
      ],
      '学习辅导': [
        { id: 'study-1', title: '学习计划制定', description: '制定个性化的学习计划和时间安排', downloads: 5123, likes: 345, views: 17890, tags: ['学习计划', '时间管理', '学习方法'] },
        { id: 'study-2', title: '知识点总结', description: '提炼和总结课程的核心知识点', downloads: 4567, likes: 298, views: 15234, tags: ['知识总结', '重点梳理', '学习笔记'] },
        { id: 'study-3', title: '难点解析助手', description: '深入解析学习中的难点和疑点', downloads: 3890, likes: 256, views: 12876, tags: ['难点解析', '学习辅导', '问题解答'] }
      ],
      '考试备考': [
        { id: 'exam-1', title: '模拟试题生成', description: '根据课程内容生成模拟考试题目', downloads: 4789, likes: 323, views: 16234, tags: ['模拟考试', '试题生成', '考试准备'] },
        { id: 'exam-2', title: '答题技巧指导', description: '提供各类题型的答题技巧和策略', downloads: 4234, likes: 287, views: 14567, tags: ['答题技巧', '考试策略', '应试方法'] },
        { id: 'exam-3', title: '复习计划安排', description: '制定科学的考前复习计划', downloads: 3876, likes: 245, views: 13456, tags: ['复习计划', '考前准备', '时间安排'] }
      ],
      '文献翻译': [
        { id: 'trans-1', title: '外文文献翻译', description: '准确翻译外文学术文献和资料', downloads: 4456, likes: 298, views: 15123, tags: ['文献翻译', '学术翻译', '外语学习'] },
        { id: 'trans-2', title: '专业术语解释', description: '解释和翻译专业领域的术语概念', downloads: 3789, likes: 234, views: 12789, tags: ['术语翻译', '专业词汇', '概念解释'] },
        { id: 'trans-3', title: '摘要翻译优化', description: '优化学术论文摘要的翻译质量', downloads: 3456, likes: 212, views: 11456, tags: ['摘要翻译', '学术英语', '翻译优化'] }
      ]
    }

    // 根据标签匹配返回对应的模拟数据
    const firstTag = tags[0]
    if (firstTag.includes('论文') || firstTag.includes('写作')) {
      return mockData['论文写作']
    } else if (firstTag.includes('学习') || firstTag.includes('辅导') || firstTag.includes('笔记')) {
      return mockData['学习辅导']
    } else if (firstTag.includes('考试') || firstTag.includes('备考') || firstTag.includes('复习')) {
      return mockData['考试备考']
    } else if (firstTag.includes('翻译') || firstTag.includes('文献') || firstTag.includes('外语')) {
      return mockData['文献翻译']
    }
    return mockData['学习辅导'] || []
  }
}

export const metadata: Metadata = {
  title: '学生专区 - 论文写作、学习辅导、考试备考AI提示词库',
  description: '专为学生打造的AI提示词库，包含论文大纲生成、学习计划制定、知识点总结、外文文献翻译等专业工具。',
  keywords: [
    // 学生核心关键词
    '学生AI提示词', '论文写作AI助手', '学习辅导工具',
    '考试备考Prompt', '学习AI工具', '学术写作生成器',
    
    // 论文写作
    '论文大纲生成Prompt', '学术写作AI',
    '文献综述助手', '引用格式规范AI',
    '论文润色工具', '学术论文模板',
    
    // 学习辅导
    '学习计划制定AI', '知识点总结助手',
    '学习笔记整理', '难点解析工具',
    '学习方法指导', '个性化学习AI',
    
    // 考试备考
    '模拟试题生成AI', '答题技巧指导',
    '复习计划安排', '考试策略助手',
    '重点提取工具', '考前准备AI',
    
    // 文献翻译
    '外文文献翻译AI', '专业术语解释',
    '学术英语翻译', '摘要翻译优化',
    '文献阅读助手', '翻译质量提升'
  ],
  openGraph: {
    title: '学生专区 - 论文写作、学习辅导、考试备考AI提示词库',
    description: '专为学生打造的AI提示词库，包含论文大纲生成、学习计划制定、知识点总结等专业工具。',
    type: 'article',
    url: 'https://www.prompthub.xin/for/students',
    siteName: 'PromptHub',
    locale: 'zh_CN',
  },
  twitter: {
    card: 'summary_large_image',
    title: '学生专区 - 论文写作、学习辅导、考试备考AI提示词库',
    description: '专为学生打造的AI提示词库，包含论文大纲生成、学习计划制定、知识点总结等专业工具。',
  },
  alternates: {
    canonical: 'https://www.prompthub.xin/for/students',
  },
}

// 定义分类配置（用于获取对应标签的提示词）
const categories = [
  {
    title: '论文写作',
    description: '论文大纲、文献综述、引用格式规范工具',
    icon: FileText,
    color: 'from-green-500 to-emerald-500',
    tags: ['论文写作', '学术写作', '论文大纲', '文献综述', '引用格式', '学术规范']
  },
  {
    title: '学习辅导',
    description: '学习计划、知识总结、难点解析工具',
    icon: BookOpen,
    color: 'from-blue-500 to-cyan-500',
    tags: ['学习辅导', '学习计划', '知识总结', '学习笔记', '难点解析', '学习方法']
  },
  {
    title: '考试备考',
    description: '模拟试题、答题技巧、复习计划工具',
    icon: GraduationCap,
    color: 'from-purple-500 to-violet-500',
    tags: ['考试备考', '模拟考试', '答题技巧', '复习计划', '考试策略', '重点提取']
  },
  {
    title: '文献翻译',
    description: '外文翻译、术语解释、摘要优化工具',
    icon: Languages,
    color: 'from-orange-500 to-red-500',
    tags: ['文献翻译', '学术翻译', '专业术语', '外语学习', '摘要翻译', '翻译优化']
  }
]

export default async function StudentsPage() {
  // 获取真实数据
  const { featuredPrompts } = await getStudentPrompts()

  // 为每个分类获取对应的提示词
  const categoriesWithPrompts = await Promise.all(
    categories.map(async (category) => ({
      ...category,
      prompts: await getPromptsByTags(category.tags, 3)
    }))
  )

  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "学生专区",
    "description": "专为学生打造的AI提示词库，包含论文大纲生成、学习计划制定、知识点总结、外文文献翻译等专业工具。",
    "url": "https://www.prompthub.xin/for/students",
    "about": {
      "@type": "Occupation",
      "name": "学生",
      "alternateName": ["大学生", "研究生", "博士生"],
      "description": "在校学习的学生群体，包括本科生、研究生等"
    },
    "mainEntity": {
      "@type": "ItemList",
      "name": "学生AI工具分类",
      "itemListElement": categories.map((category, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "name": category.title,
        "description": category.description
      }))
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://www.prompthub.xin"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "职业专区",
          "item": "https://www.prompthub.xin/for"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "学生专区",
          "item": "https://www.prompthub.xin/for/students"
        }
      ]
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面头部 */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 text-white mb-4">
          <GraduationCap className="h-8 w-8" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          学生专区
        </h1>
        <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
          专为学生打造的AI提示词库，涵盖论文写作、学习辅导、考试备考、文献翻译等各个环节，
          让AI成为你的学习利器。
        </p>

        {/* 统计数据 */}
        <div className="flex items-center justify-center gap-8 mt-6 text-sm text-gray-600">
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-1" />
            4.2万+ 学生在使用
          </div>
          <div className="flex items-center">
            <Star className="h-4 w-4 mr-1 text-yellow-500" />
            450+ 专业提示词
          </div>
          <div className="flex items-center">
            <TrendingUp className="h-4 w-4 mr-1 text-green-500" />
            每日新增使用
          </div>
        </div>
      </div>

      {/* 精选提示词 */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Star className="h-6 w-6 text-yellow-500 mr-2" />
            精选推荐
          </h2>
          <Link
            href="/prompts?search=学生"
            className="text-purple-600 hover:text-purple-700 transition-colors text-sm font-medium"
          >
            查看全部 →
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {featuredPrompts.length > 0 ? featuredPrompts.map((prompt, index) => (
            <Card key={prompt.id || index} className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm h-[280px] flex flex-col">
              <CardHeader className="pb-4 flex-shrink-0">
                <div className="flex items-start justify-between mb-2">
                  <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                    精选
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {prompt.categoryDisplayName || '学习教育'}
                  </Badge>
                </div>
                <CardTitle className="text-lg group-hover:text-purple-600 transition-colors line-clamp-1">
                  {prompt.title}
                </CardTitle>
                <CardDescription className="text-sm text-gray-600 leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                  {prompt.description || '专业的学习AI提示词，助力您的学习成长'}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0 flex-grow flex flex-col justify-end">
                <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <Copy className="h-3 w-3 mr-1" />
                      {prompt.downloads || 0}
                    </span>
                    <span className="flex items-center">
                      <Star className="h-3 w-3 mr-1" />
                      {prompt.likes || 0}
                    </span>
                    <span className="flex items-center">
                      <Eye className="h-3 w-3 mr-1" />
                      {prompt.views || 0}
                    </span>
                  </div>
                </div>

                <Link href={`/prompts/${prompt.id}`}>
                  <Button className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white border-0">
                    立即使用
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )) : (
            // 空状态显示
            Array.from({ length: 3 }).map((_, index) => (
              <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm h-[280px] flex flex-col">
                <CardHeader className="pb-4 flex-shrink-0">
                  <div className="flex items-start justify-between mb-2">
                    <Badge className="bg-gray-100 text-gray-500">
                      暂无数据
                    </Badge>
                  </div>
                  <CardTitle className="text-lg text-gray-400 line-clamp-2">
                    正在加载精选提示词...
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-400 line-clamp-3 h-[60px] overflow-hidden">
                    请稍候，我们正在为您准备最优质的学习提示词
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0 flex-grow flex flex-col justify-end">
                  <div className="flex items-center justify-between text-xs text-gray-400 mb-4">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <Copy className="h-3 w-3 mr-1" />
                        --
                      </span>
                      <span className="flex items-center">
                        <Star className="h-3 w-3 mr-1" />
                        --
                      </span>
                      <span className="flex items-center">
                        <Eye className="h-3 w-3 mr-1" />
                        --
                      </span>
                    </div>
                  </div>

                  <Button disabled className="w-full bg-gray-200 text-gray-400 border-0">
                    暂无数据
                  </Button>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* 分类提示词 */}
      <div className="space-y-8">
        <h2 className="text-2xl font-bold text-gray-900">
          分类提示词库
        </h2>

        {categoriesWithPrompts.map((category, categoryIndex) => {
          const Icon = category.icon
          return (
            <div key={categoryIndex} className="space-y-4">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color} text-white mr-3`}>
                  <Icon className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{category.title}</h3>
                  <p className="text-sm text-gray-600">{category.description}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {category.prompts.length > 0 ? category.prompts.map((prompt, promptIndex) => (
                  <Card key={prompt.id || promptIndex} className="hover:shadow-md transition-shadow border-0 bg-white/60 backdrop-blur-sm h-[220px] flex flex-col">
                    <CardHeader className="pb-3 flex-shrink-0">
                      <CardTitle className="text-base line-clamp-2">{prompt.title}</CardTitle>
                      <CardDescription className="text-sm leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                        {prompt.description || '专业的AI提示词，提升您的工作效率'}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-3 flex-grow flex flex-col justify-end">
                      <div className="flex flex-wrap gap-1">
                        {(prompt.tags || []).slice(0, 2).map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>

                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center space-x-3">
                          <span className="flex items-center">
                            <Copy className="h-3 w-3 mr-1" />
                            {prompt.downloads || 0}
                          </span>
                          <span className="flex items-center">
                            <Star className="h-3 w-3 mr-1" />
                            {prompt.likes || 0}
                          </span>
                        </div>
                        <Link href={`/prompts/${prompt.id}`}>
                          <Button size="sm" variant="outline" className="h-7 text-xs">
                            使用
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                )) : (
                  // 空状态显示
                  Array.from({ length: 3 }).map((_, emptyIndex) => (
                    <Card key={emptyIndex} className="border-0 bg-white/40 backdrop-blur-sm h-[220px] flex flex-col">
                      <CardHeader className="pb-3 flex-shrink-0">
                        <CardTitle className="text-base text-gray-400 line-clamp-2">
                          正在加载{category.title}提示词...
                        </CardTitle>
                        <CardDescription className="text-sm text-gray-400 line-clamp-3 h-[58px] overflow-hidden">
                          请稍候，我们正在为您准备专业的{category.title}工具
                        </CardDescription>
                      </CardHeader>

                      <CardContent className="space-y-3 flex-grow flex flex-col justify-end">
                        <div className="flex flex-wrap gap-1">
                          <Badge variant="outline" className="text-xs text-gray-400">
                            {category.title}
                          </Badge>
                        </div>

                        <div className="flex items-center justify-between text-xs text-gray-400">
                          <div className="flex items-center space-x-3">
                            <span className="flex items-center">
                              <Copy className="h-3 w-3 mr-1" />
                              --
                            </span>
                            <span className="flex items-center">
                              <Star className="h-3 w-3 mr-1" />
                              --
                            </span>
                          </div>
                          <Button size="sm" variant="outline" className="h-7 text-xs" disabled>
                            暂无
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* 底部CTA */}
      <div className="text-center py-12">
        <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 text-white">
          <h2 className="text-2xl font-bold mb-4">
            开始你的AI学习之旅
          </h2>
          <p className="text-green-100 mb-6 max-w-2xl mx-auto">
            安装PromptHub浏览器插件，随时随地使用专业的学习AI提示词，
            让你的学习更高效、更轻松。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/extension"
              className="inline-flex items-center px-6 py-3 bg-white text-green-600 rounded-lg font-medium hover:bg-gray-50 transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              安装浏览器插件
            </Link>
            <Link
              href="/prompts?search=学生"
              className="inline-flex items-center px-6 py-3 bg-green-500 text-white rounded-lg font-medium hover:bg-green-400 transition-colors"
            >
              浏览更多提示词
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </div>
        </div>
      </div>

      {/* 导航 */}
      <div className="flex justify-between items-center pt-8 border-t">
        <Link
          href="/for"
          className="inline-flex items-center text-purple-600 hover:text-purple-700 transition-colors"
        >
          ← 返回职业专区
        </Link>
        <Link
          href="/for/designers"
          className="inline-flex items-center text-purple-600 hover:text-purple-700 transition-colors"
        >
          下一个：设计师专区
          <ArrowRight className="h-4 w-4 ml-1" />
        </Link>
      </div>
    </div>
    </>
  )
}

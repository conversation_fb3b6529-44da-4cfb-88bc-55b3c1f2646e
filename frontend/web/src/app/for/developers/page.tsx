import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Code,
  TrendingUp,
  FileText,
  Bug,
  Database,
  Users,
  ArrowRight,
  Star,
  Copy,
  Download,
  Eye
} from 'lucide-react'

// 获取程序员相关提示词数据
async function getDeveloperPrompts() {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:4000/api'

    // 获取精选推荐（搜索程序员相关提示词，按复制量排序）
    const featuredResponse = await fetch(`${API_BASE_URL}/prompts/public?search=程序员&sortBy=downloads&limit=3`, {
      next: { revalidate: 300 }, // 5分钟缓存
      signal: AbortSignal.timeout(5000)
    })

    let featuredPrompts = []
    if (featuredResponse.ok) {
      const featuredResult = await featuredResponse.json()
      if (featuredResult.success && featuredResult.data) {
        featuredPrompts = featuredResult.data
      }
    }

    return { featuredPrompts }
  } catch (error) {
    console.error('获取程序员提示词数据失败:', error)
    // 返回模拟数据作为后备
    return {
      featuredPrompts: [
        {
          id: 'dev-demo-1',
          title: '代码注释生成器',
          description: '为代码自动生成清晰、专业的注释文档',
          categoryDisplayName: '代码优化',
          downloads: 3456,
          likes: 234,
          views: 12890
        },
        {
          id: 'dev-demo-2',
          title: 'SQL查询优化助手',
          description: '分析并优化SQL查询语句，提升数据库性能',
          categoryDisplayName: '数据库',
          downloads: 2987,
          likes: 198,
          views: 10567
        },
        {
          id: 'dev-demo-3',
          title: '技术文档写作模板',
          description: '创建结构化的技术文档和API说明',
          categoryDisplayName: '文档编写',
          downloads: 2654,
          likes: 176,
          views: 9234
        }
      ]
    }
  }
}

// 根据标签获取分类提示词
async function getPromptsByTags(tags: string[], limit = 3) {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:4000/api'

    // 使用标签筛选功能来查找包含特定标签的提示词
    const response = await fetch(`${API_BASE_URL}/prompts/public?tags=${encodeURIComponent(tags.join(','))}&sortBy=downloads&limit=${limit}`, {
      next: { revalidate: 300 },
      signal: AbortSignal.timeout(5000)
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        return result.data
      }
    }

    return []
  } catch (error) {
    console.error('获取标签提示词失败:', error)
    // 返回模拟数据作为后备
    const mockData = {
      '代码审查': [
        { id: 'code-1', title: '代码质量检查器', description: '全面检查代码质量，发现潜在问题', downloads: 2345, likes: 156, views: 8901, tags: ['代码审查', '质量检查', '最佳实践'] },
        { id: 'code-2', title: '代码重构建议', description: '提供专业的代码重构和优化建议', downloads: 1987, likes: 134, views: 7654, tags: ['代码重构', '性能优化', '架构设计'] },
        { id: 'code-3', title: '安全漏洞扫描', description: '检测代码中的安全漏洞和风险点', downloads: 1654, likes: 112, views: 6543, tags: ['安全检查', '漏洞扫描', '代码安全'] }
      ],
      '技术文档': [
        { id: 'doc-1', title: 'API文档生成器', description: '自动生成规范的API接口文档', downloads: 2876, likes: 189, views: 9876, tags: ['API文档', '接口设计', '文档生成'] },
        { id: 'doc-2', title: '技术方案设计', description: '编写详细的技术方案和架构设计', downloads: 2234, likes: 145, views: 8234, tags: ['技术方案', '架构设计', '系统设计'] },
        { id: 'doc-3', title: '用户手册编写', description: '创建清晰易懂的用户操作手册', downloads: 1876, likes: 123, views: 6789, tags: ['用户手册', '操作指南', '技术写作'] }
      ],
      '问题调试': [
        { id: 'debug-1', title: '错误诊断助手', description: '快速定位和分析程序错误原因', downloads: 3123, likes: 201, views: 11234, tags: ['错误诊断', '问题排查', '调试技巧'] },
        { id: 'debug-2', title: '性能分析工具', description: '分析程序性能瓶颈和优化方案', downloads: 2567, likes: 167, views: 9567, tags: ['性能分析', '性能优化', '系统调优'] },
        { id: 'debug-3', title: '日志分析器', description: '智能分析系统日志，快速发现问题', downloads: 2123, likes: 143, views: 8123, tags: ['日志分析', '问题定位', '系统监控'] }
      ],
      '架构设计': [
        { id: 'arch-1', title: '系统架构评估', description: '评估现有系统架构的合理性和扩展性', downloads: 2789, likes: 178, views: 9789, tags: ['系统架构', '架构评估', '技术选型'] },
        { id: 'arch-2', title: '微服务设计指南', description: '设计高可用的微服务架构方案', downloads: 2456, likes: 156, views: 8456, tags: ['微服务', '分布式系统', '架构设计'] },
        { id: 'arch-3', title: '数据库设计优化', description: '优化数据库结构和查询性能', downloads: 2234, likes: 145, views: 7234, tags: ['数据库设计', '性能优化', '数据建模'] }
      ]
    }

    // 根据标签匹配返回对应的模拟数据
    const firstTag = tags[0]
    if (firstTag.includes('代码') || firstTag.includes('审查')) {
      return mockData['代码审查']
    } else if (firstTag.includes('文档') || firstTag.includes('API')) {
      return mockData['技术文档']
    } else if (firstTag.includes('调试') || firstTag.includes('错误') || firstTag.includes('问题')) {
      return mockData['问题调试']
    } else if (firstTag.includes('架构') || firstTag.includes('设计') || firstTag.includes('系统')) {
      return mockData['架构设计']
    }
    return mockData['代码审查'] || []
  }
}

export const metadata: Metadata = {
  title: '程序员专区 - 代码审查、技术文档、问题调试AI提示词库',
  description: '专为程序员打造的AI提示词库，包含代码质量检查、API文档生成、技术方案评估、性能优化建议等专业工具。',
  keywords: [
    // 程序员核心关键词
    '程序员AI提示词', '代码审查AI助手', '技术文档生成工具',
    '问题调试Prompt', '编程AI工具', '代码优化生成器',
    
    // 代码相关
    '代码注释生成Prompt', '代码质量检查AI',
    '代码重构建议', '安全漏洞扫描AI',
    '代码审查模板', '编程最佳实践AI',
    
    // 技术文档
    'API文档生成AI', '技术方案设计',
    '系统架构文档模板', '用户手册编写助手',
    '技术写作Prompt', '接口文档生成器',
    
    // 问题调试
    '错误诊断AI', '性能分析工具',
    '日志分析Prompt', '问题排查助手',
    '调试技巧AI', '系统监控分析',
    
    // 架构设计
    '系统架构评估AI', '微服务设计指南',
    '数据库优化方案', '技术选型助手',
    '分布式系统设计', '架构方案生成器'
  ],
  openGraph: {
    title: '程序员专区 - 代码审查、技术文档、问题调试AI提示词库',
    description: '专为程序员打造的AI提示词库，包含代码质量检查、API文档生成、技术方案评估等专业工具。',
    type: 'article',
    url: 'https://www.prompthub.xin/for/developers',
    siteName: 'PromptHub',
    locale: 'zh_CN',
  },
  twitter: {
    card: 'summary_large_image',
    title: '程序员专区 - 代码审查、技术文档、问题调试AI提示词库',
    description: '专为程序员打造的AI提示词库，包含代码质量检查、API文档生成、技术方案评估等专业工具。',
  },
  alternates: {
    canonical: 'https://www.prompthub.xin/for/developers',
  },
}

// 定义分类配置（用于获取对应标签的提示词）
const categories = [
  {
    title: '代码审查',
    description: '代码质量检查、重构建议、安全扫描工具',
    icon: Code,
    color: 'from-blue-500 to-indigo-500',
    tags: ['代码审查', '代码质量', '代码重构', '安全检查', '最佳实践', '代码优化']
  },
  {
    title: '技术文档',
    description: 'API文档、技术方案、用户手册生成工具',
    icon: FileText,
    color: 'from-green-500 to-emerald-500',
    tags: ['技术文档', 'API文档', '技术方案', '用户手册', '技术写作', '文档生成']
  },
  {
    title: '问题调试',
    description: '错误诊断、性能分析、日志分析工具',
    icon: Bug,
    color: 'from-red-500 to-pink-500',
    tags: ['问题调试', '错误诊断', '性能分析', '日志分析', '问题排查', '系统监控']
  },
  {
    title: '架构设计',
    description: '系统架构、微服务、数据库设计工具',
    icon: Database,
    color: 'from-purple-500 to-violet-500',
    tags: ['架构设计', '系统架构', '微服务', '数据库设计', '技术选型', '分布式系统']
  }
]

export default async function DevelopersPage() {
  // 获取真实数据
  const { featuredPrompts } = await getDeveloperPrompts()

  // 为每个分类获取对应的提示词
  const categoriesWithPrompts = await Promise.all(
    categories.map(async (category) => ({
      ...category,
      prompts: await getPromptsByTags(category.tags, 3)
    }))
  )

  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "程序员专区",
    "description": "专为程序员打造的AI提示词库，包含代码质量检查、API文档生成、技术方案评估、性能优化建议等专业工具。",
    "url": "https://www.prompthub.xin/for/developers",
    "about": {
      "@type": "Occupation",
      "name": "程序员",
      "alternateName": ["软件工程师", "开发工程师", "码农"],
      "description": "从事软件开发、编程和系统设计的专业技术人员"
    },
    "mainEntity": {
      "@type": "ItemList",
      "name": "程序员AI工具分类",
      "itemListElement": categories.map((category, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "name": category.title,
        "description": category.description
      }))
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://www.prompthub.xin"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "职业专区",
          "item": "https://www.prompthub.xin/for"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "程序员专区",
          "item": "https://www.prompthub.xin/for/developers"
        }
      ]
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面头部 */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 text-white mb-4">
          <Code className="h-8 w-8" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          程序员专区
        </h1>
        <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
          专为程序员打造的AI提示词库，涵盖代码审查、技术文档、问题调试、架构设计等各个环节，
          让AI成为你的编程利器。
        </p>

        {/* 统计数据 */}
        <div className="flex items-center justify-center gap-8 mt-6 text-sm text-gray-600">
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-1" />
            1.8万+ 程序员在使用
          </div>
          <div className="flex items-center">
            <Star className="h-4 w-4 mr-1 text-yellow-500" />
            400+ 专业提示词
          </div>
          <div className="flex items-center">
            <TrendingUp className="h-4 w-4 mr-1 text-green-500" />
            每日新增使用
          </div>
        </div>
      </div>

      {/* 精选提示词 */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Star className="h-6 w-6 text-yellow-500 mr-2" />
            精选推荐
          </h2>
          <Link
            href="/prompts?search=程序员"
            className="text-purple-600 hover:text-purple-700 transition-colors text-sm font-medium"
          >
            查看全部 →
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {featuredPrompts.length > 0 ? featuredPrompts.map((prompt, index) => (
            <Card key={prompt.id || index} className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm h-[280px] flex flex-col">
              <CardHeader className="pb-4 flex-shrink-0">
                <div className="flex items-start justify-between mb-2">
                  <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                    精选
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {prompt.categoryDisplayName || '编程开发'}
                  </Badge>
                </div>
                <CardTitle className="text-lg group-hover:text-purple-600 transition-colors line-clamp-1">
                  {prompt.title}
                </CardTitle>
                <CardDescription className="text-sm text-gray-600 leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                  {prompt.description || '专业的编程AI提示词，助力您的开发工作'}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0 flex-grow flex flex-col justify-end">
                <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <Copy className="h-3 w-3 mr-1" />
                      {prompt.downloads || 0}
                    </span>
                    <span className="flex items-center">
                      <Star className="h-3 w-3 mr-1" />
                      {prompt.likes || 0}
                    </span>
                    <span className="flex items-center">
                      <Eye className="h-3 w-3 mr-1" />
                      {prompt.views || 0}
                    </span>
                  </div>
                </div>

                <Link href={`/prompts/${prompt.id}`}>
                  <Button className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white border-0">
                    立即使用
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )) : (
            // 空状态显示
            Array.from({ length: 3 }).map((_, index) => (
              <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm h-[280px] flex flex-col">
                <CardHeader className="pb-4 flex-shrink-0">
                  <div className="flex items-start justify-between mb-2">
                    <Badge className="bg-gray-100 text-gray-500">
                      暂无数据
                    </Badge>
                  </div>
                  <CardTitle className="text-lg text-gray-400 line-clamp-2">
                    正在加载精选提示词...
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-400 line-clamp-3 h-[60px] overflow-hidden">
                    请稍候，我们正在为您准备最优质的编程提示词
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0 flex-grow flex flex-col justify-end">
                  <div className="flex items-center justify-between text-xs text-gray-400 mb-4">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <Copy className="h-3 w-3 mr-1" />
                        --
                      </span>
                      <span className="flex items-center">
                        <Star className="h-3 w-3 mr-1" />
                        --
                      </span>
                      <span className="flex items-center">
                        <Eye className="h-3 w-3 mr-1" />
                        --
                      </span>
                    </div>
                  </div>

                  <Button disabled className="w-full bg-gray-200 text-gray-400 border-0">
                    暂无数据
                  </Button>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* 分类提示词 */}
      <div className="space-y-8">
        <h2 className="text-2xl font-bold text-gray-900">
          分类提示词库
        </h2>

        {categoriesWithPrompts.map((category, categoryIndex) => {
          const Icon = category.icon
          return (
            <div key={categoryIndex} className="space-y-4">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color} text-white mr-3`}>
                  <Icon className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{category.title}</h3>
                  <p className="text-sm text-gray-600">{category.description}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {category.prompts.length > 0 ? category.prompts.map((prompt, promptIndex) => (
                  <Card key={prompt.id || promptIndex} className="hover:shadow-md transition-shadow border-0 bg-white/60 backdrop-blur-sm h-[220px] flex flex-col">
                    <CardHeader className="pb-3 flex-shrink-0">
                      <CardTitle className="text-base line-clamp-2">{prompt.title}</CardTitle>
                      <CardDescription className="text-sm leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                        {prompt.description || '专业的AI提示词，提升您的工作效率'}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-3 flex-grow flex flex-col justify-end">
                      <div className="flex flex-wrap gap-1">
                        {(prompt.tags || []).slice(0, 2).map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>

                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center space-x-3">
                          <span className="flex items-center">
                            <Copy className="h-3 w-3 mr-1" />
                            {prompt.downloads || 0}
                          </span>
                          <span className="flex items-center">
                            <Star className="h-3 w-3 mr-1" />
                            {prompt.likes || 0}
                          </span>
                        </div>
                        <Link href={`/prompts/${prompt.id}`}>
                          <Button size="sm" variant="outline" className="h-7 text-xs">
                            使用
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                )) : (
                  // 空状态显示
                  Array.from({ length: 3 }).map((_, emptyIndex) => (
                    <Card key={emptyIndex} className="border-0 bg-white/40 backdrop-blur-sm h-[220px] flex flex-col">
                      <CardHeader className="pb-3 flex-shrink-0">
                        <CardTitle className="text-base text-gray-400 line-clamp-2">
                          正在加载{category.title}提示词...
                        </CardTitle>
                        <CardDescription className="text-sm text-gray-400 line-clamp-3 h-[58px] overflow-hidden">
                          请稍候，我们正在为您准备专业的{category.title}工具
                        </CardDescription>
                      </CardHeader>

                      <CardContent className="space-y-3 flex-grow flex flex-col justify-end">
                        <div className="flex flex-wrap gap-1">
                          <Badge variant="outline" className="text-xs text-gray-400">
                            {category.title}
                          </Badge>
                        </div>

                        <div className="flex items-center justify-between text-xs text-gray-400">
                          <div className="flex items-center space-x-3">
                            <span className="flex items-center">
                              <Copy className="h-3 w-3 mr-1" />
                              --
                            </span>
                            <span className="flex items-center">
                              <Star className="h-3 w-3 mr-1" />
                              --
                            </span>
                          </div>
                          <Button size="sm" variant="outline" className="h-7 text-xs" disabled>
                            暂无
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* 底部CTA */}
      <div className="text-center py-12">
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white">
          <h2 className="text-2xl font-bold mb-4">
            开始你的AI编程之旅
          </h2>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            安装PromptHub浏览器插件，随时随地使用专业的编程AI提示词，
            让你的开发工作更高效、更专业。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/extension"
              className="inline-flex items-center px-6 py-3 bg-white text-blue-600 rounded-lg font-medium hover:bg-gray-50 transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              安装浏览器插件
            </Link>
            <Link
              href="/prompts?search=程序员"
              className="inline-flex items-center px-6 py-3 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-400 transition-colors"
            >
              浏览更多提示词
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </div>
        </div>
      </div>

      {/* 导航 */}
      <div className="flex justify-between items-center pt-8 border-t">
        <Link
          href="/for"
          className="inline-flex items-center text-purple-600 hover:text-purple-700 transition-colors"
        >
          ← 返回职业专区
        </Link>
        <Link
          href="/for/creators"
          className="inline-flex items-center text-purple-600 hover:text-purple-700 transition-colors"
        >
          下一个：内容创作者专区
          <ArrowRight className="h-4 w-4 ml-1" />
        </Link>
      </div>
    </div>
    </>
  )
}

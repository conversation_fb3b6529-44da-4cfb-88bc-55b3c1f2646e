import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Megaphone,
  TrendingUp,
  Target,
  BarChart3,
  ShoppingCart,
  Users,
  ArrowRight,
  Star,
  Copy,
  Download,
  Eye
} from 'lucide-react'

// 获取商业营销分类的提示词数据
async function getMarketingPrompts() {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:4000/api'

    // 获取精选推荐（搜索营销人员相关提示词，按复制量排序）
    const featuredResponse = await fetch(`${API_BASE_URL}/prompts/public?search=营销人员&sortBy=downloads&limit=3`, {
      next: { revalidate: 300 }, // 5分钟缓存
      signal: AbortSignal.timeout(5000)
    })

    let featuredPrompts = []
    if (featuredResponse.ok) {
      const featuredResult = await featuredResponse.json()
      if (featuredResult.success && featuredResult.data) {
        featuredPrompts = featuredResult.data
      }
    }

    return { featuredPrompts }
  } catch (error) {
    console.error('获取营销提示词数据失败:', error)
    // 返回模拟数据作为后备
    return {
      featuredPrompts: [
        {
          id: 'demo-1',
          title: '小红书种草文案生成器',
          description: '生成符合小红书调性的种草文案，提升笔记互动率',
          categoryDisplayName: '社媒营销',
          downloads: 2345,
          likes: 156,
          views: 8901
        },
        {
          id: 'demo-2',
          title: '电商详情页转化优化助手',
          description: '分析并优化电商详情页文案，提升转化率',
          categoryDisplayName: '电商运营',
          downloads: 1987,
          likes: 134,
          views: 7654
        },
        {
          id: 'demo-3',
          title: '品牌故事创作模板',
          description: '创作引人入胜的品牌故事，增强品牌认知',
          categoryDisplayName: '品牌营销',
          downloads: 1654,
          likes: 112,
          views: 6543
        }
      ]
    }
  }
}

// 根据标签获取分类提示词
async function getPromptsByTags(tags: string[], limit = 3) {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:4000/api'

    // 使用标签筛选功能来查找包含特定标签的提示词
    const response = await fetch(`${API_BASE_URL}/prompts/public?tags=${encodeURIComponent(tags.join(','))}&sortBy=downloads&limit=${limit}`, {
      next: { revalidate: 300 },
      signal: AbortSignal.timeout(5000)
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        return result.data
      }
    }

    return []
  } catch (error) {
    console.error('获取标签提示词失败:', error)
    // 返回模拟数据作为后备
    const mockData = {
      '电商': [
        { id: 'ecom-1', title: '电商产品描述生成器', description: '根据产品特点生成吸引人的商品描述', downloads: 1234, likes: 89, views: 5678, tags: ['产品文案', '电商运营', '转化优化'] },
        { id: 'ecom-2', title: '淘宝详情页文案优化', description: '优化商品详情页文案，提升转化率', downloads: 987, likes: 76, views: 4321, tags: ['详情页', '转化优化', '淘宝运营'] },
        { id: 'ecom-3', title: '直播带货话术生成', description: '生成专业的直播带货销售话术', downloads: 756, likes: 65, views: 3210, tags: ['直播带货', '销售话术', '电商直播'] }
      ],
      '广告文案': [
        { id: 'ad-1', title: '信息流广告文案生成', description: '创作高转化的信息流广告文案', downloads: 1456, likes: 98, views: 6789, tags: ['信息流', '广告文案', '转化优化'] },
        { id: 'ad-2', title: '品牌slogan创作助手', description: '为品牌创作朗朗上口的广告语', downloads: 834, likes: 67, views: 3456, tags: ['品牌策划', 'slogan', '创意文案'] },
        { id: 'ad-3', title: 'Facebook广告创意优化', description: '优化Facebook广告的创意和文案', downloads: 623, likes: 54, views: 2789, tags: ['Facebook', '海外营销', '广告优化'] }
      ],
      '用户画像': [
        { id: 'user-1', title: '用户画像分析生成器', description: '基于数据生成详细的用户画像', downloads: 1123, likes: 87, views: 5234, tags: ['用户画像', '数据分析', '市场调研'] },
        { id: 'user-2', title: '竞品分析报告助手', description: '生成专业的竞品分析报告', downloads: 789, likes: 69, views: 3678, tags: ['竞品分析', '市场研究', '战略规划'] },
        { id: 'user-3', title: '用户评论情感分析', description: '分析用户评论的情感倾向和关键信息', downloads: 567, likes: 45, views: 2345, tags: ['情感分析', '用户反馈', '产品优化'] }
      ],
      '营销策略': [
        { id: 'strategy-1', title: '节日营销活动策划', description: '为各种节日设计营销活动方案', downloads: 1345, likes: 92, views: 6123, tags: ['活动策划', '节日营销', '营销方案'] },
        { id: 'strategy-2', title: '社媒内容规划助手', description: '制定社交媒体内容发布计划', downloads: 876, likes: 71, views: 4567, tags: ['社媒运营', '内容规划', '品牌传播'] },
        { id: 'strategy-3', title: 'KOL合作方案生成', description: '设计与KOL合作的营销方案', downloads: 654, likes: 58, views: 2987, tags: ['KOL营销', '网红合作', '品牌推广'] }
      ]
    }

    // 根据标签匹配返回对应的模拟数据
    const firstTag = tags[0]
    if (firstTag.includes('电商') || firstTag.includes('产品')) {
      return mockData['电商']
    } else if (firstTag.includes('广告') || firstTag.includes('文案')) {
      return mockData['广告文案']
    } else if (firstTag.includes('用户') || firstTag.includes('数据') || firstTag.includes('分析')) {
      return mockData['用户画像']
    } else if (firstTag.includes('营销') || firstTag.includes('策略') || firstTag.includes('活动')) {
      return mockData['营销策略']
    }
    return mockData['电商'] || []
  }
}

export const metadata: Metadata = {
  title: '营销人员专区 - 电商运营、广告投放、内容营销AI提示词库',
  description: '专为营销人员打造的AI提示词库，包含电商产品描述生成、广告文案优化、用户画像分析、营销策略规划等专业工具。',
  keywords: [
    // 营销人员核心关键词
    '营销人员AI提示词', '电商运营AI助手', '广告投放优化工具',
    '内容营销Prompt', '数字营销AI工具', '营销文案生成器',
    
    // 电商相关
    '电商产品描述生成Prompt', '淘宝详情页文案AI',
    '京东商品标题优化', '拼多多推广文案生成',
    '电商直播话术生成器', '产品卖点提炼AI',
    
    // 广告文案
    '广告文案优化AI', '创意广告语生成',
    'Facebook广告文案模板', '谷歌广告创意助手',
    '信息流广告文案生成', '品牌slogan创作AI',
    
    // 用户分析
    '用户画像分析AI', '市场调研报告生成',
    '竞品分析Prompt', '用户评论情感分析',
    '消费者行为分析AI', '目标用户定位助手',
    
    // 营销策略
    '营销策略规划AI', '节日营销活动策划',
    '品牌推广方案生成', '社媒营销内容规划',
    '邮件营销文案模板', 'KOL合作方案助手'
  ],
  openGraph: {
    title: '营销人员专区 - 电商运营、广告投放、内容营销AI提示词库',
    description: '专为营销人员打造的AI提示词库，包含电商产品描述生成、广告文案优化、用户画像分析等专业工具。',
    type: 'article',
    url: 'https://www.prompthub.xin/for/marketers',
    siteName: 'PromptHub',
    locale: 'zh_CN',
  },
  twitter: {
    card: 'summary_large_image',
    title: '营销人员专区 - 电商运营、广告投放、内容营销AI提示词库',
    description: '专为营销人员打造的AI提示词库，包含电商产品描述生成、广告文案优化、用户画像分析等专业工具。',
  },
  alternates: {
    canonical: 'https://www.prompthub.xin/for/marketers',
  },
}

// 定义分类配置（用于获取对应标签的提示词）
const categories = [
  {
    title: '电商运营',
    description: '产品描述、详情页文案、店铺运营专用工具',
    icon: ShoppingCart,
    color: 'from-pink-500 to-rose-500',
    tags: ['电商', '电商营销', '产品描述', '详情页', '淘宝', '京东', '直播带货', '电商运营']
  },
  {
    title: '广告创意',
    description: '广告文案、创意策划、投放优化工具',
    icon: Megaphone,
    color: 'from-blue-500 to-indigo-500',
    tags: ['广告文案', '信息流', 'slogan', 'Facebook', '广告创意', '品牌策划']
  },
  {
    title: '用户分析',
    description: '用户画像、市场调研、数据分析工具',
    icon: BarChart3,
    color: 'from-green-500 to-emerald-500',
    tags: ['用户画像', '数据分析', '市场调研', '竞品分析', '情感分析', '用户反馈']
  },
  {
    title: '营销策略',
    description: '营销规划、活动策划、推广方案工具',
    icon: Target,
    color: 'from-purple-500 to-violet-500',
    tags: ['营销策略', '活动策划', '节日营销', '社媒运营', 'KOL营销', '品牌推广']
  }
]

export default async function MarketersPage() {
  // 获取真实数据
  const { featuredPrompts } = await getMarketingPrompts()

  // 为每个分类获取对应的提示词
  const categoriesWithPrompts = await Promise.all(
    categories.map(async (category) => ({
      ...category,
      prompts: await getPromptsByTags(category.tags, 3)
    }))
  )
  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面头部 */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-pink-500 to-rose-500 text-white mb-4">
          <Megaphone className="h-8 w-8" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          营销人员专区
        </h1>
        <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
          专为营销人员打造的AI提示词库，涵盖电商运营、广告投放、用户分析、营销策略等各个环节，
          让AI成为你的营销利器。
        </p>
        
        {/* 统计数据 */}
        <div className="flex items-center justify-center gap-8 mt-6 text-sm text-gray-600">
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-1" />
            2.3万+ 营销人员在使用
          </div>
          <div className="flex items-center">
            <Star className="h-4 w-4 mr-1 text-yellow-500" />
            500+ 专业提示词
          </div>
          <div className="flex items-center">
            <TrendingUp className="h-4 w-4 mr-1 text-green-500" />
            每日新增使用
          </div>
        </div>
      </div>

      {/* 精选提示词 */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Star className="h-6 w-6 text-yellow-500 mr-2" />
            精选推荐
          </h2>
          <Link
            href="/prompts?search=营销人员"
            className="text-purple-600 hover:text-purple-700 transition-colors text-sm font-medium"
          >
            查看全部 →
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {featuredPrompts.length > 0 ? featuredPrompts.map((prompt, index) => (
            <Card key={prompt.id || index} className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm h-[280px] flex flex-col">
              <CardHeader className="pb-4 flex-shrink-0">
                <div className="flex items-start justify-between mb-2">
                  <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                    精选
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {prompt.categoryDisplayName || '商业营销'}
                  </Badge>
                </div>
                <CardTitle className="text-lg group-hover:text-purple-600 transition-colors line-clamp-1">
                  {prompt.title}
                </CardTitle>
                <CardDescription className="text-sm text-gray-600 leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                  {prompt.description || '专业的营销AI提示词，助力您的营销工作'}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0 flex-grow flex flex-col justify-end">
                <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <Copy className="h-3 w-3 mr-1" />
                      {prompt.downloads || 0}
                    </span>
                    <span className="flex items-center">
                      <Star className="h-3 w-3 mr-1" />
                      {prompt.likes || 0}
                    </span>
                    <span className="flex items-center">
                      <Eye className="h-3 w-3 mr-1" />
                      {prompt.views || 0}
                    </span>
                  </div>
                </div>

                <Link href={`/prompts/${prompt.id}`}>
                  <Button className="w-full bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white border-0">
                    立即使用
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )) : (
            // 空状态显示
            Array.from({ length: 3 }).map((_, index) => (
              <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm h-[280px] flex flex-col">
                <CardHeader className="pb-4 flex-shrink-0">
                  <div className="flex items-start justify-between mb-2">
                    <Badge className="bg-gray-100 text-gray-500">
                      暂无数据
                    </Badge>
                  </div>
                  <CardTitle className="text-lg text-gray-400 line-clamp-2">
                    正在加载精选提示词...
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-400 line-clamp-3 h-[60px] overflow-hidden">
                    请稍候，我们正在为您准备最优质的营销提示词
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0 flex-grow flex flex-col justify-end">
                  <div className="flex items-center justify-between text-xs text-gray-400 mb-4">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <Copy className="h-3 w-3 mr-1" />
                        --
                      </span>
                      <span className="flex items-center">
                        <Star className="h-3 w-3 mr-1" />
                        --
                      </span>
                      <span className="flex items-center">
                        <Eye className="h-3 w-3 mr-1" />
                        --
                      </span>
                    </div>
                  </div>

                  <Button disabled className="w-full bg-gray-200 text-gray-400 border-0">
                    暂无数据
                  </Button>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* 分类提示词 */}
      <div className="space-y-8">
        <h2 className="text-2xl font-bold text-gray-900">
          分类提示词库
        </h2>
        
        {categoriesWithPrompts.map((category, categoryIndex) => {
          const Icon = category.icon
          return (
            <div key={categoryIndex} className="space-y-4">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color} text-white mr-3`}>
                  <Icon className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{category.title}</h3>
                  <p className="text-sm text-gray-600">{category.description}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {category.prompts.length > 0 ? category.prompts.map((prompt, promptIndex) => (
                  <Card key={prompt.id || promptIndex} className="hover:shadow-md transition-shadow border-0 bg-white/60 backdrop-blur-sm h-[220px] flex flex-col">
                    <CardHeader className="pb-3 flex-shrink-0">
                      <CardTitle className="text-base line-clamp-2">{prompt.title}</CardTitle>
                      <CardDescription className="text-sm leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                        {prompt.description || '专业的AI提示词，提升您的工作效率'}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-3 flex-grow flex flex-col justify-end">
                      <div className="flex flex-wrap gap-1">
                        {prompt.tags && prompt.tags.length > 0 ? prompt.tags.slice(0, 3).map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        )) : (
                          <Badge variant="outline" className="text-xs">
                            {category.title}
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center space-x-3">
                          <span className="flex items-center">
                            <Copy className="h-3 w-3 mr-1" />
                            {prompt.downloads || 0}
                          </span>
                          <span className="flex items-center">
                            <Star className="h-3 w-3 mr-1" />
                            {prompt.likes || 0}
                          </span>
                        </div>
                        <Link href={`/prompts/${prompt.id}`}>
                          <Button size="sm" variant="outline" className="h-7 text-xs">
                            使用
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                )) : (
                  // 空状态显示
                  Array.from({ length: 3 }).map((_, emptyIndex) => (
                    <Card key={emptyIndex} className="border-0 bg-white/40 backdrop-blur-sm h-[220px] flex flex-col">
                      <CardHeader className="pb-3 flex-shrink-0">
                        <CardTitle className="text-base text-gray-400 line-clamp-2">暂无相关提示词</CardTitle>
                        <CardDescription className="text-sm text-gray-400 leading-[1.4] line-clamp-3 h-[58px] overflow-hidden">
                          该分类下暂时没有提示词，敬请期待
                        </CardDescription>
                      </CardHeader>

                      <CardContent className="space-y-3 flex-grow flex flex-col justify-end">
                        <div className="flex flex-wrap gap-1">
                          <Badge variant="outline" className="text-xs text-gray-400">
                            {category.title}
                          </Badge>
                        </div>

                        <div className="flex items-center justify-between text-xs text-gray-400">
                          <div className="flex items-center space-x-3">
                            <span className="flex items-center">
                              <Copy className="h-3 w-3 mr-1" />
                              --
                            </span>
                            <span className="flex items-center">
                              <Star className="h-3 w-3 mr-1" />
                              --
                            </span>
                          </div>
                          <Button size="sm" variant="outline" className="h-7 text-xs" disabled>
                            暂无
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* 底部CTA */}
      <div className="text-center py-12">
        <div className="bg-gradient-to-r from-pink-600 to-rose-600 rounded-2xl p-8 text-white">
          <h2 className="text-2xl font-bold mb-4">
            开始你的AI营销之旅
          </h2>
          <p className="text-pink-100 mb-6 max-w-2xl mx-auto">
            安装PromptHub浏览器插件，随时随地使用专业的营销AI提示词，
            让你的营销工作更高效、更专业。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/extension"
              className="inline-flex items-center px-6 py-3 bg-white text-pink-600 rounded-lg font-medium hover:bg-gray-50 transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              安装浏览器插件
            </Link>
            <Link
              href="/prompts?search=营销人员"
              className="inline-flex items-center px-6 py-3 bg-pink-500 text-white rounded-lg font-medium hover:bg-pink-400 transition-colors"
            >
              浏览更多提示词
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </div>
        </div>
      </div>

      {/* 导航 */}
      <div className="flex justify-between items-center pt-8 border-t">
        <Link 
          href="/for"
          className="inline-flex items-center text-purple-600 hover:text-purple-700 transition-colors"
        >
          ← 返回职业专区
        </Link>
        <Link 
          href="/for/developers"
          className="inline-flex items-center text-purple-600 hover:text-purple-700 transition-colors"
        >
          下一个：程序员专区
          <ArrowRight className="h-4 w-4 ml-1" />
        </Link>
      </div>
    </div>
  )
}

import HomePageClient from './HomePageClient'; // 引入新的客户端组件
import { API_BASE_URL } from '@/lib/api';

// 定义数据类型，与客户端组件保持一致
interface PromptItem {
  id: string | number;
  title: string;
  description: string;
  content: string;
  category: string;
  tags: string[];
  authorName?: string;
  likes?: number;
  usage_count?: number;
  created_at?: string;
}

interface Stats {
  totalPrompts: number;
  totalUsers: number;
  totalCategories: number;
  dailyActive: number;
}

interface PaginatedPrompts {
  data: PromptItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasNext: boolean;
  };
}

// 在服务器端获取提示词数据
async function getPrompts(): Promise<PaginatedPrompts> {
  try {
    // 确保在服务端使用完整的 URL
    const backendPort = process.env.BACKEND_PORT || '4000'
    const apiUrl = typeof window === 'undefined'
      ? `http://127.0.0.1:${backendPort}/api/prompts/public?page=1&limit=6&sortBy=downloads`
      : `${API_BASE_URL}/prompts/public?page=1&limit=6&sortBy=downloads`;

    console.log('🔗 Fetching prompts from:', apiUrl);

    const response = await fetch(apiUrl, {
      next: { revalidate: 0 }, // 禁用缓存，实时获取数据
      signal: AbortSignal.timeout(5000) // 5秒超时
    });
    if (!response.ok) throw new Error(`HTTP ${response.status}: Failed to fetch prompts`);
    const result = await response.json();
    return result.success ? result : { data: [], pagination: { page: 1, limit: 6, total: 0, hasNext: false } };
  } catch (error) {
    console.error("Error fetching prompts:", error);
    return { data: [], pagination: { page: 1, limit: 6, total: 0, hasNext: false } };
  }
}

// 在服务器端获取统计数据
async function getStats(): Promise<Stats> {
  const defaultStats: Stats = { totalPrompts: 1250, totalUsers: 50000, totalCategories: 9, dailyActive: 12000 };
  try {
    // 确保在服务端使用完整的 URL
    const backendPort = process.env.BACKEND_PORT || '4000'
    const apiUrl = typeof window === 'undefined'
      ? `http://127.0.0.1:${backendPort}/api/categories/stats`
      : `${API_BASE_URL}/categories/stats`;

    console.log('📊 Fetching stats from:', apiUrl);

    const response = await fetch(apiUrl, {
      next: { revalidate: 3600 }, // 缓存1小时
      signal: AbortSignal.timeout(5000) // 5秒超时
    });
    if (!response.ok) return defaultStats;

    const result = await response.json();
    if (result.success && Array.isArray(result.data)) {
      const totalPrompts = result.data.reduce((sum: number, category: { prompt_count?: number }) => sum + (category.prompt_count || 0), 0);
      const totalCategories = result.data.length;
      return {
        ...defaultStats,
        totalPrompts: totalPrompts > 0 ? totalPrompts : defaultStats.totalPrompts,
        totalCategories: totalCategories > 0 ? totalCategories : defaultStats.totalCategories,
      };
    }
    return defaultStats;
  } catch (error) {
    console.error("Error fetching stats:", error);
    return defaultStats;
  }
}

// 首页页面组件 (服务器组件)
export default async function HomePage() {
  // 并行获取数据，提高加载性能
  const [promptData, stats] = await Promise.all([
    getPrompts(),
    getStats()
  ]);

  return (
    <HomePageClient 
      initialPrompts={promptData.data} 
      initialStats={stats}
      initialPagination={promptData.pagination}
    />
  );
} 
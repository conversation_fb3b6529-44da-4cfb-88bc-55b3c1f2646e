'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Loader2, Search, AlertCircle, CheckCircle, XCircle } from 'lucide-react'

interface DiagnosisResult {
  config: string;
  site: string;
  token: string;
  status: number;
  ok: boolean;
  response: string;
  error?: string;
}

export default function BaiduDiagnosisPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<DiagnosisResult[]>([])
  const [customSite, setCustomSite] = useState('https://prompthub.xin')
  const [customToken, setCustomToken] = useState('bVucnxKseTp3X1Hh')

  const runDiagnosis = async () => {
    setIsLoading(true)
    setResults([])

    try {
      // 测试多种配置
      const testConfigs = [
        { site: 'https://prompthub.xin', token: 'bVucnxKseTp3X1Hh', name: '当前配置(prompthub.xin)' },
        { site: 'https://www.prompthub.xin', token: 'bVucnxKseTp3X1Hh', name: '带www配置' },
        { site: customSite, token: customToken, name: '自定义配置' }
      ]

      const diagnosisResults: DiagnosisResult[] = []

      for (const config of testConfigs) {
        const pushUrl = `http://data.zz.baidu.com/urls?site=${encodeURIComponent(config.site)}&token=${config.token}`
        
        try {
          // 测试推送一个简单的URL
          const testUrl = config.site.startsWith('http') ? config.site : `https://${config.site}`
          
          const response = await fetch(pushUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'text/plain',
              'User-Agent': 'PromptHub-Diagnosis/1.0'
            },
            body: testUrl
          })

          const result = await response.text()
          
          diagnosisResults.push({
            config: config.name,
            site: config.site,
            token: config.token,
            status: response.status,
            ok: response.ok,
            response: result,
            error: null
          })
        } catch (error) {
          diagnosisResults.push({
            config: config.name,
            site: config.site,
            token: config.token,
            status: 0,
            ok: false,
            response: '',
            error: error instanceof Error ? error.message : '未知错误'
          })
        }
      }

      setResults(diagnosisResults)
    } catch (error) {
      console.error('诊断失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusIcon = (result: DiagnosisResult) => {
    if (result.error) {
      return <XCircle className="h-4 w-4 text-red-500" />
    }
    if (result.ok) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    }
    return <AlertCircle className="h-4 w-4 text-yellow-500" />
  }

  const getStatusBadge = (result: DiagnosisResult) => {
    if (result.error) {
      return <Badge variant="destructive">错误</Badge>
    }
    if (result.ok) {
      return <Badge variant="default">成功</Badge>
    }
    return <Badge variant="secondary">失败</Badge>
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">百度推送诊断</h1>
        <p className="text-muted-foreground">
          诊断百度推送接口配置问题，找到正确的域名和token设置
        </p>
      </div>

      <div className="grid gap-6">
        {/* 配置测试 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              配置测试
            </CardTitle>
            <CardDescription>
              测试不同的域名和token配置
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="site">网站域名</Label>
                <Input
                  id="site"
                  value={customSite}
                  onChange={(e) => setCustomSite(e.target.value)}
                  placeholder="https://prompthub.xin"
                />
              </div>
              <div>
                <Label htmlFor="token">推送Token</Label>
                <Input
                  id="token"
                  value={customToken}
                  onChange={(e) => setCustomToken(e.target.value)}
                  placeholder="bVucnxKseTp3X1Hh"
                />
              </div>
            </div>
            
            <Button 
              onClick={runDiagnosis} 
              disabled={isLoading}
              className="w-full sm:w-auto"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  诊断中...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  开始诊断
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* 诊断结果 */}
        {results.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>诊断结果</CardTitle>
              <CardDescription>
                各种配置的测试结果
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results.map((result, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(result)}
                        <span className="font-medium">{result.config}</span>
                        {getStatusBadge(result)}
                      </div>
                      <span className="text-sm text-muted-foreground">
                        HTTP {result.status}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <strong>域名:</strong> {result.site}
                      </div>
                      <div>
                        <strong>Token:</strong> {result.token.slice(0, 4)}****
                      </div>
                    </div>
                    
                    {result.response && (
                      <div className="mt-3 p-3 bg-muted/50 rounded text-sm">
                        <strong>响应:</strong> {result.response}
                      </div>
                    )}
                    
                    {result.error && (
                      <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded text-sm text-red-800">
                        <strong>错误:</strong> {result.error}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 帮助信息 */}
        <Card>
          <CardHeader>
            <CardTitle>常见问题解决方案</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 text-sm">
            <div>
              <strong>错误: "site init fail"</strong>
              <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                <li>网站可能还没有在百度站长工具中验证</li>
                <li>域名配置不匹配（检查是否需要www前缀）</li>
                <li>Token可能不正确或已过期</li>
              </ul>
            </div>
            
            <div>
              <strong>错误: "site error"</strong>
              <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                <li>域名格式不正确</li>
                <li>网站未通过百度验证</li>
              </ul>
            </div>
            
            <div>
              <strong>错误: "token is not valid"</strong>
              <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                <li>Token错误或已过期</li>
                <li>需要重新从百度站长工具获取Token</li>
              </ul>
            </div>
            
            <div>
              <strong>解决步骤:</strong>
              <ol className="list-decimal list-inside mt-2 space-y-1 text-muted-foreground">
                <li>确认网站已在百度站长工具中验证</li>
                <li>检查域名配置是否与站长工具中一致</li>
                <li>重新获取最新的推送Token</li>
                <li>测试不同的域名格式（带/不带www）</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

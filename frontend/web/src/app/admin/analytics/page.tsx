'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  Users,
  FileText,
  Eye,
  Heart,
  Download,
  Calendar,
  BarChart3
} from 'lucide-react'
import { API_BASE_URL } from '@/lib/api'
import toast from 'react-hot-toast'

interface AnalyticsData {
  overview: {
    totalUsers: number
    totalPrompts: number
    totalViews: number
    totalLikes: number
    totalDownloads: number
    userGrowth: number
    promptGrowth: number
    engagementRate: number
  }
  trends: {
    date: string
    users: number
    prompts: number
    views: number
    likes: number
  }[]
  categories: {
    name: string
    count: number
    percentage: number
  }[]
  topPrompts: {
    id: number
    title: string
    views: number
    likes: number
    downloads: number
  }[]
}

export default function AdminAnalyticsPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('7d') // 7d, 30d, 90d

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
      return
    }

    if (user && !isAdmin(user)) {
      toast.error('您没有访问管理后台的权限')
      router.push('/')
      return
    }

    if (user && isAdmin(user)) {
      fetchAnalytics()
    }
  }, [user, loading, router, timeRange])

  const isAdmin = (user: any) => {
    return user?.email === '<EMAIL>' || user?.role === 'admin'
  }

  const fetchAnalytics = async () => {
    try {
      setIsLoading(true)

      // 尝试获取分析数据，如果API不存在则使用模拟数据
      try {
        // 网页端完全依赖HttpOnly Cookie认证
        const response = await fetch(`${API_BASE_URL}/admin/analytics?range=${timeRange}`, {
          credentials: 'include' // 支持Cookie认证
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            setAnalytics(result.data)
            return
          }
        }
      } catch (error) {
        console.log('分析API不存在，使用模拟数据')
      }

      // 使用模拟数据
      const mockAnalytics: AnalyticsData = {
        overview: {
          totalUsers: 156,
          totalPrompts: 89,
          totalViews: 2340,
          totalLikes: 567,
          totalDownloads: 1234,
          userGrowth: 12.5,
          promptGrowth: 8.3,
          engagementRate: 24.2
        },
        trends: [
          { date: '2024-06-14', users: 145, prompts: 82, views: 2100, likes: 520 },
          { date: '2024-06-15', users: 148, prompts: 84, views: 2180, likes: 535 },
          { date: '2024-06-16', users: 150, prompts: 85, views: 2220, likes: 545 },
          { date: '2024-06-17', users: 152, prompts: 87, views: 2280, likes: 555 },
          { date: '2024-06-18', users: 154, prompts: 88, views: 2310, likes: 562 },
          { date: '2024-06-19', users: 155, prompts: 89, views: 2325, likes: 565 },
          { date: '2024-06-20', users: 156, prompts: 89, views: 2340, likes: 567 }
        ],
        categories: [
          { name: '生产力', count: 25, percentage: 28.1 },
          { name: '编程开发', count: 18, percentage: 20.2 },
          { name: '创意写作', count: 15, percentage: 16.9 },
          { name: '图像生成', count: 12, percentage: 13.5 },
          { name: '数据分析', count: 8, percentage: 9.0 },
          { name: '教育学习', count: 6, percentage: 6.7 },
          { name: '商业营销', count: 3, percentage: 3.4 },
          { name: '生活娱乐', count: 2, percentage: 2.2 }
        ],
        topPrompts: [
          { id: 1, title: '邮件回复助手', views: 245, likes: 89, downloads: 156 },
          { id: 2, title: '代码审查助手', views: 198, likes: 67, downloads: 134 },
          { id: 3, title: '小说情节生成器', views: 167, likes: 45, downloads: 98 },
          { id: 4, title: 'DALL-E 提示词生成器', views: 134, likes: 38, downloads: 87 },
          { id: 5, title: 'SQL查询优化器', views: 112, likes: 32, downloads: 76 }
        ]
      }

      setAnalytics(mockAnalytics)

    } catch (error) {
      console.error('获取分析数据失败:', error)
      toast.error('获取分析数据失败')
    } finally {
      setIsLoading(false)
    }
  }

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载分析数据...</p>
        </div>
      </div>
    )
  }

  if (!user || !isAdmin(user) || !analytics) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-6 py-8">
        {/* 页面头部 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push('/admin')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              返回后台
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">数据分析</h1>
              <p className="text-gray-600">平台使用统计和趋势分析</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500"
            >
              <option value="7d">最近7天</option>
              <option value="30d">最近30天</option>
              <option value="90d">最近90天</option>
            </select>
          </div>
        </div>

        {/* 概览统计 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总用户数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.overview.totalUsers}</div>
              <p className="text-xs text-green-600 flex items-center gap-1">
                <TrendingUp className="h-3 w-3" />
                +{analytics.overview.userGrowth}% 较上期
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总提示词</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.overview.totalPrompts}</div>
              <p className="text-xs text-green-600 flex items-center gap-1">
                <TrendingUp className="h-3 w-3" />
                +{analytics.overview.promptGrowth}% 较上期
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总浏览量</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.overview.totalViews.toLocaleString()}</div>
              <p className="text-xs text-green-600 flex items-center gap-1">
                <TrendingUp className="h-3 w-3" />
                +23% 较上期
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">用户参与度</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.overview.engagementRate}%</div>
              <p className="text-xs text-green-600 flex items-center gap-1">
                <TrendingUp className="h-3 w-3" />
                +2.1% 较上期
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* 分类分布 */}
          <Card>
            <CardHeader>
              <CardTitle>分类分布</CardTitle>
              <CardDescription>各分类提示词数量分布</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.categories.map((category, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-violet-500" style={{
                        backgroundColor: `hsl(${index * 45}, 70%, 50%)`
                      }}></div>
                      <span className="text-sm font-medium">{category.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">{category.count}</span>
                      <Badge variant="outline">{category.percentage}%</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 热门提示词 */}
          <Card>
            <CardHeader>
              <CardTitle>热门提示词</CardTitle>
              <CardDescription>浏览量最高的提示词</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.topPrompts.map((prompt, index) => (
                  <div key={prompt.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 rounded-full bg-violet-100 flex items-center justify-center text-xs font-bold text-violet-600">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium text-sm">{prompt.title}</div>
                        <div className="text-xs text-gray-500 flex items-center gap-3">
                          <span className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            {prompt.views}
                          </span>
                          <span className="flex items-center gap-1">
                            <Heart className="h-3 w-3" />
                            {prompt.likes}
                          </span>
                          <span className="flex items-center gap-1">
                            <Download className="h-3 w-3" />
                            {prompt.downloads}
                          </span>
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/prompts/${prompt.id}`)}
                    >
                      查看
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 趋势图表 */}
        <Card>
          <CardHeader>
            <CardTitle>增长趋势</CardTitle>
            <CardDescription>最近{timeRange === '7d' ? '7天' : timeRange === '30d' ? '30天' : '90天'}的数据趋势</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{analytics.overview.totalUsers}</div>
                  <div className="text-sm text-gray-600">用户总数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{analytics.overview.totalPrompts}</div>
                  <div className="text-sm text-gray-600">提示词总数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{analytics.overview.totalViews.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">总浏览量</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{analytics.overview.totalLikes}</div>
                  <div className="text-sm text-gray-600">总点赞数</div>
                </div>
              </div>
              
              {/* 简单的趋势展示 */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-2">数据趋势（最近7天）</div>
                <div className="grid grid-cols-7 gap-2">
                  {analytics.trends.map((trend, index) => (
                    <div key={index} className="text-center">
                      <div className="text-xs text-gray-500 mb-1">
                        {new Date(trend.date).getDate()}日
                      </div>
                      <div className="space-y-1">
                        <div className="h-2 bg-blue-200 rounded" style={{
                          height: `${(trend.users / analytics.overview.totalUsers) * 20 + 4}px`
                        }}></div>
                        <div className="h-2 bg-green-200 rounded" style={{
                          height: `${(trend.prompts / analytics.overview.totalPrompts) * 20 + 4}px`
                        }}></div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="flex items-center justify-center gap-4 mt-3 text-xs">
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-2 bg-blue-200 rounded"></div>
                    <span>用户</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-2 bg-green-200 rounded"></div>
                    <span>提示词</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

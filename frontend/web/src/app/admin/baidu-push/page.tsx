'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Send, CheckCircle, XCircle, Info } from 'lucide-react'

interface PushResult {
  batch: number;
  success: boolean;
  count: number;
  response?: string;
  error?: string;
}

interface PushResponse {
  success: boolean;
  message: string;
  data?: {
    totalUrls: number;
    staticUrls: number;
    promptUrls: number;
    pushResults: PushResult[];
  };
  error?: string;
  timestamp: string;
}

export default function BaiduPushPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<PushResponse | null>(null)

  const handlePush = async () => {
    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/baidu-push', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data: PushResponse = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        message: '推送请求失败',
        error: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date().toISOString()
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">百度搜索推送</h1>
        <p className="text-muted-foreground">
          将网站URL推送到百度搜索引擎，提高收录效率
        </p>
      </div>

      <div className="grid gap-6">
        {/* 推送控制面板 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Send className="h-5 w-5" />
              推送控制
            </CardTitle>
            <CardDescription>
              点击下方按钮将所有公开页面推送到百度搜索引擎
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={handlePush} 
              disabled={isLoading}
              size="lg"
              className="w-full sm:w-auto"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  推送中...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  开始推送
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* 推送结果 */}
        {result && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {result.success ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500" />
                )}
                推送结果
              </CardTitle>
              <CardDescription>
                推送时间: {new Date(result.timestamp).toLocaleString('zh-CN')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 总体状态 */}
              <div className="flex items-center gap-2">
                <Badge variant={result.success ? "default" : "destructive"}>
                  {result.success ? "成功" : "失败"}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {result.message}
                </span>
              </div>

              {/* 详细统计 */}
              {result.data && (
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div className="bg-muted/50 p-3 rounded-lg">
                    <div className="text-2xl font-bold">{result.data.totalUrls}</div>
                    <div className="text-sm text-muted-foreground">总URL数</div>
                  </div>
                  <div className="bg-muted/50 p-3 rounded-lg">
                    <div className="text-2xl font-bold">{result.data.staticUrls}</div>
                    <div className="text-sm text-muted-foreground">静态页面</div>
                  </div>
                  <div className="bg-muted/50 p-3 rounded-lg">
                    <div className="text-2xl font-bold">{result.data.promptUrls}</div>
                    <div className="text-sm text-muted-foreground">提示词页面</div>
                  </div>
                </div>
              )}

              {/* 批次结果 */}
              {result.data?.pushResults && result.data.pushResults.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium flex items-center gap-2">
                    <Info className="h-4 w-4" />
                    批次详情
                  </h4>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {result.data.pushResults.map((batch, index) => (
                      <div 
                        key={index}
                        className="flex items-center justify-between p-3 bg-muted/30 rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          {batch.success ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                          <span className="font-medium">批次 {batch.batch}</span>
                          <Badge variant="outline">{batch.count} URLs</Badge>
                        </div>
                        {batch.error && (
                          <span className="text-sm text-red-500 max-w-xs truncate">
                            {batch.error}
                          </span>
                        )}
                        {batch.response && batch.success && (
                          <span className="text-sm text-green-600 max-w-xs truncate">
                            {batch.response}
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 错误信息 */}
              {result.error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="text-sm text-red-800">
                    <strong>错误详情:</strong> {result.error}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* 使用说明 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              使用说明
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm text-muted-foreground">
            <div>
              <strong>推送原理:</strong> 使用百度站长工具提供的主动推送API，将网站URL提交给百度搜索引擎。
            </div>
            <div>
              <strong>推送内容:</strong> 包括首页、提示词列表页、扩展下载页等静态页面，以及所有公开的提示词详情页。
            </div>
            <div>
              <strong>推送频率:</strong> 建议每天推送一次，或在发布新内容后手动推送。
            </div>
            <div>
              <strong>注意事项:</strong> 推送不等于收录，百度会根据内容质量和网站权重决定是否收录。
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

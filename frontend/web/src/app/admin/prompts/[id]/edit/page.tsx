'use client'

import { useEffect, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft,
  Save,
  Eye,
  Trash2,
  Plus,
  X
} from 'lucide-react'
import { API_BASE_URL } from '@/lib/api'
import toast from 'react-hot-toast'

interface Prompt {
  id: number
  title: string
  description: string
  category: string
  tags: string[]
  content: string
  userId: number
  authorName: string
  likes: number
  downloads: number
  views: number
  status: string
  isPrivate: boolean
  createdAt: string
  updatedAt: string
}

export default function AdminEditPromptPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const promptId = params.id as string

  const [prompt, setPrompt] = useState<Prompt | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    tags: [] as string[],
    content: '',
    status: 'published',
    isPrivate: false
  })
  const [newTag, setNewTag] = useState('')

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
      return
    }

    if (user && !isAdmin(user)) {
      toast.error('您没有访问管理后台的权限')
      router.push('/')
      return
    }

    if (user && isAdmin(user) && promptId) {
      fetchPrompt()
    }
  }, [user, loading, router, promptId])

  const isAdmin = (user: any) => {
    return user?.email === '<EMAIL>' || user?.role === 'admin'
  }

  const fetchPrompt = async () => {
    try {
      setIsLoading(true)

      // 网页端完全依赖HttpOnly Cookie认证
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      }

      const response = await fetch(`${API_BASE_URL}/prompts/${promptId}`, {
        headers,
        credentials: 'include' // 支持Cookie认证
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          const promptData = result.data
          setPrompt(promptData)
          setFormData({
            title: promptData.title,
            description: promptData.description || '',
            category: promptData.category,
            tags: promptData.tags || [],
            content: promptData.content,
            status: promptData.status,
            isPrivate: promptData.isPrivate
          })
        }
      } else {
        const result = await response.json()
        toast.error(result.message || '获取提示词信息失败')
        router.push('/admin/prompts')
      }
    } catch (error) {
      console.error('获取提示词失败:', error)
      toast.error('获取提示词信息失败')
      router.push('/admin/prompts')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    if (!formData.title.trim() || !formData.content.trim()) {
      toast.error('请填写标题和内容')
      return
    }

    try {
      setIsSaving(true)

      const response = await fetch(`${API_BASE_URL}/prompts/${promptId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include', // 支持Cookie认证
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        toast.success('提示词更新成功')
        router.push('/admin/prompts')
      } else {
        const result = await response.json()
        toast.error(result.message || '更新失败')
      }
    } catch (error) {
      console.error('更新提示词失败:', error)
      toast.error('更新失败')
    } finally {
      setIsSaving(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm('确定要删除这个提示词吗？此操作不可撤销。')) {
      return
    }

    try {
      const token = localStorage.getItem('authToken')
      if (!token) return

      const response = await fetch(`${API_BASE_URL}/prompts/${promptId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        toast.success('提示词删除成功')
        router.push('/admin/prompts')
      } else {
        toast.error('删除失败')
      }
    } catch (error) {
      console.error('删除提示词失败:', error)
      toast.error('删除失败')
    }
  }

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载提示词信息...</p>
        </div>
      </div>
    )
  }

  if (!user || !isAdmin(user) || !prompt) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-6 py-8 max-w-4xl">
        {/* 页面头部 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push('/admin/prompts')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              返回列表
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">编辑提示词</h1>
              <p className="text-gray-600">ID: {promptId}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => router.push(`/prompts/${promptId}`)}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              预览
            </Button>
            <Button
              variant="outline"
              onClick={handleDelete}
              className="flex items-center gap-2 text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
              删除
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {isSaving ? '保存中...' : '保存'}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 主要编辑区域 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 基本信息 */}
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
                <CardDescription>编辑提示词的基本信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">标题</label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="输入提示词标题"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">描述</label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="输入提示词描述"
                    rows={3}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">分类</label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500"
                  >
                    <option value="">选择分类</option>
                    <option value="ai-assistant">生产力</option>
                    <option value="copywriting">创意写作</option>
                    <option value="programming">编程开发</option>
                    <option value="design">图像生成</option>
                    <option value="data-analysis">数据分析</option>
                    <option value="education">教育学习</option>
                    <option value="business-analysis">商业营销</option>
                    <option value="lifestyle">生活娱乐</option>
                    <option value="mcp-tools">MCP工具</option>
                  </select>
                </div>
              </CardContent>
            </Card>

            {/* 提示词内容 */}
            <Card>
              <CardHeader>
                <CardTitle>提示词内容</CardTitle>
                <CardDescription>编辑提示词的具体内容</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="输入提示词内容"
                  rows={15}
                  className="font-mono"
                />
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 发布设置 */}
            <Card>
              <CardHeader>
                <CardTitle>发布设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">状态</label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500"
                  >
                    <option value="published">已发布</option>
                    <option value="draft">草稿</option>
                  </select>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="isPrivate"
                    checked={formData.isPrivate}
                    onChange={(e) => setFormData(prev => ({ ...prev, isPrivate: e.target.checked }))}
                    className="rounded"
                  />
                  <label htmlFor="isPrivate" className="text-sm">设为私有</label>
                </div>
              </CardContent>
            </Card>

            {/* 标签管理 */}
            <Card>
              <CardHeader>
                <CardTitle>标签</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="添加标签"
                    onKeyPress={(e) => e.key === 'Enter' && addTag()}
                  />
                  <Button onClick={addTag} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="flex items-center gap-1">
                      #{tag}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => removeTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 统计信息 */}
            <Card>
              <CardHeader>
                <CardTitle>统计信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">浏览次数</span>
                  <span className="font-medium">{prompt.views}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">点赞数</span>
                  <span className="font-medium">{prompt.likes}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">复制次数</span>
                  <span className="font-medium">{prompt.downloads}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">创建时间</span>
                  <span className="text-sm">{new Date(prompt.createdAt).toLocaleDateString('zh-CN')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">更新时间</span>
                  <span className="text-sm">{new Date(prompt.updatedAt).toLocaleDateString('zh-CN')}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

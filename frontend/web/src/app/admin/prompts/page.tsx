'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  ArrowLeft,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Heart,
  Download,
  Filter
} from 'lucide-react'
import { Pagination } from '@/components/ui/pagination'
import { API_BASE_URL, api } from '@/lib/api'
import toast from 'react-hot-toast'
import { getCategoryDisplayName } from '@/lib/category-utils'

interface Prompt {
  id: number
  title: string
  description: string
  category: string
  tags: string[]
  content: string
  userId: number
  authorName: string
  likes: number
  downloads: number
  views: number
  status: string
  isPrivate: boolean
  createdAt: string
  updatedAt: string
}

interface Category {
  id: number
  name: string
  description: string
  icon: string
  color: string
  prompt_count?: number
  sort_order?: number
  isActive?: boolean
  value?: string
  label?: string
  displayName?: string
}

export default function AdminPromptsPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchInput, setSearchInput] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [total, setTotal] = useState(0)

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
      return
    }

    if (user && !isAdmin(user)) {
      toast.error('您没有访问管理后台的权限')
      router.push('/')
      return
    }

    if (user && isAdmin(user)) {
      fetchCategories()
      fetchPrompts()
    }
  }, [user, loading, router, currentPage, searchQuery, selectedCategory, pageSize])

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await fetch(`${api.categories.list}`)
      if (!response.ok) {
        throw new Error('获取分类失败')
      }
      const result = await response.json()
      if (result.success) {
        setCategories(result.data)
      }
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }

  const isAdmin = (user: any) => {
    return user?.email === '<EMAIL>' || user?.role === 'admin'
  }

  const fetchPrompts = async () => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
        ...(searchQuery && { search: searchQuery }),
        ...(selectedCategory && { category: selectedCategory })
      })

      // 网页端完全依赖HttpOnly Cookie认证
      const headers = {
        'Content-Type': 'application/json'
      }

      const response = await fetch(`${API_BASE_URL}/admin/prompts?${params}`, {
        headers,
        credentials: 'include' // 支持Cookie认证
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setPrompts(result.data)
          setTotalPages(result.pagination?.totalPages || 1)
          setTotal(result.pagination?.total || 0)
        }
      } else {
        // 如果管理员API不存在，使用普通API
        const publicResponse = await fetch(`${API_BASE_URL}/prompts/public?${params}`)
        if (publicResponse.ok) {
          const result = await publicResponse.json()
          if (result.success) {
            setPrompts(result.data)
            setTotalPages(result.pagination?.totalPages || 1)
            setTotal(result.pagination?.total || 0)
          }
        }
      }
    } catch (error) {
      console.error('获取提示词列表失败:', error)
      toast.error('获取提示词列表失败')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (promptId: number) => {
    if (!confirm('确定要删除这个提示词吗？此操作不可撤销。')) {
      return
    }

    try {
      // 网页端完全依赖HttpOnly Cookie认证
      const response = await fetch(`${API_BASE_URL}/prompts/${promptId}`, {
        method: 'DELETE',
        credentials: 'include' // 支持Cookie认证
      })

      if (response.ok) {
        toast.success('提示词删除成功')
        fetchPrompts() // 重新获取列表
      } else {
        toast.error('删除失败')
      }
    } catch (error) {
      console.error('删除提示词失败:', error)
      toast.error('删除失败')
    }
  }

  const handleStatusToggle = async (promptId: number, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'published' ? 'draft' : 'published'

      // 网页端完全依赖HttpOnly Cookie认证
      const response = await fetch(`${API_BASE_URL}/prompts/${promptId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus }),
        credentials: 'include' // 支持Cookie认证
      })

      if (response.ok) {
        toast.success(`提示词已${newStatus === 'published' ? '发布' : '下架'}`)
        fetchPrompts()
      } else {
        toast.error('状态更新失败')
      }
    } catch (error) {
      console.error('更新状态失败:', error)
      toast.error('状态更新失败')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载提示词列表...</p>
        </div>
      </div>
    )
  }

  if (!user || !isAdmin(user)) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-6 py-8">
        {/* 页面头部 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push('/admin')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              返回后台
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">提示词管理</h1>
              <p className="text-gray-600">管理所有平台提示词</p>
            </div>
          </div>
          <Button
            onClick={() => window.open('/create', '_blank')}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            添加提示词
          </Button>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索提示词标题或内容... (按回车搜索)"
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        setSearchQuery(searchInput)
                        setCurrentPage(1) // 重置到第一页
                      }
                    }}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500"
                >
                  <option value="">所有分类</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.name}>
                      {category.description || category.displayName || getCategoryDisplayName(category.name)}
                    </option>
                  ))}
                </select>
                <Button variant="outline" onClick={fetchPrompts}>
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 提示词列表 */}
        <Card>
          <CardHeader>
            <CardTitle>提示词列表 ({prompts.length})</CardTitle>
            <CardDescription>
              管理所有用户创建的提示词
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>标题</TableHead>
                    <TableHead>分类</TableHead>
                    <TableHead>作者</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>统计</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {prompts.map((prompt) => (
                    <TableRow key={prompt.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{prompt.title}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {prompt.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {getCategoryDisplayName(prompt.category)}
                        </Badge>
                      </TableCell>
                      <TableCell>{prompt.authorName}</TableCell>
                      <TableCell>
                        <Badge 
                          variant={prompt.status === 'published' ? 'default' : 'secondary'}
                          className="cursor-pointer"
                          onClick={() => handleStatusToggle(prompt.id, prompt.status)}
                        >
                          {prompt.status === 'published' ? '已发布' : '草稿'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            {prompt.views}
                          </span>
                          <span className="flex items-center gap-1">
                            <Heart className="h-3 w-3" />
                            {prompt.likes}
                          </span>
                          <span className="flex items-center gap-1">
                            <Download className="h-3 w-3" />
                            {prompt.downloads}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(prompt.createdAt)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(`/prompts/${prompt.id}`, '_blank')}
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(`/admin/prompts/${prompt.id}/edit`, '_blank')}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(prompt.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* 分页 */}
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              showSizeChanger={true}
              pageSize={pageSize}
              onPageSizeChange={(size) => {
                setPageSize(size)
                setCurrentPage(1) // 重置到第一页
              }}
              total={total}
              className="mt-6"
            />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

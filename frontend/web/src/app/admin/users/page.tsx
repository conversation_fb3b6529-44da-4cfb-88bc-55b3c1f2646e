'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  ArrowLeft,
  Search,
  UserCheck,
  UserX,
  Mail,
  Calendar,
  FileText
} from 'lucide-react'
import { API_BASE_URL } from '@/lib/api'
import toast from 'react-hot-toast'
import { Pagination } from '@/components/ui/pagination'

interface User {
  id: number
  username: string
  email: string
  role: string
  status: string
  createdAt: string
  updatedAt: string
  promptCount?: number
  lastLoginAt?: string
}

export default function AdminUsersPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchInput, setSearchInput] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [total, setTotal] = useState(0)

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
      return
    }

    if (user && !isAdmin(user)) {
      toast.error('您没有访问管理后台的权限')
      router.push('/')
      return
    }

    if (user && isAdmin(user)) {
      fetchUsers()
    }
  }, [user, loading, router, currentPage, searchQuery, pageSize])

  const isAdmin = (user: any) => {
    return user?.email === '<EMAIL>' || user?.role === 'admin'
  }

  const fetchUsers = async () => {
    try {
      setIsLoading(true)

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
        ...(searchQuery && { search: searchQuery })
      })

      // 网页端完全依赖HttpOnly Cookie认证
      const response = await fetch(`${API_BASE_URL}/admin/users?${params}`, {
        credentials: 'include' // 支持Cookie认证
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      if (result.success) {
        setUsers(result.data)
        setTotalPages(result.pagination?.totalPages || 1)
        setTotal(result.pagination?.total || 0)
      } else {
        throw new Error(result.message || '获取用户列表失败')
      }

    } catch (error) {
      console.error('获取用户列表失败:', error)
      toast.error('获取用户列表失败')
    } finally {
      setIsLoading(false)
    }
  }

  const handleStatusToggle = async (userId: number, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active'

      // 网页端完全依赖HttpOnly Cookie认证
      const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include', // 支持Cookie认证
        body: JSON.stringify({ status: newStatus })
      })

      if (response.ok) {
        // 更新本地状态
        setUsers(prev => prev.map(user =>
          user.id === userId ? { ...user, status: newStatus } : user
        ))
        toast.success(`用户已${newStatus === 'active' ? '激活' : '禁用'}`)
      } else {
        toast.error('状态更新失败')
      }
    } catch (error) {
      console.error('更新用户状态失败:', error)
      toast.error('状态更新失败')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载用户列表...</p>
        </div>
      </div>
    )
  }

  if (!user || !isAdmin(user)) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-6 py-8">
        {/* 页面头部 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push('/admin')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              返回后台
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">用户管理</h1>
              <p className="text-gray-600">管理所有平台用户</p>
            </div>
          </div>
        </div>

        {/* 搜索 */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索用户名或邮箱... (按回车搜索)"
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        setSearchQuery(searchInput)
                        setCurrentPage(1) // 重置到第一页
                      }
                    }}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 用户列表 */}
        <Card>
          <CardHeader>
            <CardTitle>用户列表 ({users.length})</CardTitle>
            <CardDescription>
              管理所有注册用户
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>用户信息</TableHead>
                    <TableHead>角色</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>提示词数量</TableHead>
                    <TableHead>注册时间</TableHead>
                    <TableHead>最后登录</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((userData) => (
                    <TableRow key={userData.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{userData.username}</div>
                          <div className="text-sm text-gray-500 flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {userData.email}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={userData.role === 'admin' ? 'default' : 'secondary'}>
                          {userData.role === 'admin' ? '管理员' : '用户'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={userData.status === 'active' ? 'default' : 'destructive'}
                          className="cursor-pointer"
                          onClick={() => handleStatusToggle(userData.id, userData.status)}
                        >
                          {userData.status === 'active' ? '正常' : '禁用'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <FileText className="h-3 w-3 text-gray-400" />
                          {userData.promptCount || 0}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-sm">
                          <Calendar className="h-3 w-3 text-gray-400" />
                          {formatDate(userData.createdAt)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-gray-500">
                          {userData.lastLoginAt ? formatDate(userData.lastLoginAt) : '从未登录'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleStatusToggle(userData.id, userData.status)}
                            className={userData.status === 'active' ? 'text-red-600' : 'text-green-600'}
                          >
                            {userData.status === 'active' ? (
                              <>
                                <UserX className="h-3 w-3 mr-1" />
                                禁用
                              </>
                            ) : (
                              <>
                                <UserCheck className="h-3 w-3 mr-1" />
                                激活
                              </>
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* 分页 */}
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              showSizeChanger={true}
              pageSize={pageSize}
              onPageSizeChange={(size) => {
                setPageSize(size)
                setCurrentPage(1) // 重置到第一页
              }}
              total={total}
              className="mt-6"
            />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

# PromptHub 管理后台

## 🎯 功能概述

PromptHub 管理后台是一个功能完整的管理系统，用于管理平台的提示词、用户和数据分析。

## 🔐 访问权限

### 管理员权限
- 邮箱为 `<EMAIL>` 的用户
- 或者 `role` 字段为 `admin` 的用户

### 访问入口
- 管理员登录后，在页面右上角会显示"管理后台"按钮
- 直接访问：`/admin`

## 📱 功能模块

### 1. 仪表板 (`/admin`)
- **概览统计**：用户数、提示词数、浏览量、点赞数
- **快速操作**：直接跳转到各个管理模块
- **实时数据**：显示平台的关键指标

### 2. 提示词管理 (`/admin/prompts`)
- **列表展示**：所有提示词的详细信息
- **搜索筛选**：按标题、内容、分类筛选
- **状态管理**：发布/下架提示词
- **编辑功能**：修改提示词内容和参数
- **删除功能**：删除不当内容
- **统计信息**：浏览量、点赞数、复制次数

### 3. 提示词编辑 (`/admin/prompts/[id]/edit`)
- **基本信息编辑**：标题、描述、分类
- **内容编辑**：提示词具体内容
- **标签管理**：添加/删除标签
- **发布设置**：状态、可见性控制
- **统计查看**：实时数据展示

### 4. 用户管理 (`/admin/users`)
- **用户列表**：所有注册用户信息
- **搜索功能**：按用户名、邮箱搜索
- **状态控制**：激活/禁用用户账户
- **角色管理**：查看用户角色
- **活动统计**：登录时间、创作数量

### 5. 数据分析 (`/admin/analytics`)
- **概览指标**：用户增长、内容增长、参与度
- **分类分布**：各分类提示词数量统计
- **热门内容**：浏览量最高的提示词
- **趋势分析**：时间范围内的数据变化
- **时间筛选**：7天、30天、90天数据

## 🛠️ 技术特性

### 前端技术
- **React 19** + **Next.js 15**
- **TypeScript** 类型安全
- **Tailwind CSS** + **Shadcn/ui** 组件库
- **响应式设计**：完美适配各种设备

### 后端支持
- **权限验证**：JWT + 管理员角色验证
- **API接口**：RESTful API设计
- **数据安全**：多层权限控制
- **错误处理**：完善的错误处理机制

### 安全特性
- **身份验证**：JWT Token验证
- **权限控制**：管理员角色验证
- **操作确认**：危险操作需要确认
- **数据保护**：敏感操作日志记录

## 📊 API 端点

### 管理员统计
```
GET /api/admin/stats
Authorization: Bearer <token>
```

### 提示词管理
```
GET /api/admin/prompts?page=1&limit=20&search=&category=
PUT /api/prompts/:id
DELETE /api/prompts/:id
Authorization: Bearer <token>
```

### 用户管理
```
GET /api/admin/users?page=1&limit=20&search=
PUT /api/admin/users/:id
Authorization: Bearer <token>
```

## 🎨 界面特色

### 设计风格
- **现代化设计**：简洁美观的界面
- **一致性**：统一的设计语言
- **易用性**：直观的操作流程
- **响应式**：适配各种屏幕尺寸

### 交互体验
- **实时反馈**：操作结果即时显示
- **加载状态**：优雅的加载动画
- **错误处理**：友好的错误提示
- **确认机制**：重要操作需要确认

## 🚀 使用指南

### 1. 登录管理后台
1. 使用管理员账户登录
2. 点击右上角"管理后台"按钮
3. 进入管理后台首页

### 2. 管理提示词
1. 点击"提示词管理"
2. 使用搜索和筛选功能查找内容
3. 点击"编辑"按钮修改内容
4. 点击状态标签切换发布状态
5. 使用"删除"按钮移除不当内容

### 3. 管理用户
1. 点击"用户管理"
2. 查看所有用户信息
3. 使用搜索功能查找特定用户
4. 点击状态按钮激活/禁用用户

### 4. 查看数据分析
1. 点击"数据分析"
2. 选择时间范围
3. 查看各项统计指标
4. 分析平台使用趋势

## 🔧 开发说明

### 权限验证逻辑
```typescript
const isAdmin = (user: any) => {
  return user?.email === '<EMAIL>' || user?.role === 'admin'
}
```

### 路由保护
所有管理后台页面都有权限验证，非管理员用户会被重定向到登录页面。

### 数据获取
- 优先使用真实API
- API不存在时使用模拟数据
- 保证功能的完整性和可用性

## 📝 注意事项

1. **权限管理**：确保只有管理员能访问后台
2. **数据安全**：重要操作需要确认
3. **性能优化**：大量数据使用分页加载
4. **错误处理**：提供友好的错误提示
5. **日志记录**：记录重要操作日志

## 🎯 未来规划

- [ ] 批量操作功能
- [ ] 更详细的数据分析
- [ ] 操作日志记录
- [ ] 内容审核工作流
- [ ] 自动化管理规则

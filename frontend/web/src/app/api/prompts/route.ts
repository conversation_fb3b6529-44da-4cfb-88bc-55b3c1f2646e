import { NextRequest, NextResponse } from 'next/server'

// 🚫 此API路由已废弃
// PromptHub 已改用 MySQL + Node.js 后端
// 前端请使用 api-client.ts 调用后端API

export async function GET(request: NextRequest) {
  return NextResponse.json({
    error: '此API路由已废弃，请使用后端API'
  }, { status: 410 })
}

// 创建新提示词
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // 验证必需字段
    const { title, content, type, category, description, tags } = body

    if (!title || !content || !type || !category) {
      return NextResponse.json({ error: '缺少必需字段' }, { status: 400 })
    }

    // 获取当前用户
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: '需要登录' }, { status: 401 })
    }

    // 🚫 此API路由已废弃 - PromptHub 已改用 MySQL + Node.js 后端
    return NextResponse.json({
      error: '此API路由已废弃，请使用后端API'
    }, { status: 410 })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器错误' }, { status: 500 })
  }
} 
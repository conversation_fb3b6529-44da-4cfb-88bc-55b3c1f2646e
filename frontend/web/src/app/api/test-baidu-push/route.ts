import { NextResponse } from 'next/server'

// 强制动态渲染
export const dynamic = 'force-dynamic';

export async function POST() {
  try {
    console.log('🧪 测试百度推送接口...');
    
    // 测试不同的域名配置
    const testConfigs = [
      {
        site: 'https://prompthub.xin',
        token: 'bVucnxKseTp3X1Hh',
        name: '不带www的域名'
      },
      {
        site: 'https://www.prompthub.xin',
        token: 'bVucnxKseTp3X1Hh',
        name: '带www的域名'
      },
      {
        site: 'prompthub.xin',
        token: 'bVucnxKseTp3X1Hh',
        name: '不带协议的域名'
      }
    ];

    const results = [];

    for (const config of testConfigs) {
      const pushUrl = `http://data.zz.baidu.com/urls?site=${encodeURIComponent(config.site)}&token=${config.token}`;
      
      try {
        console.log(`🔍 测试配置: ${config.name} - ${config.site}`);
        
        // 测试推送一个简单的URL
        const testUrls = [`${config.site.startsWith('http') ? config.site : 'https://' + config.site}`];
        
        const response = await fetch(pushUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'text/plain',
            'User-Agent': 'PromptHub-Test/1.0'
          },
          body: testUrls.join('\n')
        });

        const result = await response.text();
        
        results.push({
          config: config.name,
          site: config.site,
          status: response.status,
          ok: response.ok,
          response: result,
          error: null
        });

        console.log(`📊 ${config.name}: ${response.status} - ${result}`);
        
      } catch (error) {
        console.error(`💥 ${config.name} 测试失败:`, error);
        results.push({
          config: config.name,
          site: config.site,
          status: 0,
          ok: false,
          response: null,
          error: error.message
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: '百度推送测试完成',
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 测试失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '测试失败',
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET方法用于查看测试说明
export async function GET() {
  return NextResponse.json({
    success: true,
    message: '百度推送测试API',
    description: '测试不同的域名配置以找到正确的百度推送设置',
    usage: {
      method: 'POST',
      description: '发送POST请求测试百度推送配置',
      example: 'curl -X POST http://localhost:3000/api/test-baidu-push'
    },
    timestamp: new Date().toISOString()
  });
}

import { NextResponse } from 'next/server'
import { BAIDU_PUSH_CONFIG, validateBaiduConfig, buildPushUrl, getConfigInfo } from '@/lib/baidu-config'

interface Prompt {
  id: string;
  updated_at: string;
}

// 强制动态渲染
export const dynamic = 'force-dynamic';

// 推送URL到百度搜索引擎
async function pushUrlsToBaidu(urls: string[]) {
  // 验证配置
  const configValidation = validateBaiduConfig();
  if (!configValidation.valid) {
    return {
      success: false,
      message: `配置错误: ${configValidation.errors.join(', ')}`
    };
  }

  if (urls.length === 0) {
    return { success: false, message: '没有URL需要推送' };
  }

  // 使用配置中的批次大小
  const batchSize = BAIDU_PUSH_CONFIG.batchSize;
  const batches = [];

  for (let i = 0; i < urls.length; i += batchSize) {
    batches.push(urls.slice(i, i + batchSize));
  }

  const results = [];

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    const pushUrl = buildPushUrl();
    
    try {
      console.log(`📤 推送第 ${i + 1}/${batches.length} 批次，包含 ${batch.length} 个URL`);
      
      const response = await fetch(pushUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'text/plain',
          'User-Agent': BAIDU_PUSH_CONFIG.userAgent
        },
        body: batch.join('\n'),
        signal: AbortSignal.timeout(BAIDU_PUSH_CONFIG.timeout)
      });

      const result = await response.text();
      
      if (response.ok) {
        console.log(`✅ 百度推送第 ${i + 1} 批次成功:`, result);
        results.push({
          batch: i + 1,
          success: true,
          count: batch.length,
          response: result
        });
      } else {
        console.error(`❌ 百度推送第 ${i + 1} 批次失败 (${response.status}):`, result);
        results.push({
          batch: i + 1,
          success: false,
          count: batch.length,
          error: `HTTP ${response.status}: ${result}`
        });
      }
    } catch (batchError) {
      console.error(`💥 百度推送第 ${i + 1} 批次异常:`, batchError);
      results.push({
        batch: i + 1,
        success: false,
        count: batch.length,
        error: batchError.message
      });
    }
    
    // 批次间稍作延迟，避免请求过于频繁
    if (i < batches.length - 1) {
      await new Promise(resolve => setTimeout(resolve, BAIDU_PUSH_CONFIG.batchDelay));
    }
  }

  const successCount = results.filter(r => r.success).length;
  const totalUrls = results.reduce((sum, r) => sum + r.count, 0);
  
  return {
    success: successCount > 0,
    message: `推送完成: ${successCount}/${batches.length} 批次成功，共 ${totalUrls} 个URL`,
    results
  };
}

export async function POST() {
  try {
    console.log('🚀 开始百度推送任务...');
    
    // 根据环境确定 API 地址
    const API_URL = process.env.NODE_ENV === 'production'
      ? 'http://127.0.0.1:4000'  // 生产环境：直接访问内部后端服务
      : 'http://localhost:4000'; // 开发环境：访问本地后端服务

    const APP_URL = 'https://prompthub.xin';

    // 1. 获取所有公开的提示词
    let prompts: Prompt[] = [];

    try {
      console.log(`🔍 从后端API获取提示词: ${API_URL}`);
      const response = await fetch(`${API_URL}/api/prompts/all-public-ids`, {
        headers: {
          'User-Agent': 'PromptHub-Baidu-Push'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data && Array.isArray(result.data)) {
          prompts = result.data;
          console.log(`✅ 成功获取 ${prompts.length} 个提示词`);
        }
      } else {
        console.warn(`❌ 获取提示词失败: HTTP ${response.status}`);
      }
    } catch (apiError) {
      console.error(`💥 API调用失败:`, apiError);
    }

    // 2. 构建所有需要推送的URL
    const staticUrls = [
      APP_URL,
      `${APP_URL}/prompts`,
      `${APP_URL}/extension`,
      `${APP_URL}/about`,
      `${APP_URL}/privacy`,
      `${APP_URL}/terms`,

      // 解决方案页面 - 高价值SEO页面
      `${APP_URL}/solutions`,
      `${APP_URL}/solutions/how-to-ask-ai-effectively`,
      `${APP_URL}/solutions/manage-prompts-efficiently`,

      // 职业专区页面 - 高价值SEO页面
      `${APP_URL}/for`,
      `${APP_URL}/for/marketers`,
    ];

    const promptUrls = prompts.map(prompt => `${APP_URL}/prompts/${prompt.id}`);
    const allUrls = [...staticUrls, ...promptUrls];

    console.log(`📊 准备推送: ${staticUrls.length} 个静态页面 + ${promptUrls.length} 个提示词页面 = ${allUrls.length} 个URL`);

    // 3. 执行百度推送
    const pushResult = await pushUrlsToBaidu(allUrls);

    return NextResponse.json({
      success: pushResult.success,
      message: pushResult.message,
      data: {
        totalUrls: allUrls.length,
        staticUrls: staticUrls.length,
        promptUrls: promptUrls.length,
        pushResults: pushResult.results
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 百度推送任务失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '百度推送任务失败',
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET方法用于查看推送状态和配置
export async function GET() {
  const configValidation = validateBaiduConfig();

  return NextResponse.json({
    success: configValidation.valid,
    message: configValidation.valid ? '百度推送API就绪' : '配置错误',
    config: getConfigInfo(),
    validation: configValidation,
    usage: {
      method: 'POST',
      description: '发送POST请求到此端点以触发百度推送',
      example: 'curl -X POST https://prompthub.xin/api/baidu-push'
    },
    timestamp: new Date().toISOString()
  });
}

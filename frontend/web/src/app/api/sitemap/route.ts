import { NextResponse } from 'next/server'

interface Prompt {
  id: string;
  updated_at: string;
}

// 强制动态渲染，避免构建时调用 API
export const dynamic = 'force-dynamic';
export const revalidate = 3600; // 1小时缓存

// 百度推送接口配置
const BAIDU_PUSH_CONFIG = {
  site: 'https://www.prompthub.xin',
  token: 'bVucnxKseTp3X1Hh', // 从百度站长工具获取的正确token
  pushUrl: 'http://data.zz.baidu.com/urls'
};

// 推送URL到百度搜索引擎
async function pushUrlsToBaidu(staticUrls: string[], promptUrls: string[]) {
  try {
    const allUrls = [...staticUrls, ...promptUrls];

    if (allUrls.length === 0) {
      console.log('📭 没有URL需要推送到百度');
      return;
    }

    // 百度推送API有限制，一次最多推送2000个URL
    const batchSize = 2000;
    const batches = [];

    for (let i = 0; i < allUrls.length; i += batchSize) {
      batches.push(allUrls.slice(i, i + batchSize));
    }

    console.log(`🚀 开始推送 ${allUrls.length} 个URL到百度，分 ${batches.length} 批次`);

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const pushUrl = `${BAIDU_PUSH_CONFIG.pushUrl}?site=${BAIDU_PUSH_CONFIG.site}&token=${BAIDU_PUSH_CONFIG.token}`;

      try {
        console.log(`📤 推送第 ${i + 1}/${batches.length} 批次，包含 ${batch.length} 个URL`);

        const response = await fetch(pushUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'text/plain',
            'User-Agent': 'PromptHub-Baidu-Push'
          },
          body: batch.join('\n')
        });

        const result = await response.text();

        if (response.ok) {
          console.log(`✅ 百度推送第 ${i + 1} 批次成功:`, result);
        } else {
          console.error(`❌ 百度推送第 ${i + 1} 批次失败 (${response.status}):`, result);
        }
      } catch (batchError) {
        console.error(`💥 百度推送第 ${i + 1} 批次异常:`, batchError);
      }

      // 批次间稍作延迟，避免请求过于频繁
      if (i < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  } catch (error) {
    console.error('💥 百度推送整体失败:', error);
  }
}

export async function GET() {
  try {
    const APP_URL = 'https://prompthub.xin';

    // 根据环境确定 API 地址
    const API_URL = process.env.NODE_ENV === 'production'
      ? 'http://127.0.0.1:4000'  // 生产环境：直接访问内部后端服务
      : 'http://localhost:4000'; // 开发环境：访问本地后端服务

    console.log(`🗺️ Sitemap API generation started at ${new Date().toISOString()}`);
    console.log(`🔧 Using API_URL: ${API_URL}`);

    // 1. 获取所有公开的提示词
    let prompts: Prompt[] = [];

    try {
      console.log(`🔍 Fetching prompts from backend API: ${API_URL}`);
      const response = await fetch(`${API_URL}/api/prompts/all-public-ids`, {
        headers: {
          'User-Agent': 'PromptHub-Sitemap-Generator'
        },
        next: { revalidate: 3600 } // 1小时缓存
      });

      console.log(`📡 API response status: ${response.status}`);

      if (response.ok) {
        const result = await response.json();
        console.log(`📊 API response:`, { success: result.success, dataLength: result.data?.length });

        if (result.success && result.data && Array.isArray(result.data)) {
          prompts = result.data;
          console.log(`✅ Successfully fetched ${prompts.length} prompts for sitemap`);
        } else {
          console.warn(`⚠️ Invalid response format:`, result);
        }
      } else {
        console.warn(`❌ HTTP ${response.status} from API`);
      }
    } catch (apiError) {
      console.error(`💥 Failed to fetch prompts:`, apiError);
    }

    console.log(`🏁 Final prompts count: ${prompts.length}`);

    // 2. 生成提示词URL
    const promptUrls = prompts.map((prompt) => `  <url>
    <loc>${APP_URL}/prompts/${prompt.id}</loc>
    <lastmod>${new Date(prompt.updated_at).toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`).join('\n');

    // 3. 定义核心静态页面
    const staticUrls = [
      { url: APP_URL, priority: '1.0', changefreq: 'daily' },
      { url: `${APP_URL}/prompts`, priority: '0.9', changefreq: 'daily' },
      { url: `${APP_URL}/extension`, priority: '0.6', changefreq: 'monthly' },
      { url: `${APP_URL}/about`, priority: '0.5', changefreq: 'monthly' },
      { url: `${APP_URL}/privacy`, priority: '0.3', changefreq: 'yearly' },
      { url: `${APP_URL}/terms`, priority: '0.3', changefreq: 'yearly' },

      // 提示工程指南页面 - 高价值SEO页面
      { url: `${APP_URL}/solutions`, priority: '0.8', changefreq: 'weekly' },
      { url: `${APP_URL}/solutions/how-to-ask-ai-effectively`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/solutions/manage-prompts-efficiently`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/solutions/prompt-elements-guide`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/solutions/prompt-design-techniques`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/solutions/few-shot-prompting`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/solutions/chain-of-thought`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/solutions/team-prompt-sharing`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/solutions/prompt-version-control`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/solutions/ai-workflow-optimization`, priority: '0.7', changefreq: 'monthly' },

      // 职业专区页面 - 高价值SEO页面
      { url: `${APP_URL}/for`, priority: '0.8', changefreq: 'weekly' },
      { url: `${APP_URL}/for/developers`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/for/creators`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/for/students`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/for/designers`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/for/hr`, priority: '0.7', changefreq: 'monthly' },
      { url: `${APP_URL}/for/marketers`, priority: '0.7', changefreq: 'monthly' },
    ];

    const staticUrlsXml = staticUrls.map(page => `  <url>
    <loc>${page.url}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n');

    // 4. 生成完整的sitemap XML
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${staticUrlsXml}
${promptUrls}
</urlset>`;

    console.log(`🎯 Generated sitemap with ${staticUrls.length} static pages and ${prompts.length} prompt pages`);

    // 5. 推送URL到百度 (异步执行，不影响sitemap返回)
    pushUrlsToBaidu(staticUrls.map(page => page.url), prompts.map(prompt => `${APP_URL}/prompts/${prompt.id}`))
      .catch(error => console.error('🔥 百度推送失败:', error));

    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // 缓存1小时
      },
    });
  } catch (error) {
    console.error('💥 Sitemap generation failed:', error);
    
    // 返回基本的sitemap作为fallback
    const basicSitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://prompthub.xin</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`;

    return new NextResponse(basicSitemap, {
      headers: {
        'Content-Type': 'application/xml',
      },
    });
  }
}

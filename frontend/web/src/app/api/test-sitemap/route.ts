import { NextResponse } from 'next/server'

export async function GET() {
  try {
    console.log('🧪 Testing sitemap API access...')
    
    const apiUrls = [
      'https://prompthub.xin/api/prompts/all-public-ids',
      'http://127.0.0.1:4000/api/prompts/all-public-ids',
      'http://localhost:4000/api/prompts/all-public-ids'
    ]

    const results = []
    
    for (const apiUrl of apiUrls) {
      try {
        console.log(`🔍 Testing: ${apiUrl}`)
        const response = await fetch(apiUrl, {
          headers: {
            'User-Agent': 'PromptHub-Test'
          },
          cache: 'no-store'
        })
        
        const result = {
          url: apiUrl,
          status: response.status,
          ok: response.ok,
          data: null,
          error: null
        }
        
        if (response.ok) {
          const data = await response.json()
          result.data = {
            success: data.success,
            count: data.data?.length || 0
          }
          console.log(`✅ ${apiUrl}: ${data.data?.length || 0} prompts`)
        } else {
          result.error = `HTTP ${response.status}`
          console.log(`❌ ${apiUrl}: HTTP ${response.status}`)
        }
        
        results.push(result)
      } catch (error) {
        console.error(`💥 ${apiUrl}:`, error)
        results.push({
          url: apiUrl,
          status: 0,
          ok: false,
          data: null,
          error: error.message
        })
      }
    }
    
    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      results
    })
  } catch (error) {
    console.error('💥 Test failed:', error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}

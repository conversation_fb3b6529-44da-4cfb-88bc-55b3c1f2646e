import { Metadata } from 'next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Shield, Lock, Eye, Database, Users, Globe, AlertCircle, CheckCircle } from 'lucide-react'

export const metadata: Metadata = {
  title: '隐私政策 - PromptHub AI提示词平台',
  description: 'PromptHub隐私政策详细说明我们如何收集、使用和保护您的个人信息，确保用户数据安全和隐私权益。',
  keywords: [
    '隐私政策', '数据保护', '用户隐私', 'PromptHub隐私',
    '信息安全', '数据安全', '隐私保护', '用户协议',
    '个人信息保护', '数据使用规则', '安全可信', 'GDPR合规'
  ],
  openGraph: {
    title: '隐私政策 - PromptHub AI提示词平台',
    description: 'PromptHub隐私政策详细说明我们如何收集、使用和保护您的个人信息。',
    type: 'website',
  },
}

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50">
      {/* Hero Section */}
      <section className="px-4 py-20 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <div className="mb-8 flex justify-center">
            <div className="w-20 h-20 bg-gradient-to-br from-violet-600 to-cyan-600 rounded-2xl flex items-center justify-center">
              <Shield className="w-12 h-12 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold mb-6 bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent">
            隐私政策
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
            PromptHub 非常重视您的隐私权益。本隐私政策详细说明了我们如何收集、使用和保护您的个人信息。
          </p>
          <div className="mt-6 text-sm text-gray-500">
            最后更新时间：2024年12月
          </div>
        </div>
      </section>

      {/* 重要提示 */}
      <section className="px-4 py-8 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-6 h-6 text-blue-600 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">重要提示</h3>
                <p className="text-blue-800 text-sm leading-relaxed">
                  请仔细阅读本隐私政策。当您使用 PromptHub 服务时，即表示您已经理解并同意本隐私政策的所有条款。
                  如果您不同意本政策的任何内容，请停止使用我们的服务。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 政策内容 */}
      <section className="px-4 py-8 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl space-y-8">
          
          {/* 信息收集 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                  <Database className="w-5 h-5 text-white" />
                </div>
                <CardTitle className="text-xl text-gray-900">1. 信息收集</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">1.1 个人信息</h4>
                <p className="text-gray-600 leading-relaxed">
                  我们可能收集以下个人信息：
                </p>
                <ul className="mt-2 space-y-1 text-gray-600 text-sm ml-4">
                  <li>• 账户信息：用户名、邮箱地址、密码（加密存储）</li>
                  <li>• 个人资料：头像、个人简介、社交媒体链接</li>
                  <li>• 联系信息：您主动提供的联系方式</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">1.2 使用数据</h4>
                <p className="text-gray-600 leading-relaxed">
                  为了提供更好的服务，我们会收集：
                </p>
                <ul className="mt-2 space-y-1 text-gray-600 text-sm ml-4">
                  <li>• 您创建、编辑、分享的提示词内容</li>
                  <li>• 您的浏览记录、搜索历史、使用偏好</li>
                  <li>• 设备信息、IP地址、浏览器类型</li>
                  <li>• 与其他用户的互动记录（点赞、评论、收藏）</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">1.3 技术信息</h4>
                <ul className="space-y-1 text-gray-600 text-sm ml-4">
                  <li>• Cookies 和本地存储数据</li>
                  <li>• 访问日志和错误报告</li>
                  <li>• 网站分析数据</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* 信息使用 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-violet-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <Eye className="w-5 h-5 text-white" />
                </div>
                <CardTitle className="text-xl text-gray-900">2. 信息使用</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 leading-relaxed">
                我们使用收集的信息用于以下目的：
              </p>
              
              <div className="grid gap-4 md:grid-cols-2">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    服务提供
                  </h4>
                  <ul className="space-y-1 text-gray-600 text-sm">
                    <li>• 创建和管理用户账户</li>
                    <li>• 提供提示词平台服务</li>
                    <li>• 处理用户请求和反馈</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    服务优化
                  </h4>
                  <ul className="space-y-1 text-gray-600 text-sm">
                    <li>• 个性化内容推荐</li>
                    <li>• 改进产品功能</li>
                    <li>• 分析使用趋势</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    安全保障
                  </h4>
                  <ul className="space-y-1 text-gray-600 text-sm">
                    <li>• 防止滥用和欺诈</li>
                    <li>• 维护平台安全</li>
                    <li>• 遵守法律法规</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    沟通联系
                  </h4>
                  <ul className="space-y-1 text-gray-600 text-sm">
                    <li>• 发送重要通知</li>
                    <li>• 客户服务支持</li>
                    <li>• 营销信息（可选）</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 信息共享 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-white" />
                </div>
                <CardTitle className="text-xl text-gray-900">3. 信息共享</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 leading-relaxed">
                我们承诺不会出售、出租或以其他方式商业化您的个人信息。在以下情况下，我们可能会共享您的信息：
              </p>
              
              <div className="space-y-3">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold text-gray-900">公开内容</h4>
                  <p className="text-gray-600 text-sm">您选择公开分享的提示词和个人资料信息将对其他用户可见</p>
                </div>
                
                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-semibold text-gray-900">服务提供商</h4>
                  <p className="text-gray-600 text-sm">与可信的第三方服务提供商共享必要信息以提供技术支持</p>
                </div>
                
                <div className="border-l-4 border-red-500 pl-4">
                  <h4 className="font-semibold text-gray-900">法律要求</h4>
                  <p className="text-gray-600 text-sm">在法律要求或为保护用户安全的情况下可能披露信息</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 数据安全 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-500 rounded-lg flex items-center justify-center">
                  <Lock className="w-5 h-5 text-white" />
                </div>
                <CardTitle className="text-xl text-gray-900">4. 数据安全</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 leading-relaxed">
                我们采用多重安全措施保护您的信息：
              </p>
              
              <div className="grid gap-4 md:grid-cols-2">
                {[
                  { title: "数据加密", desc: "使用 HTTPS 和数据库加密技术" },
                  { title: "访问控制", desc: "严格限制数据访问权限" },
                  { title: "安全监控", desc: "24/7 安全监控和威胁检测" },
                  { title: "定期审计", desc: "定期进行安全评估和更新" }
                ].map((item, index) => (
                  <div key={index} className="bg-green-50 rounded-lg p-4">
                    <h4 className="font-semibold text-green-900 mb-1">{item.title}</h4>
                    <p className="text-green-700 text-sm">{item.desc}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 用户权利 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <Globe className="w-5 h-5 text-white" />
                </div>
                <CardTitle className="text-xl text-gray-900">5. 您的权利</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 leading-relaxed">
                您对自己的个人信息享有以下权利：
              </p>
              
              <div className="space-y-3">
                {[
                  { title: "访问权", desc: "查看我们保存的您的个人信息" },
                  { title: "更正权", desc: "更新或修正不准确的个人信息" },
                  { title: "删除权", desc: "要求删除您的个人信息" },
                  { title: "数据携带权", desc: "获取您的数据副本" },
                  { title: "限制处理权", desc: "限制我们对您信息的使用" },
                  { title: "反对权", desc: "反对特定的数据处理活动" }
                ].map((right, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-purple-600 flex-shrink-0 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-gray-900">{right.title}</h4>
                      <p className="text-gray-600 text-sm">{right.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="bg-blue-50 rounded-lg p-4 mt-4">
                <p className="text-blue-800 text-sm">
                  如需行使上述权利，请通过 <EMAIL> 联系我们。
                  我们将在30天内回复您的请求。
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Cookies */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl text-gray-900">6. Cookies 使用</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 leading-relaxed">
                我们使用 Cookies 和类似技术来：
              </p>
              <ul className="space-y-1 text-gray-600 text-sm ml-4">
                <li>• 记住您的登录状态</li>
                <li>• 保存您的偏好设置</li>
                <li>• 分析网站使用情况</li>
                <li>• 提供个性化体验</li>
              </ul>
              <p className="text-gray-600 text-sm">
                您可以通过浏览器设置管理 Cookies，但这可能会影响网站的正常功能。
              </p>
            </CardContent>
          </Card>

          {/* 政策更新 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl text-gray-900">7. 政策更新</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 leading-relaxed">
                我们可能会不时更新本隐私政策。重大变更将通过以下方式通知您：
              </p>
              <ul className="space-y-1 text-gray-600 text-sm ml-4">
                <li>• 在网站显著位置发布通知</li>
                <li>• 通过邮件发送更新通知</li>
                <li>• 在用户登录时显示提醒</li>
              </ul>
              <p className="text-gray-600 text-sm">
                继续使用我们的服务即表示您接受更新后的隐私政策。
              </p>
            </CardContent>
          </Card>

          {/* 联系方式 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl text-gray-900">8. 联系我们</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 leading-relaxed mb-4">
                如果您对本隐私政策有任何疑问或建议，请通过以下方式联系我们：
              </p>
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <p className="text-gray-700"><strong>邮箱：</strong> <EMAIL></p>
                <p className="text-gray-700"><strong>网站：</strong> prompthub.xin</p>
                <p className="text-gray-700"><strong>地址：</strong> 中国</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  )
} 
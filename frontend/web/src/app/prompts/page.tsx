'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchPara<PERSON>, useRouter, usePathname } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  Filter,
  TrendingUp,
  Heart,
  Download,
  Eye,
  Sparkles,
  ArrowUp,
  X
} from "lucide-react"
import { useAuth } from '@/hooks/useAuth'
import { api } from '@/lib/api'
import { PromptCard } from '@/components/prompt/PromptCard'
import { getCategoryDisplayName } from '@/lib/category-utils'
import { PromptsLibrarySEO } from '@/components/seo/HiddenSEOContent'
import toast from 'react-hot-toast'

interface Prompt {
  id: number
  title: string
  description: string
  content: string
  category: string
  categoryDisplayName?: string // 新增字段接收后端返回的中文显示名称
  tags: string[]
  userId?: number
  likes: number
  downloads: number
  views: number
  createdAt: string
  updatedAt: string
  status?: string
  isPrivate?: boolean
  authorName?: string
}

interface Category {
  id: number
  name: string
  description: string
  icon: string
  color: string
  prompt_count?: number
  sort_order?: number
  isActive?: boolean
  value?: string
  label?: string
  displayName?: string
}

function PromptsContent() {
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [searchInput, setSearchInput] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy, setSortBy] = useState('newest')
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 6,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const searchParams = useSearchParams()
  const router = useRouter()
  const pathname = usePathname()
  const { user, isAuthenticated, loading: authLoading } = useAuth() as any

  // 更新 URL 参数的函数
  const updateURL = (newSearchQuery: string) => {
    const params = new URLSearchParams(searchParams.toString())
    if (newSearchQuery) {
      params.set('search', newSearchQuery)
    } else {
      params.delete('search')
    }
    const newURL = `${pathname}?${params.toString()}`
    router.replace(newURL)
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await fetch(`${api.categories.list}`)
      if (!response.ok) {
        throw new Error('获取分类失败')
      }
      const result = await response.json()
      if (result.success) {
        setCategories(result.data)
      }
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }

  useEffect(() => {
    // 检查URL中的搜索参数
    const urlSearchQuery = searchParams.get('search')
    if (urlSearchQuery) {
      setSearchQuery(urlSearchQuery)
      setSearchInput(urlSearchQuery) // 同时设置输入框的值
    }

    // 初始加载数据
    fetchCategories()
    fetchPrompts()
  }, [searchParams])

  const fetchPrompts = async (page = 1, append = false) => {
    try {
      if (!append) {
        setLoading(true)
      } else {
        setIsLoadingMore(true)
      }
      setError('')

      // 网页端完全依赖HttpOnly Cookie认证
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      // 构建查询参数
      const params = new URLSearchParams()
      params.append('page', page.toString())
      params.append('limit', '6')
      if (sortBy) {
        params.append('sortBy', sortBy)
      }

      // 优先使用 URL 中的搜索参数，如果没有则使用状态中的搜索参数
      const urlSearchQuery = searchParams.get('search')
      const currentSearchQuery = urlSearchQuery || searchQuery
      if (currentSearchQuery) {
        params.append('search', currentSearchQuery)
      }

      if (selectedCategory && selectedCategory !== 'all') {
        params.append('category', selectedCategory)
      }

      const url = `${api.prompts.public}?${params.toString()}`

      console.log('🔍 API调用:', url, '页码:', page, '排序方式:', sortBy)

      const response = await fetch(url, {
        headers,
        credentials: 'include' // 支持Cookie认证
      })

      if (!response.ok) {
        throw new Error('获取提示词失败')
      }

      const data = await response.json()

      if (data.success) {
        // 转换数据格式，统一字段名
        const formattedPrompts = data.data.map((prompt: any) => ({
          ...prompt,
          tags: Array.isArray(prompt.tags) ? prompt.tags : JSON.parse(prompt.tags || '[]'),
          likes: prompt.likes_count || prompt.likes || 0,
          downloads: prompt.usage_count || prompt.downloads || 0,
          views: prompt.views || 0,
          isLiked: prompt.isLiked || false // 保留点赞状态
        }))

        if (append) {
          setPrompts(prev => [...prev, ...formattedPrompts])
        } else {
          setPrompts(formattedPrompts)
        }

        setPagination(data.pagination)
        setCurrentPage(page)
      } else {
        throw new Error(data.message || '获取提示词失败')
      }

    } catch (err) {
      console.error('获取提示词失败:', err)
      setError(err instanceof Error ? err.message : '获取提示词失败')
      if (!append) {
        setPrompts([])
      }
    } finally {
      setLoading(false)
      setIsLoadingMore(false)
    }
  }

  // 当搜索条件、筛选条件或排序条件改变时，重新获取数据
  useEffect(() => {
    setCurrentPage(1)
    fetchPrompts(1, false)
  }, [searchQuery, selectedCategory, sortBy])

  // 监听用户登录状态变化，当用户登录后重新获取数据以获取点赞状态
  useEffect(() => {
    if (!authLoading && user) {
      // 用户刚登录，重新获取当前页面的数据以获取点赞状态
      fetchPrompts(1, false)
    }
  }, [authLoading, user])

  const handleUpdate = (updatedPrompt: Prompt) => {
    setPrompts(prevPrompts =>
      prevPrompts.map(p =>
        p.id === updatedPrompt.id ? updatedPrompt : p
      )
    )
  }

  // 处理搜索输入 - 按回车搜索
  const handleSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setSearchQuery(searchInput)
      updateURL(searchInput) // 更新 URL
    }
  }

  // 清除搜索内容
  const clearSearch = () => {
    setSearchInput('')
    setSearchQuery('')
    updateURL('') // 清除 URL 中的搜索参数
  }

  // 加载更多
  const loadMorePrompts = () => {
    if (pagination.hasNext && !isLoadingMore) {
      fetchPrompts(currentPage + 1, true)
    }
  }

  // 分类选项现在从数据库获取，而不是从当前显示的提示词中提取

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="w-12 h-12 text-red-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">加载失败</h3>
            <p className="text-gray-600 mb-6">{error}</p>
            <Button onClick={fetchPrompts} variant="outline">
              重试
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50">
      {/* 隐藏的SEO内容 */}
      <PromptsLibrarySEO />

      <div className="container mx-auto px-6 py-8">
        {/* 页面头部 */}
        <div className="mb-8">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              免费AI提示词库大全
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              10000+优质AI提示词，涵盖ChatGPT、Claude、Midjourney等主流AI工具，助您掌握提示词工程
            </p>
          </div>

          {/* 搜索和筛选 */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* 搜索框 */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <Input
                    placeholder="搜索提示词、标签或描述... (按回车搜索)"
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    onKeyPress={handleSearchKeyPress}
                    className="pl-10 pr-10 text-base"
                  />
                  {searchInput && (
                    <button
                      onClick={clearSearch}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 hover:text-gray-600 transition-colors"
                      aria-label="清除搜索"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  )}
                </div>
              </div>

              {/* 分类筛选 */}
              <div className="flex gap-4">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部分类</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category.id} value={category.name}>
                        {category.description || category.displayName || getCategoryDisplayName(category.name)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* 排序 */}
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="排序方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">最新上架</SelectItem>
                    <SelectItem value="popular">最受欢迎</SelectItem>
                    <SelectItem value="downloads">热门推荐</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* 统计信息 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2 text-gray-600">
              <Filter className="w-5 h-5" />
              <span>找到 {pagination.total} 个提示词</span>
              {prompts.length > 0 && prompts.length < pagination.total && (
                <span className="text-sm text-gray-500">
                  (已显示 {prompts.length} 个)
                </span>
              )}
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                <span>{prompts.reduce((sum, p) => sum + (p.likes || 0), 0)} 点赞</span>
              </div>
              <div className="flex items-center gap-1">
                <Download className="w-4 h-4" />
                <span>{prompts.reduce((sum, p) => sum + (p.downloads || 0), 0)} 下载</span>
              </div>
            </div>
          </div>
        </div>

        {/* 提示词列表 */}
        {prompts.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {searchQuery || selectedCategory !== 'all' ? '未找到匹配的提示词' : '暂无提示词'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchQuery || selectedCategory !== 'all'
                ? '尝试调整搜索条件或筛选器'
                : '目前还没有提示词，请稍后再来看看'
              }
            </p>
            {(searchQuery || selectedCategory !== 'all') && (
              <Button
                onClick={() => {
                  setSearchQuery('')
                  setSearchInput('')
                  setSelectedCategory('all')
                  updateURL('') // 清除 URL 中的搜索参数
                }}
                variant="outline"
              >
                清除筛选条件
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {prompts.map((prompt) => (
              <PromptCard
                key={prompt.id}
                prompt={prompt}
              />
            ))}
          </div>
        )}

        {/* 加载更多按钮 */}
        {prompts.length > 0 && pagination.hasNext && (
          <div className="flex justify-center mt-12">
            <Button
              onClick={loadMorePrompts}
              disabled={isLoadingMore}
              variant="outline"
              size="lg"
              className="flex items-center gap-2 px-8 py-3 text-violet-600 border-violet-200 hover:border-violet-300 hover:bg-violet-50"
            >
              {isLoadingMore ? (
                <>
                  <div className="w-5 h-5 border-2 border-violet-600 border-t-transparent rounded-full animate-spin"></div>
                  <span>加载中...</span>
                </>
              ) : (
                <>
                  <Sparkles className="w-5 h-5" />
                  <span>加载更多提示词</span>
                </>
              )}
            </Button>
          </div>
        )}

        {/* 已显示全部提示 */}
        {prompts.length > 0 && !pagination.hasNext && prompts.length > 6 && (
          <div className="text-center mt-12 py-8 text-gray-500">
            <Sparkles className="w-8 h-8 mx-auto mb-3 opacity-50" />
            <p className="text-lg">已显示全部 {prompts.length} 个提示词</p>
            <p className="text-sm mt-1">感谢您的浏览！</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default function PromptsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    }>
      <PromptsContent />
    </Suspense>
  )
} 
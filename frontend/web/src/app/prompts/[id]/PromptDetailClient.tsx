'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Breadcrumb, generateBreadcrumbJsonLd } from "@/components/ui/breadcrumb"
import {
  Star,
  Download,
  Heart,
  Eye,
  User,
  Calendar,
  Copy,
  Plus,
  ArrowLeft,
  ExternalLink
} from "lucide-react"
import { useAuth } from '@/hooks/useAuth'
import toast from 'react-hot-toast'
import { api, API_BASE_URL } from '@/lib/api'
import { getCategoryDisplayName } from '@/lib/category-utils'

interface Prompt {
  id: number
  title: string
  description: string
  category: string
  categoryDisplayName?: string
  tags: string[]
  content: string
  userId: number
  likes: number
  downloads: number
  views: number
  createdAt: string
  updatedAt: string
  status: string
  isPrivate: boolean
  authorName?: string
  isLiked?: boolean
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return '今天'
  if (diffDays === 2) return '昨天'
  if (diffDays <= 7) return `${diffDays}天前`
  if (diffDays <= 30) return `${Math.ceil(diffDays / 7)}周前`
  return `${Math.ceil(diffDays / 30)}个月前`
}

interface PromptDetailClientProps {
  initialPrompt: Prompt
}

export default function PromptDetailClient({ initialPrompt }: PromptDetailClientProps) {
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const [prompt, setPrompt] = useState(initialPrompt)
  const [isLiked, setIsLiked] = useState(initialPrompt.isLiked)
  const [likes, setLikes] = useState(initialPrompt.likes)
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    // 当 initialPrompt 更新时，同步状态
    setPrompt(initialPrompt)
    setIsLiked(initialPrompt.isLiked)
    setLikes(initialPrompt.likes)
  }, [initialPrompt])

  // 获取最新的提示词数据（包括点赞状态）
  const fetchLatestPromptData = async () => {
    if (!user) return // 未登录用户不需要获取点赞状态

    try {
      // 网页端完全依赖HttpOnly Cookie认证
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      const response = await fetch(`${API_BASE_URL}/prompts/${initialPrompt.id}`, {
        headers,
        credentials: 'include' // 支持Cookie认证
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // 更新点赞状态和统计数据
          setPrompt(data.data)
          setIsLiked(data.data.isLiked || false)
          setLikes(data.data.likes || 0)
        }
      }
    } catch (error) {
      console.warn('获取最新提示词数据失败:', error)
    }
  }

  // 在用户登录状态确定后获取最新数据
  useEffect(() => {
    if (!authLoading && user) {
      fetchLatestPromptData()
    }
  }, [authLoading, user, initialPrompt.id])

  const handleLike = async () => {
    if (!user || authLoading) return
    if (isProcessing) return

    try {
      setIsProcessing(true)

      // 网页端使用 HttpOnly Cookie 认证，不需要 token 处理
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      // 调用后端API点赞/取消点赞
      const response = await fetch(`${API_BASE_URL}/prompts/${prompt?.id}/like`, {
        method: 'POST',
        headers,
        credentials: 'include' // 支持Cookie认证
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // 使用后端返回的准确状态
          setIsLiked(data.data.isLiked)
          setLikes(data.data.likesCount)
          toast.success(data.data.isLiked ? '❤️ 点赞成功' : '💔 取消点赞')
        } else {
          throw new Error(data.message || '点赞失败')
        }
      } else {
        throw new Error('点赞请求失败')
      }
    } catch (error) {
      console.error('❌ 点赞操作异常:', {
        error: error.message,
        stack: error.stack,
        user: !!user,
        promptId: prompt?.id
      })
      toast.error('点赞失败，请重试')
    } finally {
      setIsProcessing(false)
      console.log('🏁 点赞操作完成')
    }
  }

  const handleCopy = async () => {
    if (!prompt) return
    if (isProcessing) return

    try {
      setIsProcessing(true)

      // 复制到剪贴板
      await navigator.clipboard.writeText(prompt.content)

      // 调用后端API增加复制次数（所有用户都计数）
      try {
        const headers: Record<string, string> = {
          'Content-Type': 'application/json'
        }

        // 网页端使用 HttpOnly Cookie 认证，不需要额外的认证头

        const response = await fetch(`${API_BASE_URL}/prompts/${prompt.id}/copy`, {
          method: 'POST',
          credentials: 'include', // 支持Cookie认证
          headers
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success && result.data) {
            // 更新本地显示的复制次数
            setPrompt(prev => prev ? { ...prev, downloads: result.data.downloads } : prev)
          }
        }
      } catch (copyError) {
        console.warn('更新复制计数失败:', copyError)
      }

      toast.success('📋 提示词已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
      toast.error('复制失败，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleCopyToMyPrompts = async () => {
    if (!prompt) return
    if (!user) {
      toast.error('请先登录')
      return
    }
    if (isProcessing) return
    
    try {
      setIsProcessing(true)
      
      // 网页端使用 HttpOnly Cookie 认证，不需要 token 处理
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      const response = await fetch(api.prompts.create, {
        method: 'POST',
        headers,
        credentials: 'include', // 支持Cookie认证
        body: JSON.stringify({
          title: `${prompt.title} (副本)`,
          description: prompt.description,
          content: prompt.content,
          category: prompt.category,
          tags: prompt.tags,
          isPrivate: true
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '收藏失败')
      }

      const result = await response.json()

      if (result.success) {
        toast.success('✨ 成功收藏到"我的提示词"')
        router.push('/my-prompts')
      } else {
        throw new Error(result.message || '收藏失败')
      }

    } catch (error) {
      console.error('收藏失败:', error)
      toast.error(error instanceof Error ? error.message : '收藏失败，请稍后再试')
    } finally {
      setIsProcessing(false)
    }
  }

  // AI平台跳转功能
  const aiPlatforms = [
    {
      name: 'ChatGPT',
      url: 'https://chat.openai.com/',
      color: 'bg-green-500 hover:bg-green-600',
      icon: '🤖'
    },
    {
      name: 'Claude',
      url: 'https://claude.ai/',
      color: 'bg-orange-500 hover:bg-orange-600',
      icon: '🧠'
    },
    {
      name: 'Gemini',
      url: 'https://gemini.google.com/',
      color: 'bg-blue-500 hover:bg-blue-600',
      icon: '💎'
    },
    {
      name: 'DeepSeek',
      url: 'https://chat.deepseek.com/',
      color: 'bg-purple-500 hover:bg-purple-600',
      icon: '🔮'
    },
    {
      name: '文心一言',
      url: 'https://yiyan.baidu.com/',
      color: 'bg-red-500 hover:bg-red-600',
      icon: '📝'
    },
    {
      name: '通义千问',
      url: 'https://tongyi.aliyun.com/',
      color: 'bg-indigo-500 hover:bg-indigo-600',
      icon: '💭'
    },
    {
      name: 'Kimi',
      url: 'https://kimi.moonshot.cn/',
      color: 'bg-cyan-500 hover:bg-cyan-600',
      icon: '🌙'
    },
    {
      name: '豆包',
      url: 'https://www.doubao.com/',
      color: 'bg-yellow-500 hover:bg-yellow-600',
      icon: '🫘'
    },
    {
      name: '智谱清言',
      url: 'https://chatglm.cn/',
      color: 'bg-teal-500 hover:bg-teal-600',
      icon: '🎯'
    },
    {
      name: 'Copilot',
      url: 'https://copilot.microsoft.com/',
      color: 'bg-blue-600 hover:bg-blue-700',
      icon: '🚁'
    },
    {
      name: 'Perplexity',
      url: 'https://www.perplexity.ai/',
      color: 'bg-gray-700 hover:bg-gray-800',
      icon: '🔍'
    },
    {
      name: '讯飞星火',
      url: 'https://xinghuo.xfyun.cn/',
      color: 'bg-pink-500 hover:bg-pink-600',
      icon: '✨'
    }
  ]

  const handleJumpToPlatform = async (platform: typeof aiPlatforms[0]) => {
    try {
      // 复制提示词内容到剪贴板
      await navigator.clipboard.writeText(prompt.content)

      // 记录复制次数
      try {
        await fetch(`${API_BASE_URL}/prompts/${prompt.id}/copy`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include'
        })
      } catch (error) {
        console.warn('记录复制次数失败:', error)
      }

      // 打开AI平台
      window.open(platform.url, '_blank')

      toast.success(`✨ 提示词已复制，正在跳转到 ${platform.name}`)
    } catch (error) {
      console.error('复制失败:', error)
      toast.error('复制失败，请手动复制提示词内容')
      // 即使复制失败也打开平台
      window.open(platform.url, '_blank')
    }
  }

  const categoryDisplayName = getCategoryDisplayName(prompt.category)

  // 生成面包屑导航数据
  const breadcrumbItems = [
    { label: '提示词库', href: '/prompts' },
    { label: categoryDisplayName, href: `/prompts?category=${prompt.category}` },
    { label: prompt.title, current: true }
  ];

  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://www.prompthub.xin';
  const breadcrumbJsonLd = generateBreadcrumbJsonLd(breadcrumbItems, baseUrl);

  // 生成JSON-LD结构化数据
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": prompt.title,
    "description": prompt.description || `${prompt.title} - 优质AI提示词`,
    "author": {
      "@type": "Person",
      "name": prompt.authorName || "PromptHub用户"
    },
    "publisher": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": process.env.NEXT_PUBLIC_APP_URL || "https://www.prompthub.xin"
    },
    "datePublished": prompt.createdAt,
    "dateModified": prompt.updatedAt,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${process.env.NEXT_PUBLIC_APP_URL || "https://www.prompthub.xin"}/prompts/${prompt.id}`
    },
    "articleSection": prompt.category || "AI提示词",
    "keywords": prompt.tags?.join(", ") || "",
    "inLanguage": "zh-CN",
    "isAccessibleForFree": true,
    "creativeWorkStatus": "Published",
    "genre": "AI提示词",
    "audience": {
      "@type": "Audience",
      "audienceType": "AI爱好者"
    },
    "interactionStatistic": [
      {
        "@type": "InteractionCounter",
        "interactionType": "https://schema.org/LikeAction",
        "userInteractionCount": likes
      },
      {
        "@type": "InteractionCounter",
        "interactionType": "https://schema.org/ViewAction",
        "userInteractionCount": prompt.views || 0
      }
    ]
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50">
      {/* JSON-LD 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      {/* 面包屑导航 JSON-LD */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbJsonLd) }}
      />

      <div className="container mx-auto px-6 py-8">
        {/* 面包屑导航 */}
        <Breadcrumb items={breadcrumbItems} className="mb-4" />

        {/* 返回按钮 */}
        <Button
          onClick={() => {
            // 检查浏览器历史记录长度来判断是否是新标签页
            // 新标签页的 history.length 通常为 1
            if (window.history.length > 1) {
              router.back()
            } else {
              router.push('/prompts')
            }
          }}
          variant="outline"
          className="mb-6 hover:bg-violet-50 hover:border-violet-200"
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> 返回
        </Button>

        {/* 主要内容区域 - 左右布局 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：标题和提示词内容 */}
          <div className="lg:col-span-2">
            <Card className="border border-gray-200/60 bg-white backdrop-blur-sm shadow-lg">
              <CardHeader className="pb-4">
                {/* 第一行：分类 + 浏览量 */}
                <div className="flex items-center justify-between mb-4">
                  <Badge
                    variant="secondary"
                    className="text-sm bg-violet-100 text-violet-700 hover:bg-violet-200"
                  >
                    {categoryDisplayName}
                  </Badge>
                  <div className="flex items-center gap-1 text-sm text-gray-500">
                    <Eye className="w-4 h-4" />
                    <span>{prompt.views || 0}</span>
                  </div>
                </div>

                {/* 第二行：标题 */}
                <CardTitle className="text-3xl font-bold text-gray-900 mb-4">
                  {prompt.title}
                </CardTitle>

                {/* 第三行：描述 */}
                {prompt.description && (
                  <CardDescription className="text-lg text-gray-600 mb-4">
                    {prompt.description}
                  </CardDescription>
                )}

                {/* 第四行：用户信息 */}
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <div className="w-6 h-6 bg-gradient-to-br from-violet-500 to-cyan-500 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-white" />
                  </div>
                  <span className="font-medium">{prompt.authorName || '匿名创作者'}</span>
                  <span className="text-gray-400">•</span>
                  <span className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {formatDate(prompt.createdAt)}
                  </span>
                </div>
              </CardHeader>

              <CardContent className="p-6">
                {/* 提示词内容区域 */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      提示词内容
                    </h3>
                  </div>
                  <div className="bg-white rounded-xl p-6 border border-blue-200 shadow-sm">
                    <div className="prose prose-lg max-w-none">
                      <pre className="whitespace-pre-wrap font-mono text-sm leading-7 text-gray-900 bg-gray-50 rounded-lg p-4 border-l-4 border-blue-400 overflow-x-auto">
{prompt.content}
                      </pre>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：标签、统计和操作 */}
          <div className="lg:col-span-1 space-y-3">

            {/* 标签区域 */}
            {prompt.tags && prompt.tags.length > 0 && (
              <Card className="border border-gray-200/60 bg-white backdrop-blur-sm shadow-lg">
                <CardContent className="p-4">
                  <h3 className="text-base font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    相关标签
                  </h3>
                  <div className="flex flex-wrap gap-1.5">
                    {prompt.tags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="text-xs px-2 py-0.5 text-gray-700 border-gray-300 bg-white hover:bg-gray-50 transition-colors"
                      >
                        #{tag}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 统计信息 */}
            <Card className="border border-gray-200/60 bg-white backdrop-blur-sm shadow-lg">
              <CardContent className="p-4">
                <h3 className="text-base font-semibold text-gray-800 mb-3 flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  数据统计
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-2 bg-red-50 rounded-lg border border-red-100">
                    <div className="flex items-center gap-2">
                      <Heart className={`w-4 h-4 ${isLiked ? 'fill-current text-red-500' : 'text-red-400'}`} />
                      <span className="text-xs text-gray-600 font-medium">点赞数</span>
                    </div>
                    <span className="text-lg font-bold text-gray-900">{likes}</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-blue-50 rounded-lg border border-blue-100">
                    <div className="flex items-center gap-2">
                      <Copy className="w-4 h-4 text-blue-400" />
                      <span className="text-xs text-gray-600 font-medium">复制次数</span>
                    </div>
                    <span className="text-lg font-bold text-gray-900">{prompt.downloads || 0}</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-100">
                    <div className="flex items-center gap-2">
                      <Eye className="w-4 h-4 text-green-400" />
                      <span className="text-xs text-gray-600 font-medium">浏览次数</span>
                    </div>
                    <span className="text-lg font-bold text-gray-900">{prompt.views || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 操作按钮区域 */}
            <Card className="border border-gray-200/60 bg-white backdrop-blur-sm shadow-lg">
              <CardContent className="p-4">
                <h3 className="text-base font-semibold text-gray-800 mb-3 flex items-center gap-2">
                  <div className="w-2 h-2 bg-violet-500 rounded-full"></div>
                  快速操作
                </h3>
                {authLoading ? (
                  // 认证状态加载中
                  <div className="space-y-2">
                    <div className="w-full h-10 bg-gray-100 rounded-md animate-pulse"></div>
                    <div className="w-full h-10 bg-gray-100 rounded-md animate-pulse"></div>
                    <div className="w-full h-10 bg-gray-100 rounded-md animate-pulse"></div>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {/* 点赞按钮 */}
                    <Button
                      size="default"
                      variant="outline"
                      onClick={handleLike}
                      disabled={isProcessing || !user}
                      className={`w-full flex items-center justify-center gap-2 h-10 transition-all duration-200 border-2 ${
                        isLiked
                          ? 'text-red-600 bg-red-50 border-red-200 hover:bg-red-100'
                          : 'text-gray-600 border-gray-200 hover:text-red-600 hover:bg-red-50 hover:border-red-200'
                      }`}
                      title={!user ? '请先登录' : '点赞'}
                    >
                      <Heart className={`w-4 h-4 transition-all duration-200 ${
                        isLiked ? 'fill-current text-red-600' : 'text-red-600'
                      }`} />
                      <span className="font-medium text-sm">{isLiked ? '已点赞' : '点赞'}</span>
                    </Button>

                    {/* 复制按钮 */}
                    <Button
                      size="default"
                      variant="outline"
                      onClick={handleCopy}
                      disabled={isProcessing}
                      className="w-full flex items-center justify-center gap-2 h-10 text-blue-600 border-2 border-blue-200 hover:bg-blue-50 hover:border-blue-300 transition-all duration-200"
                      title="复制提示词内容"
                    >
                      <Copy className="w-4 h-4 text-blue-600" />
                      <span className="font-medium text-sm">复制内容</span>
                    </Button>

                    {/* 收藏按钮 */}
                    {user && (
                      <Button
                        size="default"
                        onClick={handleCopyToMyPrompts}
                        disabled={isProcessing}
                        className="w-full flex items-center justify-center gap-2 h-10 bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white border-0 transition-all duration-200 shadow-lg hover:shadow-xl font-medium"
                        title="复制到我的提示词"
                      >
                        <Plus className="w-4 h-4" />
                        <span className="text-sm">{isProcessing ? '复制中...' : '复制到我的提示词'}</span>
                      </Button>
                    )}
                  </div>
                )}
                {!authLoading && !user && (
                  <p className="text-xs text-gray-500 mt-2 text-center">
                    登录后可以点赞和收藏提示词
                  </p>
                )}
              </CardContent>
            </Card>

            {/* AI平台跳转区域 */}
            <Card className="border border-gray-200/60 bg-white backdrop-blur-sm shadow-lg">
              <CardContent className="p-4">
                <h3 className="text-base font-semibold text-gray-800 mb-3 flex items-center gap-2">
                  <div className="w-2 h-2 bg-cyan-500 rounded-full"></div>
                  一键跳转AI平台
                </h3>
                <div className="grid grid-cols-2 gap-2">
                  {aiPlatforms.map((platform, index) => (
                    <Button
                      key={index}
                      size="sm"
                      onClick={() => handleJumpToPlatform(platform)}
                      className={`${platform.color} text-white border-0 transition-all duration-200 shadow-md hover:shadow-lg font-medium text-xs h-8 flex items-center justify-center gap-1`}
                      title={`复制提示词并跳转到 ${platform.name}`}
                    >
                      <span className="text-xs">{platform.icon}</span>
                      <span className="truncate">{platform.name}</span>
                      <ExternalLink className="w-2.5 h-2.5 flex-shrink-0" />
                    </Button>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-2 text-center">
                  点击按钮将自动复制提示词并跳转到对应平台
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { cookies } from 'next/headers'
import { API_BASE_URL } from '@/lib/api'
import PromptDetailClient from './PromptDetailClient'

// 定义Props类型
interface PageProps {
  params: Promise<{ id: string }>
}

// 定义Prompt数据类型
interface Prompt {
  id: number
  title: string
  description: string
  category: string
  tags: string[]
  content: string
  userId: number
  likes: number
  downloads: number
  views: number
  createdAt: string
  updatedAt: string
  status: string
  isPrivate: boolean
  authorName?: string
  isLiked?: boolean
}

// 服务端数据获取逻辑
async function getPromptData(id: string): Promise<Prompt | null> {
  try {
    const cookieStore = await cookies();
    const authToken = cookieStore.get('prompthub_token');

    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    // 如果有认证 Cookie，添加到请求头中
    if (authToken) {
      headers['Cookie'] = `prompthub_token=${authToken.value}`;
    }

    console.log('🔗 Fetching prompt from:', `${API_BASE_URL}/prompts/${id}`);
    console.log('🍪 Auth cookie present:', !!authToken);

    const response = await fetch(`${API_BASE_URL}/prompts/${id}`, {
      headers,
      cache: 'no-store' // 禁用缓存以获取最新数据
    });

    if (!response.ok) {
      console.error('API response not ok:', response.status, response.statusText);
      return null;
    }

    const data = await response.json();
    if (data.success) {
      // 确保tags是数组
      const promptData = {
        ...data.data,
        tags: Array.isArray(data.data.tags) ? data.data.tags : JSON.parse(data.data.tags || '[]')
      };
      return promptData;
    }
    return null;
  } catch (error) {
    console.error(`Failed to fetch prompt ${id}:`, error);
    return null;
  }
}

// 生成动态metadata
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const prompt = await getPromptData(resolvedParams.id);

  if (!prompt) {
    return {
      title: '提示词不存在 - PromptHub',
      description: '您要查找的提示词不存在或已被删除。',
    };
  }

  // 截取描述，确保不超过160字符
  const description = prompt.description
    ? prompt.description.slice(0, 150) + (prompt.description.length > 150 ? '...' : '')
    : `${prompt.title} - 优质AI提示词，适用于ChatGPT、Claude等AI模型。`;

  // 生成关键词
  const keywords = [
    // 基础信息
    prompt.title,
    prompt.category,
    ...prompt.tags,
    
    // 固定关键词
    'AI提示词', 'ChatGPT', 'Prompt', 'PromptHub',
    
    // 应用场景关键词
    `${prompt.category}提示词`,
    `${prompt.category}Prompt`,
    
    // 功能性关键词
    '免费下载', '一键复制', 'AI指令',
    
    // 质量相关
    '优质提示词', '精选Prompt',
    
    // 热门AI工具
    'Claude', 'GPT-4', 'AI助手'
  ].filter(Boolean);

  return {
    title: `${prompt.title} - PromptHub AI提示词库`,
    description,
    keywords: keywords.slice(0, 10), // 限制关键词数量
    openGraph: {
      title: `${prompt.title} - PromptHub`,
      description,
      type: 'article',
      publishedTime: prompt.createdAt,
      modifiedTime: prompt.updatedAt,
      authors: prompt.authorName ? [prompt.authorName] : undefined,
      tags: prompt.tags,
    },
    twitter: {
      card: 'summary',
      title: `${prompt.title} - PromptHub`,
      description,
    },
    alternates: {
      canonical: `https://prompthub.xin/prompts/${prompt.id}`,
    },
  };
}

// 页面组件本身 (服务端组件)
export default async function PromptDetailPage({ params }: PageProps) {
  const resolvedParams = await params;
  const prompt = await getPromptData(resolvedParams.id);

  if (!prompt) {
    notFound();
  }

  // 渲染客户端组件，并传入服务端获取的数据
  return <PromptDetailClient initialPrompt={prompt} />;
}
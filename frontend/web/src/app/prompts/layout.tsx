import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'AI提示词库 - PromptHub优质提示词分享平台',
  description: '浏览PromptHub精选的AI提示词库，包含ChatGPT、Midjourney、Claude等AI模型的优质提示词，涵盖写作、绘画、编程、商业等多个领域。',
  keywords: [
    // 核心库概念
    'AI提示词库', 'Prompt库', '提示词大全', '免费提示词',
    
    // AI工具相关
    'ChatGPT提示词', 'Claude提示词', 'Midjourney提示词', 'GPT-4提示词',
    
    // 分类相关
    'AI写作提示词', 'AI绘画提示词', '编程提示词', '营销提示词',
    '创意写作Prompt', '数据分析提示词', '教育学习Prompt',
    
    // 质量相关
    '优质提示词', '精选Prompt', '最佳AI指令', '高效提示词'
  ],
  openGraph: {
    title: 'AI提示词库 - PromptHub优质提示词分享平台',
    description: '浏览PromptHub精选的AI提示词库，包含ChatGPT、Midjourney、Claude等AI模型的优质提示词。',
    type: 'website',
  },
}

export default function PromptsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}

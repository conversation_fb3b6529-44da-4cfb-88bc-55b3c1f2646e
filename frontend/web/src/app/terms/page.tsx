import { Metadata } from 'next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FileText, Scale, Users, AlertTriangle, CheckCircle, XCircle, UserCheck, Shield } from 'lucide-react'

export const metadata: Metadata = {
  title: '服务条款 - PromptHub AI提示词平台',
  description: 'PromptHub服务条款详细说明用户权利义务、平台使用规则、内容版权等重要条款，保障用户和平台的合法权益。',
  keywords: [
    '服务条款', '使用协议', '用户协议', 'PromptHub条款',
    '平台规则', '用户权利', '法律条款', '服务规范',
    '版权保护', '内容规则', '免责声明', '合规使用'
  ],
  openGraph: {
    title: '服务条款 - PromptHub AI提示词平台',
    description: 'PromptHub服务条款详细说明用户权利义务、平台使用规则、内容版权等重要条款。',
    type: 'website',
  },
}

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50">
      {/* Hero Section */}
      <section className="px-4 py-20 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <div className="mb-8 flex justify-center">
            <div className="w-20 h-20 bg-gradient-to-br from-violet-600 to-cyan-600 rounded-2xl flex items-center justify-center">
              <FileText className="w-12 h-12 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold mb-6 bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent">
            使用条款
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
            欢迎使用 PromptHub！在使用我们的服务之前，请仔细阅读并理解以下使用条款。
          </p>
          <div className="mt-6 text-sm text-gray-500">
            最后更新时间：2024年12月
          </div>
        </div>
      </section>

      {/* 重要提示 */}
      <section className="px-4 py-8 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl">
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-6 mb-8">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-6 h-6 text-amber-600 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="font-semibold text-amber-900 mb-2">重要协议</h3>
                <p className="text-amber-800 text-sm leading-relaxed">
                  通过注册、访问或使用 PromptHub 服务，您表示已阅读、理解并同意遵守本使用条款。
                  如果您不同意这些条款，请不要使用我们的服务。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 条款内容 */}
      <section className="px-4 py-8 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl space-y-8">
          
          {/* 服务描述 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-white" />
                </div>
                <CardTitle className="text-xl text-gray-900">1. 服务描述</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 leading-relaxed">
                PromptHub 是一个AI提示词分享平台，为用户提供创建、分享和发现优质提示词的服务。
              </p>
              
              <div className="grid gap-4 md:grid-cols-2">
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-900 mb-2">📝 内容创作</h4>
                  <ul className="space-y-1 text-blue-800 text-sm">
                    <li>• 创建和编辑提示词</li>
                    <li>• 分类管理和标签系统</li>
                    <li>• 版本控制和历史记录</li>
                  </ul>
                </div>
                
                <div className="bg-green-50 rounded-lg p-4">
                  <h4 className="font-semibold text-green-900 mb-2">🌐 社区交流</h4>
                  <ul className="space-y-1 text-green-800 text-sm">
                    <li>• 浏览和搜索他人的提示词</li>
                    <li>• 点赞、收藏和评论</li>
                    <li>• 关注感兴趣的创作者</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 用户责任 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-violet-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <UserCheck className="w-5 h-5 text-white" />
                </div>
                <CardTitle className="text-xl text-gray-900">2. 用户责任与义务</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">账户管理</h4>
                <div className="space-y-2">
                  {[
                    "提供真实、准确的注册信息",
                    "妥善保管账户密码，不与他人共享",
                    "及时更新个人信息变更",
                    "对账户下的所有活动负责"
                  ].map((item, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-600 text-sm">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">内容规范</h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-600 text-sm mb-3">您承诺发布的内容将：</p>
                  <div className="grid gap-2 md:grid-cols-2">
                    {[
                      "遵守法律法规",
                      "尊重他人权益",
                      "内容真实有效",
                      "不侵犯知识产权",
                      "不包含恶意代码",
                      "符合社区标准"
                    ].map((item, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <CheckCircle className="w-3 h-3 text-green-600" />
                        <span className="text-gray-600 text-xs">{item}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 禁止行为 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <XCircle className="w-5 h-5 text-white" />
                </div>
                <CardTitle className="text-xl text-gray-900">3. 禁止行为</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 leading-relaxed">
                在使用 PromptHub 服务时，严格禁止以下行为：
              </p>
              
              <div className="grid gap-4 md:grid-cols-2">
                <div className="border border-red-200 rounded-lg p-4 bg-red-50">
                  <h4 className="font-semibold text-red-900 mb-2">🚫 非法内容</h4>
                  <ul className="space-y-1 text-red-800 text-sm">
                    <li>• 发布违法、欺诈信息</li>
                    <li>• 传播仇恨言论</li>
                    <li>• 侵犯他人隐私</li>
                    <li>• 恶意传播病毒</li>
                  </ul>
                </div>
                
                <div className="border border-orange-200 rounded-lg p-4 bg-orange-50">
                  <h4 className="font-semibold text-orange-900 mb-2">⚠️ 滥用行为</h4>
                  <ul className="space-y-1 text-orange-800 text-sm">
                    <li>• 大量发送垃圾信息</li>
                    <li>• 恶意刷赞或评论</li>
                    <li>• 冒充他人身份</li>
                    <li>• 绕过技术限制</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 知识产权 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-500 rounded-lg flex items-center justify-center">
                  <Scale className="w-5 h-5 text-white" />
                </div>
                <CardTitle className="text-xl text-gray-900">4. 知识产权</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">平台知识产权</h4>
                <p className="text-gray-600 text-sm leading-relaxed">
                  PromptHub 的商标、标识、界面设计、软件代码等均受知识产权法保护，
                  未经许可不得复制、修改或用于商业目的。
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">用户内容权利</h4>
                <div className="bg-green-50 rounded-lg p-4">
                  <p className="text-green-800 text-sm mb-2">您发布的提示词内容：</p>
                  <ul className="space-y-1 text-green-700 text-sm">
                    <li>✓ 您保留完整的知识产权</li>
                    <li>✓ 您可以随时删除或修改</li>
                    <li>✓ 您授权平台展示和分发</li>
                    <li>✓ 其他用户可依据您设置的权限使用</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 免责声明 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-gray-500 to-slate-600 rounded-lg flex items-center justify-center">
                  <Shield className="w-5 h-5 text-white" />
                </div>
                <CardTitle className="text-xl text-gray-900">5. 免责声明</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-2">服务免责</h4>
                <ul className="space-y-1 text-gray-600 text-sm">
                  <li>• 服务按"现状"提供，不保证无故障</li>
                  <li>• 不对用户生成内容的准确性负责</li>
                  <li>• 不保证服务不会中断</li>
                  <li>• 第三方链接和服务不在担保范围内</li>
                </ul>
              </div>
              

            </CardContent>
          </Card>

          {/* 联系方式 */}
          <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl text-gray-900">6. 联系我们</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 leading-relaxed mb-4">
                如果您对本使用条款有任何疑问，请通过以下方式联系我们：
              </p>
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="space-y-1 text-blue-800 text-sm">
                  <p>📧 邮箱：<EMAIL></p>
                  <p>🌐 网站：prompthub.xin</p>
                  <p>💬 如有疑问，请随时联系我们</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  )
} 
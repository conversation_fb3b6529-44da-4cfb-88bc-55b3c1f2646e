import { Metadata } from 'next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Brain,
  Globe,
  Users,
  Target,
  Lightbulb,
  Heart,
  Sparkles,
  Zap,
  BookOpen,
  TrendingUp,
  Shield,
  Code
} from 'lucide-react'

export const metadata: Metadata = {
  title: '关于我们 - PromptHub AI提示词平台',
  description: 'PromptHub是专业的AI提示词分享社区，连接全球AI创作者，提供优质的ChatGPT、Midjourney等AI模型提示词，让每一次与AI的对话都充满智慧与创意。',
  keywords: [
    '关于PromptHub', 'AI提示词社区', 'AI提示词平台', '人工智能',
    'Prompt Engineering', '团队介绍', '提示词工程师', 'AI创作者社区',
    '中文AI提示词', '专业提示词平台', '免费AI工具', 'AIGC社区'
  ],
  openGraph: {
    title: '关于我们 - PromptHub AI提示词平台',
    description: 'PromptHub是专业的AI提示词分享社区，连接全球AI创作者，提供优质的ChatGPT、Midjourney等AI模型提示词。',
    type: 'website',
  },
}

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50">
      {/* Hero Section */}
      <section className="px-4 py-20 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <div className="mb-8 flex justify-center">
            <div className="relative">
              <div className="w-20 h-20 bg-gradient-to-br from-violet-600 to-cyan-600 rounded-2xl flex items-center justify-center">
                <Brain className="w-12 h-12 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-white" />
              </div>
            </div>
          </div>
          <h1 className="text-4xl font-bold mb-6 bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent">
            关于 PromptHub
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
            我们致力于连接全球AI创作者，打造最专业、最友好的提示词分享社区，
            让每一次与AI的对话都充满智慧与创意。
          </p>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="px-4 py-16 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-6xl">
          <div className="grid gap-8 lg:grid-cols-2">
            <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-violet-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <Target className="w-6 h-6 text-white" />
                  </div>
                  <CardTitle className="text-2xl text-gray-900">我们的使命</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 leading-relaxed">
                  降低AI使用门槛，让每个人都能轻松掌握与AI对话的艺术。
                  通过优质的提示词资源和活跃的社区交流，帮助用户充分发挥AI的潜能，
                  提升工作效率和创作质量。
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <Lightbulb className="w-6 h-6 text-white" />
                  </div>
                  <CardTitle className="text-2xl text-gray-900">我们的愿景</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 leading-relaxed">
                  成为全球领先的AI提示词平台，构建一个开放、包容、创新的生态系统。
                  让优质的提示词触手可及，让创意的火花在全球范围内传播，
                  推动人工智能技术的普及和应用。
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="px-4 py-16 sm:px-6 lg:px-8 bg-gradient-to-r from-violet-50 to-cyan-50">
        <div className="mx-auto max-w-6xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900">核心价值观</h2>
            <p className="text-lg text-gray-600">这些价值观指引着我们每一个决策和行动</p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {[
              {
                icon: Globe,
                title: '开放共享',
                description: '促进知识共享，打破信息壁垒，让优质提示词惠及每一个人',
                color: 'from-blue-500 to-cyan-500'
              },
              {
                icon: Users,
                title: '社区驱动',
                description: '以用户为中心，倾听社区声音，共同构建更好的平台',
                color: 'from-violet-500 to-purple-500'
              },
              {
                icon: Zap,
                title: '创新进取',
                description: '持续探索AI技术前沿，为用户提供最新最实用的工具',
                color: 'from-orange-500 to-red-500'
              },
              {
                icon: Shield,
                title: '品质保障',
                description: '严格把控内容质量，确保每一个提示词都经过精心验证',
                color: 'from-emerald-500 to-green-500'
              }
            ].map((value, index) => (
              <Card key={index} className="group border-0 bg-white/90 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <CardContent className="p-6 text-center">
                  <div className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r ${value.color} group-hover:scale-110 transition-transform duration-300`}>
                    <value.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{value.title}</h3>
                  <p className="text-sm text-gray-600 leading-relaxed">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Platform Features */}
      <section className="px-4 py-16 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-6xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900">平台特色</h2>
            <p className="text-lg text-gray-600">为什么选择 PromptHub</p>
          </div>

          <div className="grid gap-8 lg:grid-cols-3">
            {[
              {
                icon: BookOpen,
                title: '海量资源库',
                features: ['精选提示词模板', '多领域覆盖', '实时更新', '专业分类'],
                description: '汇聚全球优质提示词，涵盖创作、编程、营销、教育等多个领域'
              },
              {
                icon: Users,
                title: '活跃社区',
                features: ['创作者交流', '经验分享', '互助问答', '创意碰撞'],
                description: '连接志同道合的AI爱好者，在交流中获得灵感和成长'
              },
              {
                icon: TrendingUp,
                title: '智能推荐',
                features: ['个性化推送', '热门趋势', '使用统计', '效果反馈'],
                description: '基于用户偏好和使用数据，为您推荐最合适的提示词'
              }
            ].map((feature, index) => (
              <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
                <CardHeader>
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-violet-500 to-cyan-500 rounded-lg flex items-center justify-center">
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <CardTitle className="text-xl text-gray-900">{feature.title}</CardTitle>
                  </div>
                  <p className="text-gray-600">{feature.description}</p>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {feature.features.map((item, itemIndex) => (
                      <Badge key={itemIndex} variant="secondary" className="text-xs">
                        {item}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact & Community */}
      <section className="px-4 py-16 sm:px-6 lg:px-8 bg-gradient-to-r from-slate-50 to-gray-50">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold mb-6 text-gray-900">加入我们的社区</h2>
          <p className="text-lg text-gray-600 mb-8 leading-relaxed">
            PromptHub 不仅仅是一个平台，更是一个充满活力的创作者社区。
            在这里，每个人都可以分享自己的创意，学习他人的经验，共同推动AI技术的发展。
          </p>
          
          <div className="grid gap-6 md:grid-cols-3 mb-8">
            {[
              { icon: Heart, label: '友好氛围', desc: '包容开放的社区环境' },
              { icon: Sparkles, label: '创意激发', desc: '无限可能的创意碰撞' },
              { icon: Code, label: '技术前沿', desc: '紧跟AI技术发展趋势' }
            ].map((item, index) => (
              <div key={index} className="flex flex-col items-center">
                <div className="w-16 h-16 bg-gradient-to-r from-violet-500 to-cyan-500 rounded-full flex items-center justify-center mb-3">
                  <item.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-1">{item.label}</h3>
                <p className="text-sm text-gray-600">{item.desc}</p>
              </div>
            ))}
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">联系我们</h3>
            <div className="space-y-2 text-gray-600">
              <p>📧 邮箱：<EMAIL></p>
              <p>🌐 网站：prompthub.xin</p>
              <p>💬 我们随时倾听您的建议和反馈</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
} 
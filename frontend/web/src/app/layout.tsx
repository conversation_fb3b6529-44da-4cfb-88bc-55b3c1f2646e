import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import { AuthProvider } from "@/hooks/useAuth";
import ToastProvider from "@/components/providers/ToastProvider";
import ClientOnly from "@/components/providers/ClientOnly";
import { Footer } from "@/components/layout/Footer";
import { 
  organizationStructuredData, 
  websiteStructuredData, 
  softwareApplicationStructuredData 
} from "@/lib/structured-data";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: 'AI提示词大全 - 免费ChatGPT提示词库 | PromptHub',
    template: '%s | PromptHub - AI提示词大全'
  },
  description: "PromptHub提供10000+免费AI提示词，涵盖ChatGPT、Claude、Midjourney等主流AI工具。精选提示词模板，助您快速掌握AI提示词工程，提升AI使用效率。",
  keywords: [
    // 核心词汇 - 高频搜索词
    '提示词', 'AI提示词', '提示词大全', 'Prompt', 'ChatGPT提示词', 'AI指令',

    // 免费相关 - 用户痛点
    '免费提示词', '免费AI提示词', '提示词免费下载', '免费Prompt',

    // 工具相关 - 热门AI工具
    'ChatGPT', 'Claude', 'GPT-4', 'Midjourney提示词', '文心一言', '通义千问', 'Gemini',

    // 应用场景 - 具体需求
    'AI写作提示词', 'AI绘画提示词', 'AI编程提示词', 'AI营销提示词', 'AI翻译提示词',

    // 行业词汇 - 专业术语
    '提示词工程', 'Prompt Engineering', 'AIGC', '人工智能', '机器学习',

    // 长尾关键词 - 具体场景
    '优质提示词分享', 'AI提示词库', '提示词模板', '提示词生成器', 'AI助手提示词',
    '商业提示词', '学习提示词', '创意提示词', '办公提示词', '编程助手提示词'
  ],
  authors: [{ name: 'PromptHub Team' }],
  creator: 'PromptHub',
  publisher: 'PromptHub',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://www.prompthub.xin'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: '/',
    title: 'PromptHub - AI提示词分享、创作与学习平台',
    description: 'PromptHub是一个面向AI爱好者的提示词社区，提供海量优质ChatGPT、Midjourney等AI模型的提示词(Prompts)，助您轻松驾驭AI，激发无限创意。',
    siteName: 'PromptHub',
    images: [
      {
        url: '/icon-512x512.png',
        width: 512,
        height: 512,
        alt: 'PromptHub - AI提示词平台',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'PromptHub - AI提示词分享、创作与学习平台',
    description: 'PromptHub是一个面向AI爱好者的提示词社区，提供海量优质ChatGPT、Midjourney等AI模型的提示词(Prompts)，助您轻松驾驭AI，激发无限创意。',
    creator: '@prompthub',
    images: ['/icon-512x512.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
    yahoo: process.env.YAHOO_VERIFICATION,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationStructuredData)
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(websiteStructuredData)
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(softwareApplicationStructuredData)
          }}
        />
        {/* 隐藏的SEO Meta标签 - 搜索引擎可见 */}
        <meta name="subject" content="AI提示词工程和免费提示词库" />
        <meta name="topic" content="人工智能,提示词工程,ChatGPT,Claude,Midjourney" />
        <meta name="summary" content="PromptHub提供免费AI提示词大全，包含ChatGPT、Claude、Midjourney等主流AI工具的优质提示词模板" />
        <meta name="classification" content="AI工具,提示词,人工智能,教育资源" />
        <meta name="category" content="AI提示词库,提示词工程,人工智能工具" />
        <meta name="coverage" content="全球" />
        <meta name="distribution" content="全球" />
        <meta name="rating" content="适合所有用户" />
        <meta name="revisit-after" content="1 days" />

        {/* 百度站点验证 */}
        <meta name="baidu-site-verification" content={process.env.BAIDU_SITE_VERIFICATION} />
        {/* 360站点验证 */}
        <meta name="360-site-verification" content={process.env.SITE_360_VERIFICATION} />
        {/* 搜狗站点验证 */}
        <meta name="sogou_site_verification" content={process.env.SOGOU_SITE_VERIFICATION} />
      </head>
      <body className={`${inter.className} antialiased`}>
        <ClientOnly
          fallback={
            <div className="min-h-screen flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600"></div>
            </div>
          }
        >
          <AuthProvider>
            <Header />
            <main className="min-h-screen">
              {children}
            </main>
            <Footer />
            <ToastProvider />
          </AuthProvider>
        </ClientOnly>
      </body>
    </html>
  );
}

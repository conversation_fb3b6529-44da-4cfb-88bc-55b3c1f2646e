'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Save, X, Plus, ArrowLeft } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import authService from '@/lib/auth'
import { API_BASE_URL } from '@/lib/api'
import toast from 'react-hot-toast'

interface Category {
  id: number
  name: string
  description: string
  icon: string
  color: string
  value?: string
  label?: string
  displayName?: string
}

export default function CreatePromptPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const editId = searchParams.get('edit')
  const { user, loading } = useAuth()
  const [submitLoading, setSubmitLoading] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [categoriesLoading, setCategoriesLoading] = useState(true)

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    category: '',
    tags: [] as string[],
    isPrivate: false,
    status: 'draft' as 'draft' | 'published'
  })

  const [currentTag, setCurrentTag] = useState('')

  // 检查登录状态
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
    }
  }, [user, loading, router])

  // 获取分类列表
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/categories`)
        if (!response.ok) {
          throw new Error('获取分类失败')
        }
        const result = await response.json()
        if (result.success) {
          setCategories(result.data)
        }
      } catch (error) {
        console.error('获取分类失败:', error)
        toast.error('获取分类失败，请刷新页面重试')
      } finally {
        setCategoriesLoading(false)
      }
    }

    fetchCategories()
  }, [])

  // 如果是编辑模式，加载现有数据
  useEffect(() => {
    const loadPromptData = async () => {
      if (!editId || !user || categories.length === 0) return
      
      try {
        // 网页端完全依赖HttpOnly Cookie认证，不需要检查token
        const headers = {
          'Content-Type': 'application/json'
        }

        const response = await fetch(`${API_BASE_URL}/prompts/${editId}`, {
          headers,
          credentials: 'include' // 支持Cookie认证
        })

        if (!response.ok) {
          throw new Error('获取提示词数据失败')
        }

        const result = await response.json()
        
        if (result.success) {
          const promptData = result.data
          
          console.log('📋 编辑模式 - 原分类:', promptData.category)
          console.log('🔄 所有分类:', categories)
          
          // 在categories数组中查找匹配的分类
          // 先通过 value（英文标识符）匹配，再通过 name 匹配
          const matchedCategory = categories.find(cat => 
            cat.value === promptData.category || 
            cat.name === promptData.category ||
            cat.label === promptData.category
          )
          
          let displayCategory = ''
          if (matchedCategory) {
            // 使用中文显示名称
            displayCategory = matchedCategory.label || matchedCategory.displayName || matchedCategory.name
            console.log('✅ 找到匹配分类:', matchedCategory)
            console.log('🎯 将显示的分类:', displayCategory)
          } else {
            // 备用映射，直接使用中文映射
            const categoryMapping: { [key: string]: string } = {
              'ai-assistant': '生产力',
              'copywriting': '创意写作',
              'programming': '编程开发', 
              'design': '图像生成',
              'data-analysis': '数据分析',
              'education': '教育学习',
              'business-analysis': '商业营销',
              'lifestyle': '生活娱乐',
              'mcp-tools': 'MCP工具'
            }
            displayCategory = categoryMapping[promptData.category] || promptData.category || ''
            console.log('⚠️  使用备用映射:', displayCategory)
          }
          
          console.log('🔄 最终分类值:', displayCategory)
          
          // 回写表单数据
          setFormData({
            title: promptData.title || '',
            description: promptData.description || '',
            content: promptData.content || '',
            category: displayCategory, // 使用中文显示名称
            tags: Array.isArray(promptData.tags) ? promptData.tags : [],
            isPrivate: Boolean(promptData.isPrivate),
            status: promptData.status || 'draft'
          })
        } else {
          throw new Error(result.message || '获取提示词数据失败')
        }
      } catch (error) {
        console.error('加载提示词数据失败:', error)
        toast.error('加载提示词数据失败，请重试')
        router.push('/my-prompts')
      }
    }

    loadPromptData()
  }, [editId, user, router, categories]) // 添加 categories 依赖

  const handleAddTag = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && currentTag.trim()) {
      e.preventDefault()
      if (!formData.tags.includes(currentTag.trim())) {
        setFormData(prev => ({
          ...prev,
          tags: [...prev.tags, currentTag.trim()]
        }))
      }
      setCurrentTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleSubmit = async (e: React.FormEvent, publishNow = false) => {
    e.preventDefault()
    
    if (!formData.title || !formData.content || !formData.category) {
      toast.error('请填写所有必需字段')
      return
    }

    setSubmitLoading(true)

    try {
      // 将中文分类名称转换回英文标识符（保持与现有数据库数据的兼容性）
      // 首先尝试从 categories 数组中找到对应的英文值
      const selectedCategory = categories.find(cat => 
        cat.label === formData.category || 
        cat.displayName === formData.category || 
        cat.name === formData.category
      )
      
      let convertedCategory = ''
      if (selectedCategory) {
        // 使用从API返回的正确的英文标识符
        convertedCategory = selectedCategory.value || selectedCategory.name
        console.log('✅ 从分类数据匹配:', convertedCategory)
      } else {
        // 备用映射（兼容旧数据）
        const reverseCategoryMapping: { [key: string]: string } = {
          '生产力': 'ai-assistant',
          'MCP工具': 'mcp-tools',
          '商业营销': 'business-analysis',
          '图像生成': 'design',
          '创意写作': 'copywriting',
          '编程开发': 'programming',
          '数据分析': 'data-analysis',
          '教育学习': 'education',
          '生活娱乐': 'lifestyle'
        }
        
        convertedCategory = reverseCategoryMapping[formData.category] || formData.category
        console.log('⚠️  使用备用映射:', convertedCategory)
      }
      
      const dataToSubmit = {
        ...formData,
        category: convertedCategory,
        status: publishNow ? 'published' : 'draft'
      }
      
      console.log(editId ? '更新提示词:' : '创建提示词:', dataToSubmit)
      
      // 调用创建/更新提示词API
      const apiUrl = editId
        ? `${API_BASE_URL}/prompts/${editId}`
        : `${API_BASE_URL}/prompts`

      console.log('🔗 API URL:', apiUrl)
      console.log('📝 提交数据:', dataToSubmit)

      // 网页端完全依赖HttpOnly Cookie认证
      const headers = {
        'Content-Type': 'application/json'
      }

      const response = await fetch(apiUrl, {
        method: editId ? 'PUT' : 'POST',
        headers,
        body: JSON.stringify(dataToSubmit),
        credentials: 'include' // 支持Cookie认证
      })

      console.log('📡 响应状态:', response.status)
      
      const result = await response.json()
      console.log('📦 响应数据:', result)

      if (!response.ok) {
        // 更详细的错误处理
        if (response.status === 401) {
          toast.error('登录已过期，请重新登录')
          authService.clearAuth()
          router.push('/auth/login')
          return
        }
        throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      if (!result.success) {
        throw new Error(result.message || '操作失败')
      }
      
      const action = editId ? '更新' : '创建'
      const status = publishNow ? '发布' : '保存为草稿'
      toast.success(`提示词${action}成功！已${status}`, {
        icon: '🎉',
      })
      
      // 创建/更新成功后跳转到我的提示词页面
      router.push('/my-prompts')
    } catch (error: unknown) {
      console.error('❌ 操作失败详情:', error)
      
      const errorMessage = error instanceof Error ? error.message : '创建失败，请稍后重试'
      const errorName = error instanceof Error ? error.name : 'Unknown'
      
      console.error('❌ 错误名称:', errorName)
      console.error('❌ 错误消息:', errorMessage)
      
      // 更具体的错误提示
      if (errorMessage.includes('401') || errorMessage.includes('登录')) {
        toast.error(errorMessage)
      } else if (errorMessage.includes('网络') || errorName === 'TypeError') {
        toast.error('网络连接失败，请检查网络后重试')
      } else {
        toast.error(errorMessage || '创建失败，请稍后重试')
      }
    } finally {
      setSubmitLoading(false)
    }
  }

  // 加载中状态
  if (loading) {
    return (
      <div className="container max-w-4xl mx-auto py-8 px-4">
        <div className="text-center py-16">
          <div className="w-12 h-12 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  // 未登录状态
  if (!user) {
    return null // 会被重定向到登录页
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50">
      <div className="container max-w-4xl mx-auto py-8 px-4">
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="outline"
              onClick={() => router.push('/my-prompts')}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 border-gray-300 hover:border-gray-400"
            >
              <ArrowLeft className="w-4 h-4" />
              返回我的提示词
            </Button>
          </div>
          <h1 className="text-3xl font-bold mb-2 text-gray-900">
            {editId ? '编辑提示词' : '创建新提示词'}
          </h1>
          <p className="text-gray-600">
            分享你的优质提示词，帮助社区成员提升AI使用效果
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要表单 */}
          <div className="lg:col-span-2">
            <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-gray-900">
                  <Save className="w-5 h-5 text-violet-600" />
                  提示词信息
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={(e) => handleSubmit(e, false)} className="space-y-6">
                  {/* 标题 */}
                  <div className="space-y-2">
                    <Label htmlFor="title" className="text-sm font-medium text-gray-700">标题 *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="为你的提示词起一个清晰的标题"
                      className="border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                      required
                    />
                  </div>

                  {/* 描述 */}
                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-sm font-medium text-gray-700">描述</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="简要描述这个提示词的用途和特点"
                      className="border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                      rows={3}
                    />
                  </div>

                  {/* 分类 */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">分类 *</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                    >
                      <SelectTrigger className="border-gray-200 focus:border-violet-500 focus:ring-violet-500">
                        <SelectValue placeholder="选择分类" />
                      </SelectTrigger>
                      <SelectContent>
                        {categoriesLoading ? (
                          <SelectItem value="loading" disabled>
                            加载分类中...
                          </SelectItem>
                        ) : (
                          categories.map((category: Category) => (
                            <SelectItem 
                              key={category.id} 
                              value={category.label || category.displayName || category.name}
                            >
                              <span className="flex items-center gap-2">
                                <span>{category.icon}</span>
                                <span>{category.label || category.displayName || category.name}</span>
                              </span>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 提示词内容 */}
                  <div className="space-y-2">
                    <Label htmlFor="content" className="text-sm font-medium text-gray-700">提示词内容 *</Label>
                    <Textarea
                      id="content"
                      value={formData.content}
                      onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                      placeholder="输入完整的提示词内容..."
                      className="min-h-[200px] border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                      required
                    />
                  </div>

                  {/* 标签 */}
                  <div className="space-y-2">
                    <Label htmlFor="tags" className="text-sm font-medium text-gray-700">标签</Label>
                    <div className="space-y-3">
                      <Input
                        id="tags"
                        value={currentTag}
                        onChange={(e) => setCurrentTag(e.target.value)}
                        onKeyDown={handleAddTag}
                        placeholder="输入标签后按回车添加"
                        className="border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                      />
                      {formData.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {formData.tags.map((tag, index) => (
                            <Badge key={index} variant="secondary" className="flex items-center gap-1">
                              {tag}
                              <button
                                type="button"
                                onClick={() => removeTag(tag)}
                                className="ml-1 hover:text-red-500"
                              >
                                <X className="w-3 h-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 隐私设置 */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">隐私设置</Label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="private"
                        checked={!formData.isPrivate}
                        onChange={(e) => setFormData(prev => ({ ...prev, isPrivate: !e.target.checked }))}
                        className="rounded border-gray-300 text-violet-600 focus:ring-violet-500"
                      />
                      <label htmlFor="private" className="text-sm text-gray-600">
                        分享到提示词广场
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 ml-6">
                      勾选后，其他用户可以在广场看到并复制您的提示词
                    </p>
                  </div>

                  {/* 提交按钮 */}
                  <div className="flex gap-3 pt-4">
                    <Button 
                      type="submit" 
                      disabled={submitLoading}
                      className="flex-1 bg-gray-600 hover:bg-gray-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      {submitLoading ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                          保存中...
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4 mr-2" />
                          保存草稿
                        </>
                      )}
                    </Button>
                    <Button 
                      type="button" 
                      onClick={(e) => handleSubmit(e, true)}
                      disabled={submitLoading}
                      className="flex-1 bg-gradient-to-r from-violet-600 to-cyan-600 hover:from-violet-700 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      {submitLoading ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                          发布中...
                        </>
                      ) : (
                        <>
                          <Plus className="w-4 h-4 mr-2" />
                          发布提示词
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏：创建指南 */}
          <div className="lg:col-span-1">
            <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-gray-900">创建指南</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 text-sm text-gray-600">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">创建优质提示词的要点</h4>
                    <ul className="space-y-2">
                      <li>• 标题要清晰地表达提示词的用途</li>
                      <li>• 描述要简明扼要，突出特点</li>
                      <li>• 选择合适的分类</li>
                      <li>• 提示词内容要详细和可操作</li>
                      <li>• 添加相关标签便于搜索</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">发布选项</h4>
                    <ul className="space-y-2">
                      <li>• <strong>保存草稿</strong>：仅保存，不对外展示</li>
                      <li>• <strong>发布提示词</strong>：公开展示，其他用户可见</li>
                      <li>• <strong>私有</strong>：仅自己可见，不在公开列表中显示</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">示例标签</h4>
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="outline" className="text-xs">ChatGPT</Badge>
                      <Badge variant="outline" className="text-xs">创意写作</Badge>
                      <Badge variant="outline" className="text-xs">营销</Badge>
                      <Badge variant="outline" className="text-xs">编程</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
} 
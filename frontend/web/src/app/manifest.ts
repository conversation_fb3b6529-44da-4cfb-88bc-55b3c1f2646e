import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'PromptHub - AI提示词分享、创作与学习平台',
    short_name: 'PromptHub',
    description: 'PromptHub是一个面向AI爱好者的提示词社区，提供海量优质ChatGPT、Midjourney等AI模型的提示词(Prompts)，助您轻松驾驭AI，激发无限创意。',
    start_url: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#7c3aed',
    orientation: 'portrait',
    scope: '/',
    lang: 'zh-CN',
    categories: ['productivity', 'education', 'utilities'],
    icons: [
      {
        src: '/icon-192x192.png',
        sizes: '192x192',
        type: 'image/png',
        purpose: 'maskable'
      },
      {
        src: '/icon-512x512.png',
        sizes: '512x512',
        type: 'image/png',
        purpose: 'maskable'
      },
      {
        src: '/apple-touch-icon.png',
        sizes: '180x180',
        type: 'image/png'
      }
    ],
    shortcuts: [
      {
        name: '浏览提示词',
        short_name: '提示词库',
        description: '浏览所有公开的AI提示词',
        url: '/prompts',
        icons: [{ src: '/icon-192x192.png', sizes: '192x192' }]
      },
      {
        name: '创建提示词',
        short_name: '创建',
        description: '创建新的AI提示词',
        url: '/create',
        icons: [{ src: '/icon-192x192.png', sizes: '192x192' }]
      }
    ]
  }
}

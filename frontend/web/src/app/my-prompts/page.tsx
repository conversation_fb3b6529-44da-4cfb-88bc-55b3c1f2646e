'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Search,
  Download,
  Sparkles,
  Plus,
  Eye,
  Heart,
  Filter,
  Grid,
  List,
  TrendingUp,
  Clock,
  BarChart3,
  X
} from "lucide-react"
import { useAuth } from '@/hooks/useAuth'
import AuthGuard from '@/components/auth/AuthGuard'
import { PromptCard } from '@/components/prompt/PromptCard'
import Link from 'next/link'
import toast from 'react-hot-toast'
import { API_BASE_URL } from '@/lib/api'

// 后端返回的原始数据类型
interface RawPrompt {
  id: number
  title: string
  description: string
  content: string
  category: string
  categoryDisplayName?: string
  tags: string | string[]
  isPrivate?: boolean
  status?: string
  likes: number
  downloads: number
  views: number
  createdAt: string
  updatedAt: string
  userId?: number
  authorName?: string
}

// 提示词类型定义
interface Prompt {
  id: number
  title: string
  description: string
  content: string
  category: string
  categoryDisplayName?: string
  tags: string[]
  isPrivate?: boolean  // 改为可选，与PromptCard组件保持一致
  status?: string      // 改为可选
  likes: number
  downloads: number
  views: number
  createdAt: string
  updatedAt: string
  publishTime?: string // 格式化后的发布时间
  userId?: number      // 添加userId字段以保持一致性
  authorName?: string  // 添加authorName字段
}

export default function MyPromptsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchInput, setSearchInput] = useState('') // 输入框的值
  const [filterStatus, setFilterStatus] = useState('all')
  const [viewMode, setViewMode] = useState('grid')
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 6,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  const { user } = useAuth() as { user: { id: number; username: string } | null }

  // 获取用户的提示词
  const fetchMyPrompts = async (page = 1, isLoadMore = false) => {
    if (!user) return

    try {
      if (!isLoadMore) {
        setLoading(true)
      } else {
        setIsLoadingMore(true)
      }

      // 构建查询参数
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '6',
        sortBy: 'latest'
      })

      if (searchQuery.trim()) {
        params.append('search', searchQuery.trim())
      }

      if (filterStatus !== 'all') {
        params.append('status', filterStatus)
      }

      const url = `${API_BASE_URL}/prompts/my?${params.toString()}`
      console.log('🔍 发送请求到:', url)
      console.log('🔑 使用Cookie认证')

      // 网页端完全依赖HttpOnly Cookie认证，不需要Authorization头
      const headers = {
        'Content-Type': 'application/json'
      }

      const response = await fetch(url, {
        headers,
        credentials: 'include' // 支持Cookie认证
      })

      console.log('📡 响应状态:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ 请求失败:', response.status, errorText)
        throw new Error(`获取提示词失败: ${response.status} ${errorText}`)
      }

      const data = await response.json()
      if (data.success) {
        // 转换数据格式以匹配前端期望的格式
        const formattedPrompts = data.data.map((prompt: Record<string, unknown>) => ({
          ...prompt,
          tags: Array.isArray(prompt.tags) ? prompt.tags : JSON.parse((prompt.tags as string) || '[]'),
          isPrivate: Boolean(prompt.isPrivate),
          publishTime: new Date(prompt.createdAt as string).toLocaleDateString('zh-CN')
        }))

        if (isLoadMore) {
          // 加载更多时追加数据
          setPrompts(prev => [...prev, ...formattedPrompts])
        } else {
          // 首次加载或搜索时替换数据
          setPrompts(formattedPrompts)
        }

        // 更新分页信息
        if (data.pagination) {
          setPagination(data.pagination)
        }
      } else {
        throw new Error(data.message || '获取提示词失败')
      }
    } catch (err) {
      console.error('获取提示词失败:', err)
      setError(err instanceof Error ? err.message : '获取提示词失败')
    } finally {
      setLoading(false)
      setIsLoadingMore(false)
    }
  }

  // 初始加载
  useEffect(() => {
    fetchMyPrompts(1, false)
  }, [user])

  // 处理搜索
  const handleSearch = () => {
    setSearchQuery(searchInput.trim())
  }

  // 处理回车搜索
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  // 清除搜索内容
  const clearSearch = () => {
    setSearchInput('')
    setSearchQuery('')
  }

  // 搜索查询变化时重新加载
  useEffect(() => {
    if (user) {
      fetchMyPrompts(1, false)
    }
  }, [searchQuery, user])

  // 筛选状态变化时立即重新加载
  useEffect(() => {
    if (user) {
      fetchMyPrompts(1, false)
    }
  }, [filterStatus, user])

  const handleDelete = async (id: string | number) => {
    try {
      // 网页端完全依赖HttpOnly Cookie认证
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      const response = await fetch(`${API_BASE_URL}/prompts/${id}`, {
        method: 'DELETE',
        headers,
        credentials: 'include' // 支持Cookie认证
      })

      if (!response.ok) {
        throw new Error('删除提示词失败')
      }

      const data = await response.json()
      if (data.success) {
        // 重新加载当前页面数据
        fetchMyPrompts(1, false)
        toast.success('提示词删除成功')
      } else {
        throw new Error(data.message || '删除提示词失败')
      }
    } catch (err) {
      console.error('删除提示词失败:', err)
      toast.error(err instanceof Error ? err.message : '删除提示词失败')
    }
  }

  const handleUpdate = (updatedPrompt: Prompt) => {
    setPrompts(prevPrompts =>
      prevPrompts.map(p =>
        p.id === updatedPrompt.id ? updatedPrompt : p
      )
    )
  }

  // 加载更多
  const loadMorePrompts = () => {
    if (pagination.hasNext) {
      fetchMyPrompts(pagination.page + 1, true)
    }
  }

  // 计算统计数据（基于当前加载的数据）
  const totalLikes = prompts.reduce((sum, p) => sum + p.likes, 0)
  const totalDownloads = prompts.reduce((sum, p) => sum + p.downloads, 0)
  const totalViews = prompts.reduce((sum, p) => sum + p.views, 0)
  // 计算本周创建的提示词数量（基于当前加载的数据）
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
  const thisWeekCreated = prompts.filter(prompt =>
    new Date(prompt.createdAt) >= oneWeekAgo
  ).length

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>重试</Button>
        </div>
      </div>
    )
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50">
        <div className="container mx-auto px-6 py-8">
          {/* 页面头部 */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{pagination.total || prompts.length}个提示词助力您的创作</h1>
                <p className="text-gray-600">管理和编辑你创建的提示词</p>
              </div>
              <Link href="/create">
                <Button className="bg-gradient-to-r from-violet-600 to-cyan-600 hover:from-violet-700 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 gap-3 px-6 py-4 h-auto">
                  <Plus className="w-5 h-5" />
                  <div className="text-left">
                    <div className="font-medium text-base">添加提示词</div>
                    <div className="text-sm opacity-90">创建新的AI提示词</div>
                  </div>
                </Button>
              </Link>
            </div>

            {/* 创作统计仪表板 */}
            <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                  <BarChart3 className="w-5 h-5 text-red-500" />
                  创作统计
                </h2>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <TrendingUp className="w-4 h-4 text-green-500" />
                  <span className="text-green-600 font-medium">1个</span>
                  <span>平均每周创作</span>
                </div>
              </div>

              <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
                {/* 总提示词 */}
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-violet-100 to-violet-200 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Sparkles className="w-8 h-8 text-violet-600" />
                  </div>
                  <div className="text-3xl font-bold text-violet-600 mb-1">{pagination.total || prompts.length}</div>
                  <div className="text-sm text-gray-500">总提示词</div>
                  <div className="text-xs text-gray-400 mt-1">累计创作</div>
                </div>

                {/* 总点赞 */}
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Heart className="w-8 h-8 text-green-600" />
                  </div>
                  <div className="text-3xl font-bold text-green-600 mb-1">{totalLikes}</div>
                  <div className="text-sm text-gray-500">总点赞</div>
                  <div className="text-xs text-gray-400 mt-1">用户喜爱</div>
                </div>

                {/* 总下载 */}
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Download className="w-8 h-8 text-blue-600" />
                  </div>
                  <div className="text-3xl font-bold text-blue-600 mb-1">{totalDownloads}</div>
                  <div className="text-sm text-gray-500">总下载</div>
                  <div className="text-xs text-gray-400 mt-1">实用分享</div>
                </div>

                {/* 总浏览 */}
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-orange-200 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Eye className="w-8 h-8 text-orange-600" />
                  </div>
                  <div className="text-3xl font-bold text-orange-600 mb-1">{totalViews}</div>
                  <div className="text-sm text-gray-500">总浏览</div>
                  <div className="text-xs text-gray-400 mt-1">影响力</div>
                </div>
              </div>

              {/* 创作活跃度 */}
              <div className="mt-6 pt-6 border-t border-gray-100">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-gray-600">创作活跃度</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-gray-500">平均每周创作</span>
                    <span className="font-semibold text-gray-900">{thisWeekCreated}个</span>
                    <span className="text-gray-500">本周活跃</span>
                    <span className="text-green-600 font-medium">活跃</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 搜索和筛选 */}
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    placeholder="搜索我的提示词..."
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="pl-10 pr-10 border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                  />
                  {searchInput && (
                    <button
                      onClick={clearSearch}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 hover:text-gray-600 transition-colors"
                      aria-label="清除搜索"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                {/* 状态筛选 */}
                <div className="flex items-center gap-2">
                  <Filter className="w-4 h-4 text-gray-500" />
                  <select 
                    value={filterStatus} 
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="border border-gray-200 rounded-md px-3 py-2 text-sm focus:border-violet-500 focus:ring-violet-500 focus:outline-none"
                  >
                    <option value="all">全部</option>
                    <option value="published">已发布</option>
                    <option value="draft">草稿</option>
                    <option value="private">私有</option>
                  </select>
                </div>
                
                {/* 视图切换 */}
                <div className="flex items-center border border-gray-200 rounded-md p-1">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="p-2"
                  >
                    <Grid className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="p-2"
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* 最近提示词部分 */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900">最近提示词</h2>
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Clock className="w-4 h-4" />
                <span>最近活动</span>
              </div>
            </div>

            {/* 提示词列表 */}
            {prompts.length === 0 ? (
              <div className="text-center py-16">
                <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="w-12 h-12 text-gray-400" />
                </div>
                {searchQuery ? (
                  <>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无符合条件的提示词</h3>
                    <p className="text-gray-600 mb-6">没有找到包含 "{searchQuery}" 的提示词，试试其他关键词</p>
                    <Button
                      onClick={clearSearch}
                      variant="outline"
                      className="mb-4"
                    >
                      清除搜索条件
                    </Button>
                  </>
                ) : (
                  <>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">还没有提示词</h3>
                    <p className="text-gray-600 mb-6">创建你的第一个提示词，开始分享你的创意</p>
                    <Link href="/create">
                      <Button className="bg-gradient-to-r from-violet-600 to-cyan-600 hover:from-violet-700 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 gap-3 px-6 py-4 h-auto">
                        <Plus className="w-5 h-5" />
                        <div className="text-left">
                          <div className="font-medium text-base">添加提示词</div>
                          <div className="text-sm opacity-90">创建新的AI提示词</div>
                        </div>
                      </Button>
                    </Link>
                  </>
                )}
              </div>
            ) : (
              <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
                {prompts.map((prompt) => (
                  <PromptCard
                    key={prompt.id}
                    prompt={prompt}
                    isMyPrompt={true}
                    onDelete={handleDelete}
                    onUpdate={handleUpdate}
                  />
                ))}
              </div>
            )}
          </div>

          {/* 加载更多按钮 */}
          {prompts.length > 0 && pagination.hasNext && (
            <div className="flex justify-center mt-8">
              <Button
                onClick={loadMorePrompts}
                disabled={isLoadingMore}
                variant="outline"
                size="lg"
                className="flex items-center gap-2 px-8 py-3 text-violet-600 border-violet-200 hover:border-violet-300 hover:bg-violet-50"
              >
                {isLoadingMore ? (
                  <>
                    <div className="w-5 h-5 border-2 border-violet-600 border-t-transparent rounded-full animate-spin"></div>
                    <span>加载中...</span>
                  </>
                ) : (
                  <>
                    <Clock className="w-5 h-5" />
                    <span>加载更多提示词</span>
                  </>
                )}
              </Button>
            </div>
          )}

          {/* 已显示全部提示 */}
          {prompts.length > 0 && !pagination.hasNext && pagination.total > 6 && (
            <div className="text-center mt-8 py-6 text-gray-500">
              <Sparkles className="w-6 h-6 mx-auto mb-2 opacity-50" />
              <p>已显示全部 {pagination.total} 个提示词</p>
            </div>
          )}
        </div>
      </div>
    </AuthGuard>
  )
} 
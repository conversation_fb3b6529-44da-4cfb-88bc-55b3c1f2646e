import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '浏览器插件下载 - PromptHub AI提示词助手',
  description: 'PromptHub浏览器插件，一键访问海量AI提示词，支持Chrome浏览器，提供悬浮按钮、侧边栏等便捷功能，让AI创作更高效。',
  keywords: [
    // 产品相关
    'PromptHub插件', 'Chrome插件', 'AI提示词插件', '浏览器扩展',
    
    // 功能相关
    'AI助手插件', '提示词管理', '一键复制提示词', '悬浮提示词',
    
    // 使用场景
    '网页AI助手', '写作助手插件', '编程助手扩展', 'ChatGPT辅助工具',
    
    // 技术相关
    '浏览器AI工具', '前端AI插件', 'Chrome扩展开发', '免费AI插件'
  ],
  openGraph: {
    title: '浏览器插件下载 - PromptHub AI提示词助手',
    description: 'PromptHub浏览器插件，一键访问海量AI提示词，支持Chrome浏览器，让AI创作更高效。',
    type: 'website',
  },
}

export default function ExtensionLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}

'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Download,
  Chrome,
  Zap,
  Search,
  Copy,
  Sparkles,
  MousePointer,
  Keyboard,
  Users,
  Globe,
  Shield,
  Star,
  CheckCircle2,
  ArrowRight,
  Play,
  Pause,
  ExternalLink,
  Heart,
  BookOpen,
  Settings,
  Eye,
  ThumbsUp,
  X
} from 'lucide-react'

export default function ExtensionPage() {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false)

  const handleDownload = () => {
    // 下载插件zip文件
    const downloadUrl = '/prompthub-extension.zip'
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = 'prompthub-extension.zip'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleWatchDemo = () => {
    // 滚动到演示区域
    const demoSection = document.getElementById('demo-section')
    if (demoSection) {
      demoSection.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start' 
      })
    }
  }

  const features = [
    {
      icon: MousePointer,
      title: '拖拽悬浮按钮',
      description: '随时随地快速访问提示词库，支持自定义位置',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Zap,
      title: '一键复制使用',
      description: '选中网页文本，右键即可复制为提示词模板',
      color: 'from-violet-500 to-purple-500'
    },
    {
      icon: Search,
      title: '智能搜索',
      description: '快速查找所需提示词，支持分类和标签筛选',
      color: 'from-emerald-500 to-green-500'
    },
    {
      icon: Keyboard,
      title: '快捷键支持',
      description: 'Ctrl+Shift+P 快速开启侧边栏，提升使用效率',
      color: 'from-orange-500 to-red-500'
    },
    {
      icon: Users,
      title: '账户同步',
      description: '与网站账户无缝同步，随时访问个人提示词',
      color: 'from-pink-500 to-rose-500'
    },
    {
      icon: Shield,
      title: '隐私安全',
      description: '本地数据加密存储，保护您的隐私安全',
      color: 'from-indigo-500 to-blue-500'
    }
  ]

  const steps = [
    {
      step: 1,
      title: '下载插件',
      description: '点击下载按钮获取最新版本插件',
      icon: Download
    },
    {
      step: 2,
      title: '安装插件',
      description: '打开Chrome扩展管理页面，拖拽文件安装',
      icon: Chrome
    },
    {
      step: 3,
      title: '开始使用',
      description: '点击插件图标或使用快捷键开始体验',
      icon: Sparkles
    }
  ]

  const stats = [
    { icon: Users, label: '活跃用户', value: '10,000+' },
    { icon: ThumbsUp, label: '用户评分', value: '4.8' },
    { icon: Download, label: '下载次数', value: '50,000+' },
    { icon: Star, label: '五星好评', value: '95%' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-cyan-50">
      {/* Hero Section */}
      <section className="px-4 py-20 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <div className="mb-8 flex justify-center">
              <div className="relative">
                <div className="w-24 h-24 bg-white rounded-2xl flex items-center justify-center shadow-2xl border border-gray-200">
                  <img src="/extension-icon.png" alt="PromptHub Extension Icon" className="w-20 h-20 rounded-xl" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
              </div>
            </div>
            
            <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent">
              PromptHub 浏览器插件
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto mb-8">
              让AI提示词触手可及！在任何网页上快速访问海量优质提示词，
              一键复制使用，让您的AI创作更加高效便捷。
            </p>
            
                         <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
               <Button 
                 size="lg" 
                 className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg gap-3 shadow-xl font-semibold border-2 border-white/20"
                 onClick={handleDownload}
               >
                 <Download className="w-5 h-5" />
                 免费下载插件
                 <Badge variant="secondary" className="ml-2 bg-white/30 text-white font-medium">
                   v2.0.0
                 </Badge>
               </Button>
              
              <Button 
                variant="outline" 
                size="lg" 
                className="border-2 border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400 px-8 py-4 text-lg gap-3 bg-white/90 backdrop-blur-sm font-medium"
                onClick={handleWatchDemo}
              >
                <Play className="w-5 h-5" />
                观看演示
              </Button>
            </div>

            {/* Google Store Link */}
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                如果您可以正常访问谷歌插件商店，也可以选择{' '}
                <a 
                  href="https://chromewebstore.google.com/detail/prompthub-ai%E6%8F%90%E7%A4%BA%E8%AF%8D%E5%8A%A9%E6%89%8B/niadifmdkhiligphoipmkhfgkgikcgmn"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline font-medium inline-flex items-center gap-1"
                >
                  前往插件商店
                  <ExternalLink className="w-3 h-3" />
                </a>
                {' '}下载
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mt-16 max-w-4xl mx-auto">
              {stats.map((stat, index) => (
                <div key={index} className="text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20">
                  <stat.icon className="w-8 h-8 text-violet-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="px-4 py-16 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-6xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900">强大功能特性</h2>
            <p className="text-lg text-gray-600">为您的AI创作之旅提供全方位支持</p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {features.map((feature, index) => (
              <Card key={index} className="group border-0 bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <CardContent className="p-6">
                  <div className={`mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r ${feature.color} group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-sm text-gray-600 leading-relaxed">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section id="demo-section" className="px-4 py-16 sm:px-6 lg:px-8 bg-gradient-to-r from-violet-50 to-cyan-50">
        <div className="mx-auto max-w-6xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900">使用演示</h2>
            <p className="text-lg text-gray-600">看看插件是如何让您的工作更高效的</p>
          </div>
          
          <Card className="border-0 bg-white/90 backdrop-blur-sm shadow-2xl overflow-hidden">
            <CardContent className="p-0">
              <div className="relative aspect-video">
                {!isVideoPlaying ? (
                  // 视频封面/预览状态 - 简化布局避免重叠
                  <div className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-violet-900 aspect-video flex items-center justify-center cursor-pointer"
                       onClick={() => setIsVideoPlaying(true)}>
                    
                    {/* 简单的背景渐变 */}
                    <div className="absolute inset-0 bg-gradient-to-br from-violet-600/20 to-cyan-600/20"></div>
                    
                    {/* 播放按钮区域 - 完全居中，无重叠元素 */}
                    <div className="flex flex-col items-center justify-center h-full w-full">
                      {/* 播放按钮 */}
                      <Button 
                        size="lg" 
                        className="bg-yellow-500 border-2 border-yellow-400 text-black hover:bg-yellow-400 gap-3 font-bold shadow-2xl hover:scale-105 transition-all duration-300 px-8 py-4 text-lg mb-4"
                      >
                        <Play className="w-6 h-6" />
                        播放演示视频
                      </Button>
                      
                      {/* 描述文字 */}
                      <p className="text-white/90 text-base font-medium text-center">
                        了解插件核心功能和使用方法
                      </p>
                    </div>
                    
                    {/* 视频信息卡片 - 固定在底部 */}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                      <div className="text-white">
                        <div className="text-lg font-semibold mb-1">PromptHub 浏览器插件演示</div>
                        <div className="text-sm text-gray-300">在任意网页快速使用AI提示词 • 时长 3:26</div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // 真实视频播放状态
                  <div className="relative w-full h-full">
                    <iframe
                      src="https://player.bilibili.com/player.html?bvid=BV1sFgRzME7d&page=1&high_quality=1&danmaku=0"
                      className="w-full h-full"
                      frameBorder="0"
                      allowFullScreen
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      title="PromptHub 插件使用演示"
                    ></iframe>
                    
                    {/* 关闭按钮 */}
                    <Button 
                      size="sm"
                      variant="outline"
                      className="absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white border-white/30 hover:border-white/50"
                      onClick={() => setIsVideoPlaying(false)}
                    >
                      <X className="w-4 h-4 mr-1" />
                      关闭
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          
          {/* 视频下方的提示信息 */}
          <div className="text-center mt-6">
            <p className="text-sm text-gray-600">
              视频展示了插件的核心功能和使用方法，建议全屏观看以获得最佳体验
            </p>
            {isVideoPlaying && (
              <div className="mt-3">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => window.open('https://www.bilibili.com/video/BV1sFgRzME7d/', '_blank')}
                  className="gap-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  在B站观看完整版
                </Button>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Installation Guide */}
      <section className="px-4 py-16 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900">安装指南</h2>
            <p className="text-lg text-gray-600">三步完成安装，立即开始使用</p>
          </div>
          
          <div className="space-y-8">
            {steps.map((step, index) => (
              <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
                <CardContent className="p-8">
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 bg-gradient-to-r from-violet-600 to-cyan-600 rounded-full flex items-center justify-center text-white text-xl font-bold">
                        {step.step}
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <step.icon className="w-6 h-6 text-violet-600" />
                        <h3 className="text-xl font-semibold text-gray-900">{step.title}</h3>
                      </div>
                      <p className="text-gray-600 mb-4">{step.description}</p>
                      
                                             {step.step === 1 && (
                         <div className="space-y-3">
                           <Button 
                             className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white gap-2 font-semibold px-6 py-2.5 shadow-lg"
                             onClick={handleDownload}
                           >
                             <Download className="w-4 h-4" />
                             下载 Chrome 插件
                           </Button>
                          <div className="text-sm text-gray-500">
                            支持 Chrome 88+ 版本，zip文件包含完整插件源码
                          </div>
                        </div>
                      )}
                      
                      {step.step === 2 && (
                        <div className="space-y-3">
                          <div className="bg-gray-50 p-4 rounded-lg">
                            <ol className="text-sm text-gray-600 space-y-2">
                              <li className="flex items-start gap-2">
                                <CheckCircle2 className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                                在地址栏输入 <code className="bg-gray-200 px-2 py-1 rounded">chrome://extensions/</code>
                              </li>
                              <li className="flex items-start gap-2">
                                <CheckCircle2 className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                                开启"开发者模式"
                              </li>
                              <li className="flex items-start gap-2">
                                <CheckCircle2 className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                                解压zip文件，然后点击"加载已解压的扩展程序"选择文件夹
                              </li>
                            </ol>
                          </div>
                        </div>
                      )}
                      
                      {step.step === 3 && (
                        <div className="space-y-3">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="bg-gradient-to-r from-violet-50 to-purple-50 p-4 rounded-lg border border-violet-200">
                              <div className="flex items-center gap-2 mb-2">
                                <MousePointer className="w-4 h-4 text-violet-600" />
                                <span className="font-medium text-gray-900">点击图标</span>
                              </div>
                              <p className="text-sm text-gray-600">点击浏览器工具栏的插件图标</p>
                            </div>
                            <div className="bg-gradient-to-r from-cyan-50 to-blue-50 p-4 rounded-lg border border-cyan-200">
                              <div className="flex items-center gap-2 mb-2">
                                <Keyboard className="w-4 h-4 text-cyan-600" />
                                <span className="font-medium text-gray-900">快捷键</span>
                              </div>
                              <p className="text-sm text-gray-600">
                                <kbd className="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl+Shift+P</kbd>
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* User Reviews */}
      <section className="px-4 py-16 sm:px-6 lg:px-8 bg-gradient-to-r from-slate-50 to-gray-50">
        <div className="mx-auto max-w-6xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900">用户反馈</h2>
            <p className="text-lg text-gray-600">看看其他用户是怎么说的</p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-3">
            {[
              {
                name: "小李同学",
                role: "内容创作者",
                content: "这个插件太棒了！再也不用在网站和AI工具之间来回切换，直接在侧边栏就能找到需要的提示词。",
                rating: 5,
                avatar: "👨‍💻"
              },
              {
                name: "产品经理王女士",
                role: "产品经理",
                content: "团队协作效率提升了很多，大家都在用这个插件分享和使用提示词模板。快捷键特别方便！",
                rating: 5,
                avatar: "👩‍💼"
              },
              {
                name: "设计师张先生",
                role: "UI设计师",
                content: "界面设计很用心，使用体验很流畅。拖拽悬浮按钮的功能设计得很贴心，不会遮挡工作内容。",
                rating: 5,
                avatar: "🎨"
              }
            ].map((review, index) => (
              <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-violet-500 to-cyan-500 rounded-full flex items-center justify-center text-xl">
                      {review.avatar}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{review.name}</div>
                      <div className="text-sm text-gray-600">{review.role}</div>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-4 leading-relaxed">{review.content}</p>
                  <div className="flex gap-1">
                    {[...Array(review.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="px-4 py-16 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900">常见问题</h2>
            <p className="text-lg text-gray-600">解答您可能遇到的问题</p>
          </div>
          
          <div className="space-y-6">
            {[
              {
                q: "插件是免费的吗？",
                a: "是的，PromptHub 浏览器插件完全免费，无任何隐藏费用。我们致力于为用户提供最好的AI提示词体验。"
              },
              {
                q: "支持哪些浏览器？",
                a: "目前支持 Chrome 88+ 版本。我们正在开发 Firefox 和 Edge 版本，敬请期待。"
              },
              {
                q: "插件会收集我的个人数据吗？",
                a: "我们非常重视用户隐私。插件只会收集必要的使用统计数据以改善服务，不会收集任何个人敏感信息。"
              },
              {
                q: "如何同步我的提示词？",
                a: "在插件中登录您的 PromptHub 账户即可自动同步您创建和收藏的提示词。"
              }
            ].map((faq, index) => (
              <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-violet-600 to-cyan-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      ?
                    </div>
                    {faq.q}
                  </h3>
                  <p className="text-gray-600 leading-relaxed ml-8">{faq.a}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="px-4 py-20 sm:px-6 lg:px-8 bg-gradient-to-r from-violet-600 to-cyan-600">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            立即开始您的AI创作之旅
          </h2>
          <p className="text-xl text-white/90 mb-8 leading-relaxed">
            加入数万用户的选择，让优质提示词触手可及
          </p>
          
                     <div className="flex flex-col sm:flex-row gap-4 justify-center">
             <Button 
               size="lg" 
               className="bg-white text-blue-700 hover:bg-blue-50 border-2 border-blue-200 hover:border-blue-300 px-8 py-4 text-lg gap-3 shadow-xl font-semibold"
               onClick={handleDownload}
             >
               <Download className="w-5 h-5" />
               立即下载插件
             </Button>
            
            <Link href="/">
              <Button size="lg" className="bg-green-500 border-2 border-green-400 text-black hover:bg-green-400 px-8 py-4 text-lg gap-3 font-bold shadow-xl">
                <ExternalLink className="w-5 h-5" />
                访问网站版本
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
} 
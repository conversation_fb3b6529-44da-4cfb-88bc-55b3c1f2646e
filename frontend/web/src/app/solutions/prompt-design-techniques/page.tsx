import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Brain, Lightbulb, Target, CheckCircle, XCircle, ArrowRight } from 'lucide-react'

export const metadata: Metadata = {
  title: '提示词设计通用技巧 - 从简单到复杂的优化方法',
  description: '掌握提示词设计的核心技巧，从简单开始逐步优化，学会具体性、明确性等关键设计原则，避免常见设计陷阱。',
  keywords: [
    '提示词设计', '提示词优化', 'Prompt设计技巧', 'AI指令优化',
    '提示词迭代', '设计原则', '最佳实践', '提示词改进',
    '具体性原则', '明确性原则', '提示词调优', 'AI交互优化'
  ],
  openGraph: {
    title: '提示词设计通用技巧 - 从简单到复杂的优化方法',
    description: '掌握提示词设计的核心技巧，从简单开始逐步优化，学会具体性、明确性等关键设计原则。',
    type: 'article',
    url: 'https://www.prompthub.xin/solutions/prompt-design-techniques',
    siteName: 'PromptHub',
    locale: 'zh_CN',
  },
  twitter: {
    card: 'summary_large_image',
    title: '提示词设计通用技巧 - 从简单到复杂的优化方法',
    description: '掌握提示词设计的核心技巧，从简单开始逐步优化，学会具体性、明确性等关键设计原则。',
  },
  alternates: {
    canonical: 'https://www.prompthub.xin/solutions/prompt-design-techniques',
  },
}

export default function PromptDesignTechniquesPage() {
  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "提示词设计通用技巧 - 从简单到复杂的优化方法",
    "description": "掌握提示词设计的核心技巧，从简单开始逐步优化，学会具体性、明确性等关键设计原则，避免常见设计陷阱。",
    "url": "https://www.prompthub.xin/solutions/prompt-design-techniques",
    "datePublished": "2024-12-01",
    "dateModified": "2025-01-01",
    "author": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "publisher": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "https://www.prompthub.xin/solutions/prompt-design-techniques"
    },
    "articleSection": "提示工程指南",
    "keywords": "提示词设计, 提示词优化, 设计原则, 最佳实践",
    "educationalLevel": "进阶技巧",
    "timeRequired": "PT10M",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://www.prompthub.xin"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "提示工程指南",
          "item": "https://www.prompthub.xin/solutions"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "提示词设计通用技巧",
          "item": "https://www.prompthub.xin/solutions/prompt-design-techniques"
        }
      ]
    },
    "mainEntity": {
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "提示词设计应该从哪里开始？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "从简单开始，逐步优化。先写一个基础的提示词，测试效果，然后根据结果逐步添加更多细节和约束条件。"
          }
        },
        {
          "@type": "Question",
          "name": "如何让提示词更加具体和明确？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "使用具体的数字、明确的格式要求、详细的背景信息，避免模糊的词汇，提供具体的例子和期望的输出格式。"
          }
        }
      ]
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link href="/solutions">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="w-4 h-4" />
              返回解决方案
            </Button>
          </Link>
        </div>

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
            <Brain className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            提示词设计通用技巧
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            从简单开始，逐步优化，掌握具体性、明确性等关键设计原则
          </p>
          <div className="flex items-center justify-center gap-4 mt-6">
            <Badge variant="secondary">进阶技巧</Badge>
            <Badge variant="outline">10分钟阅读</Badge>
          </div>
        </div>

        {/* 核心内容 */}
        <div className="max-w-4xl mx-auto space-y-8">
          
          {/* 核心原则 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="w-5 h-5 text-yellow-500" />
                设计提示词的核心原则
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed mb-6">
                设计提示词是一个迭代过程，需要大量的实验才能获得最佳结果。
                以下是一些经过验证的通用技巧，帮助你设计出更有效的提示词。
              </p>
            </CardContent>
          </Card>

          {/* 技巧详解 */}
          <div className="space-y-6">
            
            {/* 从简单开始 */}
            <Card className="border-l-4 border-l-green-500">
              <CardHeader>
                <CardTitle className="text-green-600">1. 从简单开始</CardTitle>
                <CardDescription>迭代优化，逐步完善</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-700">
                  在开始设计提示时，你应该记住，这实际上是一个迭代过程。
                  使用简单的 playground 是一个很好的起点。
                </p>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-800 mb-2">实践建议：</h4>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• 从简单的提示词开始</li>
                    <li>• 逐渐添加更多元素和上下文</li>
                    <li>• 将大任务分解为更简单的子任务</li>
                    <li>• 随着结果的改善逐步构建</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* 明确指令 */}
            <Card className="border-l-4 border-l-blue-500">
              <CardHeader>
                <CardTitle className="text-blue-600">2. 明确指令</CardTitle>
                <CardDescription>使用清晰的命令和分隔符</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-700">
                  你可以使用命令来指示模型执行各种简单任务，例如"写入"、"分类"、"总结"、"翻译"、"排序"等。
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-800 mb-2">推荐格式：</h4>
                    <div className="text-sm text-blue-700 font-mono bg-white p-2 rounded">
                      ### 指令 ###<br />
                      将以下文本翻译成西班牙语：<br />
                      文本："hello！"
                    </div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-gray-800 mb-2">输出结果：</h4>
                    <div className="text-sm text-gray-700 font-mono bg-white p-2 rounded">
                      ¡Hola!
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 具体性 */}
            <Card className="border-l-4 border-l-purple-500">
              <CardHeader>
                <CardTitle className="text-purple-600">3. 具体性</CardTitle>
                <CardDescription>详细描述期望的结果</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-700">
                  要非常具体地说明你希望模型执行的指令和任务。提示越具描述性和详细，结果越好。
                </p>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-purple-800 mb-3">实际案例：</h4>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm font-medium text-purple-700">提示：</p>
                      <div className="text-sm bg-white p-2 rounded border">
                        提取以下文本中的地名。<br />
                        所需格式：<br />
                        地点：&lt;逗号分隔的地名列表&gt;
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-purple-700">输出：</p>
                      <div className="text-sm bg-white p-2 rounded border">
                        地点：里斯本，香帕利莫德中心
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 避免不明确 */}
            <Card className="border-l-4 border-l-orange-500">
              <CardHeader>
                <CardTitle className="text-orange-600">4. 避免不明确</CardTitle>
                <CardDescription>具体和直接会更好</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-700">
                  给定上述关于详细描述和改进格式的建议，很容易陷入陷阱：想要在提示上过于聪明，
                  从而可能创造出不明确的描述。通常来说，具体和直接会更好。
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-red-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <XCircle className="w-4 h-4 text-red-500" />
                      <h4 className="font-semibold text-red-800">不好的示例：</h4>
                    </div>
                    <div className="text-sm text-red-700 bg-white p-2 rounded">
                      解释提示工程的概念。保持解释简短，只有几句话，不要过于描述。
                    </div>
                    <p className="text-xs text-red-600 mt-2">
                      不清楚要使用多少句话以及什么风格
                    </p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <h4 className="font-semibold text-green-800">好的示例：</h4>
                    </div>
                    <div className="text-sm text-green-700 bg-white p-2 rounded">
                      使用 2-3 句话向高中学生解释提示工程的概念。
                    </div>
                    <p className="text-xs text-green-600 mt-2">
                      具体、简洁并且切中要点
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 做什么还是不做什么 */}
            <Card className="border-l-4 border-l-red-500">
              <CardHeader>
                <CardTitle className="text-red-600">5. 说要做什么，而不是不要做什么</CardTitle>
                <CardDescription>正面指导更有效</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-700">
                  设计提示时的另一个常见技巧是避免说不要做什么，而应该说要做什么。
                  这样更加的具体，并且聚焦于有利于模型生成良好回复的细节上。
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-red-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <XCircle className="w-4 h-4 text-red-500" />
                      <h4 className="font-semibold text-red-800">失败的示例：</h4>
                    </div>
                    <div className="text-sm text-red-700 bg-white p-2 rounded">
                      以下是向客户推荐电影的代理程序。不要询问兴趣。不要询问个人信息。
                    </div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <h4 className="font-semibold text-green-800">更好的示例：</h4>
                    </div>
                    <div className="text-sm text-green-700 bg-white p-2 rounded">
                      以下是向客户推荐电影的代理程序。代理负责从全球热门电影中推荐电影。
                      如果代理没有电影推荐，它应该回答"抱歉，今天找不到电影推荐。"
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 实践检查清单 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-blue-500" />
                设计检查清单
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-blue-600">设计前检查：</h4>
                  <div className="space-y-2 text-sm">
                    <label className="flex items-center gap-2">
                      <input type="checkbox" className="rounded" />
                      <span>明确任务目标</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" className="rounded" />
                      <span>确定输出格式</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" className="rounded" />
                      <span>准备测试数据</span>
                    </label>
                  </div>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold text-blue-600">设计后验证：</h4>
                  <div className="space-y-2 text-sm">
                    <label className="flex items-center gap-2">
                      <input type="checkbox" className="rounded" />
                      <span>指令是否清晰明确</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" className="rounded" />
                      <span>是否提供足够上下文</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" className="rounded" />
                      <span>输出格式是否明确</span>
                    </label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 下一步 */}
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
            <CardContent className="p-6">
              <div className="text-center">
                <h3 className="text-xl font-semibold mb-4">准备学习更高级的技巧？</h3>
                <p className="text-gray-600 mb-6">
                  掌握了基础设计技巧后，让我们探索少样本提示和思维链推理
                </p>
                <div className="flex gap-4 justify-center">
                  <Link href="/solutions/few-shot-prompting">
                    <Button variant="outline" className="gap-2">
                      少样本提示
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  </Link>
                  <Link href="/solutions/chain-of-thought">
                    <Button className="gap-2">
                      思维链推理
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
    </>
  )
}

import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  ArrowLeft,
  GitBranch,
  History,
  FileText,
  Users,
  CheckCircle,
  AlertCircle,
  Lightbulb,
  Target,
  Code,
  Database
} from 'lucide-react'

export const metadata: Metadata = {
  title: '提示词版本控制指南 - 像管理代码一样管理你的Prompt',
  description: '学会像管理代码一样管理你的提示词，掌握版本控制、变更追踪、A/B测试等专业技能，优化提示词迭代过程。',
  keywords: [
    '提示词版本控制', 'Prompt版本管理', '变更追踪', 'A/B测试',
    '版本管理策略', '提示词迭代', '专业技能', '优化迭代',
    '版本历史', '回滚机制', '分支管理', '协作开发'
  ],
  openGraph: {
    title: '提示词版本控制指南 - 像管理代码一样管理你的Prompt',
    description: '学会像管理代码一样管理你的提示词，掌握版本控制、变更追踪、A/B测试等专业技能。',
    type: 'article',
    url: 'https://www.prompthub.xin/solutions/prompt-version-control',
    siteName: 'PromptHub',
    locale: 'zh_CN',
  },
  twitter: {
    card: 'summary_large_image',
    title: '提示词版本控制指南 - 像管理代码一样管理你的Prompt',
    description: '学会像管理代码一样管理你的提示词，掌握版本控制、变更追踪、A/B测试等专业技能。',
  },
  alternates: {
    canonical: 'https://www.prompthub.xin/solutions/prompt-version-control',
  },
}

export default function PromptVersionControlPage() {
  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "提示词版本控制指南 - 像管理代码一样管理你的Prompt",
    "description": "学会像管理代码一样管理你的提示词，掌握版本控制、变更追踪、A/B测试等专业技能，优化提示词迭代过程。",
    "url": "https://www.prompthub.xin/solutions/prompt-version-control",
    "datePublished": "2024-12-01",
    "dateModified": "2025-01-01",
    "author": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "publisher": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "https://www.prompthub.xin/solutions/prompt-version-control"
    },
    "articleSection": "提示工程指南",
    "keywords": "提示词版本控制, 版本管理, 变更追踪, 专业技能",
    "educationalLevel": "专业技能",
    "timeRequired": "PT12M",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://www.prompthub.xin"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "提示工程指南",
          "item": "https://www.prompthub.xin/solutions"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "提示词版本控制指南",
          "item": "https://www.prompthub.xin/solutions/prompt-version-control"
        }
      ]
    },
    "mainEntity": {
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "为什么需要对提示词进行版本控制？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "版本控制可以追踪提示词的优化历程，记录每次修改的原因和效果，保留有效版本的备份，支持回滚和A/B测试。"
          }
        },
        {
          "@type": "Question",
          "name": "如何实施提示词的版本管理策略？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "建立版本命名规范、记录变更日志、实施分支管理、进行效果测试、建立回滚机制等系统化的版本管理流程。"
          }
        }
      ]
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link href="/solutions">
            <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回提示工程指南
            </Button>
          </Link>
        </div>

        {/* 页面头部 */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl mb-6">
            <GitBranch className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            提示词版本控制指南
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-6">
            像管理代码一样管理你的提示词，追踪变更历史，优化迭代过程
          </p>
          <div className="flex flex-wrap justify-center gap-2">
            <Badge variant="secondary">专业技能</Badge>
            <Badge variant="outline">12分钟阅读</Badge>
            <Badge variant="outline">版本控制</Badge>
          </div>
        </div>

        {/* 主要内容 */}
        <div className="max-w-4xl mx-auto space-y-8">
          
          {/* 为什么需要版本控制 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertCircle className="w-5 h-5 mr-2 text-orange-500" />
                为什么提示词需要版本控制？
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-900">常见问题</h4>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      修改后效果变差，但忘记了原来的版本
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      团队协作时提示词版本混乱
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      无法追踪哪个版本效果最好
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      A/B测试结果无法对应具体版本
                    </li>
                  </ul>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-900">版本控制的价值</h4>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                      完整的修改历史记录
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                      随时回滚到任意版本
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                      团队协作更加高效
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                      性能优化有据可循
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 版本控制基础概念 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Code className="w-5 h-5 mr-2 text-blue-500" />
                版本控制基础概念
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <FileText className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                  <h4 className="font-semibold mb-2">版本号</h4>
                  <p className="text-sm text-gray-600">v1.0, v1.1, v2.0<br/>语义化版本管理</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <History className="w-8 h-8 text-green-500 mx-auto mb-2" />
                  <h4 className="font-semibold mb-2">变更日志</h4>
                  <p className="text-sm text-gray-600">记录每次修改<br/>的原因和效果</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <GitBranch className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                  <h4 className="font-semibold mb-2">分支管理</h4>
                  <p className="text-sm text-gray-600">并行开发<br/>不同版本</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 实践方法 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Target className="w-5 h-5 mr-2 text-green-500" />
                版本控制实践方法
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              
              {/* 方法1：文件命名法 */}
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold text-gray-900 mb-2">1. 文件命名法（入门级）</h4>
                <div className="bg-gray-50 p-4 rounded-lg mb-3">
                  <code className="text-sm">
                    customer_service_v1.0.txt<br/>
                    customer_service_v1.1.txt<br/>
                    customer_service_v2.0_final.txt
                  </code>
                </div>
                <p className="text-gray-600 text-sm">
                  优点：简单易懂，无需工具<br/>
                  缺点：容易混乱，难以管理大量版本
                </p>
              </div>

              {/* 方法2：表格管理法 */}
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold text-gray-900 mb-2">2. 表格管理法（进阶级）</h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border border-gray-300 px-3 py-2">版本</th>
                        <th className="border border-gray-300 px-3 py-2">修改日期</th>
                        <th className="border border-gray-300 px-3 py-2">修改内容</th>
                        <th className="border border-gray-300 px-3 py-2">效果评估</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-3 py-2">v1.0</td>
                        <td className="border border-gray-300 px-3 py-2">2024-01-01</td>
                        <td className="border border-gray-300 px-3 py-2">初始版本</td>
                        <td className="border border-gray-300 px-3 py-2">基准测试</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-3 py-2">v1.1</td>
                        <td className="border border-gray-300 px-3 py-2">2024-01-05</td>
                        <td className="border border-gray-300 px-3 py-2">增加上下文示例</td>
                        <td className="border border-gray-300 px-3 py-2">准确率提升15%</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* 方法3：Git管理法 */}
              <div className="border-l-4 border-purple-500 pl-4">
                <h4 className="font-semibold text-gray-900 mb-2">3. Git管理法（专业级）</h4>
                <div className="bg-gray-900 text-green-400 p-4 rounded-lg mb-3 font-mono text-sm">
                  <div># 初始化仓库</div>
                  <div>git init prompt-library</div>
                  <div className="mt-2"># 提交新版本</div>
                  <div>git add customer_service.txt</div>
                  <div>git commit -m "v1.1: 增加上下文示例，提升准确率15%"</div>
                  <div className="mt-2"># 创建分支测试</div>
                  <div>git checkout -b experiment/new-format</div>
                </div>
                <p className="text-gray-600 text-sm">
                  优点：专业工具，功能强大，支持分支和合并<br/>
                  缺点：需要学习Git基础知识
                </p>
              </div>
            </CardContent>
          </Card>

          {/* A/B测试与版本控制 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="w-5 h-5 mr-2 text-orange-500" />
                A/B测试与版本控制结合
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3">测试流程</h4>
                  <ol className="space-y-2 text-gray-600">
                    <li className="flex items-start">
                      <span className="bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">1</span>
                      创建基准版本（v1.0）
                    </li>
                    <li className="flex items-start">
                      <span className="bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">2</span>
                      开发实验版本（v1.1-exp）
                    </li>
                    <li className="flex items-start">
                      <span className="bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">3</span>
                      并行测试两个版本
                    </li>
                    <li className="flex items-start">
                      <span className="bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">4</span>
                      记录测试结果
                    </li>
                    <li className="flex items-start">
                      <span className="bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">5</span>
                      选择最优版本发布
                    </li>
                  </ol>
                </div>
                <div>
                  <h4 className="font-semibold mb-3">关键指标</h4>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      响应准确率
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      用户满意度
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      处理时间
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      错误率
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      一致性评分
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 团队协作最佳实践 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2 text-indigo-500" />
                团队协作最佳实践
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">协作规范</h4>
                  <ul className="space-y-2 text-gray-600">
                    <li>• 统一的版本号命名规则</li>
                    <li>• 详细的变更日志记录</li>
                    <li>• 代码审查机制</li>
                    <li>• 定期的版本发布节奏</li>
                  </ul>
                </div>
                <div className="space-y-4">
                  <h4 className="font-semibold">权限管理</h4>
                  <ul className="space-y-2 text-gray-600">
                    <li>• 主分支保护策略</li>
                    <li>• 分级权限控制</li>
                    <li>• 发布审批流程</li>
                    <li>• 回滚权限管理</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 实用工具推荐 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lightbulb className="w-5 h-5 mr-2 text-yellow-500" />
                实用工具推荐
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">PromptHub</h4>
                  <p className="text-sm text-gray-600 mb-2">内置版本控制功能</p>
                  <Badge variant="outline" className="text-xs">推荐</Badge>
                </div>
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">GitHub</h4>
                  <p className="text-sm text-gray-600 mb-2">专业Git托管服务</p>
                  <Badge variant="outline" className="text-xs">免费</Badge>
                </div>
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Notion</h4>
                  <p className="text-sm text-gray-600 mb-2">文档化版本管理</p>
                  <Badge variant="outline" className="text-xs">易用</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 行动建议 */}
          <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
            <CardHeader>
              <CardTitle className="text-purple-800">立即开始版本控制</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-purple-800 mb-2">今天就开始</h4>
                  <ul className="space-y-1 text-purple-700 text-sm">
                    <li>✓ 为现有提示词创建v1.0版本</li>
                    <li>✓ 建立版本命名规则</li>
                    <li>✓ 记录第一个变更日志</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-purple-800 mb-2">持续改进</h4>
                  <ul className="space-y-1 text-purple-700 text-sm">
                    <li>✓ 定期回顾版本效果</li>
                    <li>✓ 优化版本控制流程</li>
                    <li>✓ 分享最佳实践</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

        </div>

        {/* 底部导航 */}
        <div className="max-w-4xl mx-auto mt-12 pt-8 border-t">
          <div className="flex justify-between items-center">
            <Link href="/solutions">
              <Button variant="outline">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回指南列表
              </Button>
            </Link>
            <Link href="/solutions/ai-workflow-optimization">
              <Button>
                下一篇：AI工作流程优化
                <ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
    </>
  )
}

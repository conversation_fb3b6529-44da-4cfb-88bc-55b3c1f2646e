import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Target, MessageSquare, Database, FileText, ArrowRight } from 'lucide-react'

export const metadata: Metadata = {
  title: '提示词要素完全指南 - 掌握AI提示词的四大核心要素',
  description: '深入了解提示词的四大核心要素：指令、上下文、输入数据、输出指示，学会构建结构化的高效AI提示词。',
  keywords: [
    '提示词要素', '提示词结构', 'Prompt要素', 'AI指令设计',
    '提示词组成', '结构化提示词', '提示词模板', 'AI提示词教程',
    '指令设计', '上下文设置', '输入数据', '输出指示'
  ],
  openGraph: {
    title: '提示词要素完全指南 - 掌握AI提示词的四大核心要素',
    description: '深入了解提示词的四大核心要素：指令、上下文、输入数据、输出指示，学会构建结构化的高效AI提示词。',
    type: 'article',
    url: 'https://www.prompthub.xin/solutions/prompt-elements-guide',
    siteName: 'PromptHub',
    locale: 'zh_CN',
  },
  twitter: {
    card: 'summary_large_image',
    title: '提示词要素完全指南 - 掌握AI提示词的四大核心要素',
    description: '深入了解提示词的四大核心要素：指令、上下文、输入数据、输出指示，学会构建结构化的高效AI提示词。',
  },
  alternates: {
    canonical: 'https://www.prompthub.xin/solutions/prompt-elements-guide',
  },
}

export default function PromptElementsGuidePage() {
  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "提示词要素完全指南 - 掌握AI提示词的四大核心要素",
    "description": "深入了解提示词的四大核心要素：指令、上下文、输入数据、输出指示，学会构建结构化的高效AI提示词。",
    "url": "https://www.prompthub.xin/solutions/prompt-elements-guide",
    "datePublished": "2024-12-01",
    "dateModified": "2025-01-01",
    "author": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "publisher": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "https://www.prompthub.xin/solutions/prompt-elements-guide"
    },
    "articleSection": "提示工程指南",
    "keywords": "提示词要素, 提示词结构, AI指令设计, 结构化提示词",
    "educationalLevel": "基础必学",
    "timeRequired": "PT6M",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://www.prompthub.xin"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "提示工程指南",
          "item": "https://www.prompthub.xin/solutions"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "提示词要素完全指南",
          "item": "https://www.prompthub.xin/solutions/prompt-elements-guide"
        }
      ]
    },
    "mainEntity": {
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "提示词的四大核心要素是什么？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "提示词的四大核心要素包括：1)指令(Instruction) - 想要模型执行的特定任务；2)上下文(Context) - 外部信息或额外的上下文；3)输入数据(Input Data) - 想要找到答案的输入或问题；4)输出指示(Output Indicator) - 输出的类型或格式。"
          }
        },
        {
          "@type": "Question",
          "name": "是否所有提示词都需要包含四个要素？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "不是的。并非所有要素都是必须的，根据任务类型选择合适的要素组合是关键。简单任务可能只需要指令，复杂任务则需要更多要素配合。"
          }
        }
      ]
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link href="/solutions">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="w-4 h-4" />
              返回解决方案
            </Button>
          </Link>
        </div>

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
            <Target className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            提示词要素完全指南
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            深入了解提示词的四大核心要素，掌握结构化提示词设计的精髓
          </p>
          <div className="flex items-center justify-center gap-4 mt-6">
            <Badge variant="secondary">基础必学</Badge>
            <Badge variant="outline">6分钟阅读</Badge>
          </div>
        </div>

        {/* 核心内容 */}
        <div className="max-w-4xl mx-auto space-y-8">
          
          {/* 概述 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="w-5 h-5 text-blue-500" />
                什么是提示词要素？
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700 leading-relaxed">
                如果您接触过大量提示工程相关的示例和应用，您会注意到提示词是由一些要素组成的。
                理解这些要素有助于我们设计更有效的提示词，获得更精准的AI回答。
              </p>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-blue-800 font-medium">
                  💡 提示：并非所有要素都是必须的，根据任务类型选择合适的要素组合是关键。
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 四大要素 */}
          <div className="grid md:grid-cols-2 gap-6">
            <Card className="border-l-4 border-l-blue-500">
              <CardHeader>
                <CardTitle className="text-blue-600">1. 指令 (Instruction)</CardTitle>
                <CardDescription>想要模型执行的特定任务或指令</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-gray-600">
                    这是提示词的核心部分，明确告诉AI你希望它做什么。
                  </p>
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <strong>示例：</strong><br />
                    "请将文本分类为中性、否定或肯定"
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-green-500">
              <CardHeader>
                <CardTitle className="text-green-600">2. 上下文 (Context)</CardTitle>
                <CardDescription>包含外部信息或额外的上下文信息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-gray-600">
                    提供背景信息，引导语言模型更好地响应。
                  </p>
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <strong>示例：</strong><br />
                    "作为一名专业的情感分析师，基于以下标准进行分类..."
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-purple-500">
              <CardHeader>
                <CardTitle className="text-purple-600">3. 输入数据 (Input Data)</CardTitle>
                <CardDescription>用户输入的内容或问题</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-gray-600">
                    需要AI处理的具体内容或数据。
                  </p>
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <strong>示例：</strong><br />
                    "文本：我觉得食物还可以。"
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-orange-500">
              <CardHeader>
                <CardTitle className="text-orange-600">4. 输出指示 (Output Indicator)</CardTitle>
                <CardDescription>指定输出的类型或格式</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-gray-600">
                    明确指定期望的输出格式和结构。
                  </p>
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <strong>示例：</strong><br />
                    "情绪："
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 完整示例 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5 text-green-500" />
                完整示例分析
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-lg">
                <div className="space-y-4">
                  <div className="border-l-4 border-blue-500 pl-4">
                    <span className="text-blue-600 font-medium">指令：</span>
                    <span className="ml-2">请将文本分类为中性、否定或肯定</span>
                  </div>
                  <div className="border-l-4 border-purple-500 pl-4">
                    <span className="text-purple-600 font-medium">输入数据：</span>
                    <span className="ml-2">文本：我觉得食物还可以。</span>
                  </div>
                  <div className="border-l-4 border-orange-500 pl-4">
                    <span className="text-orange-600 font-medium">输出指示：</span>
                    <span className="ml-2">情绪：</span>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <p className="text-sm text-gray-600">
                    <strong>注意：</strong> 此基本示例不使用上下文，但也可以作为提示的一部分提供。
                    例如，可以提供其他示例来帮助模型更好地理解任务。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 实践建议 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5 text-purple-500" />
                实践建议
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold text-green-600">✅ 推荐做法</h4>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>根据任务复杂度选择合适的要素组合</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>保持指令清晰明确</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>提供相关的上下文信息</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>明确指定输出格式</span>
                    </li>
                  </ul>
                </div>
                <div className="space-y-4">
                  <h4 className="font-semibold text-red-600">❌ 避免做法</h4>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <span className="text-red-500 mt-1">•</span>
                      <span>指令过于模糊或复杂</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-red-500 mt-1">•</span>
                      <span>提供无关的上下文信息</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-red-500 mt-1">•</span>
                      <span>输入数据格式不清晰</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-red-500 mt-1">•</span>
                      <span>缺少必要的输出指示</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 下一步 */}
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
            <CardContent className="p-6">
              <div className="text-center">
                <h3 className="text-xl font-semibold mb-4">准备好进入下一阶段了吗？</h3>
                <p className="text-gray-600 mb-6">
                  掌握了提示词要素后，让我们学习更高级的设计技巧
                </p>
                <Link href="/solutions/prompt-design-techniques">
                  <Button className="gap-2">
                    学习设计技巧
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
    </>
  )
}

import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Zap, Brain, Target, CheckCircle, AlertCircle, ArrowRight } from 'lucide-react'

export const metadata: Metadata = {
  title: '思维链推理技巧 - 让AI像人类一样逐步思考',
  description: '掌握思维链推理技巧，让AI像人类一样逐步思考，解决复杂的推理和计算问题，提升AI的逻辑推理能力。',
  keywords: [
    '思维链推理', 'Chain of Thought', 'CoT', '逐步推理',
    '复杂推理', 'AI推理能力', '逻辑思维', '推理技巧',
    '自我一致性', '推理优化', '高级技巧', '复杂问题解决'
  ],
  openGraph: {
    title: '思维链推理技巧 - 让AI像人类一样逐步思考',
    description: '掌握思维链推理技巧，让AI像人类一样逐步思考，解决复杂的推理和计算问题。',
    type: 'article',
    url: 'https://www.prompthub.xin/solutions/chain-of-thought',
    siteName: 'PromptHub',
    locale: 'zh_CN',
  },
  twitter: {
    card: 'summary_large_image',
    title: '思维链推理技巧 - 让AI像人类一样逐步思考',
    description: '掌握思维链推理技巧，让AI像人类一样逐步思考，解决复杂的推理和计算问题。',
  },
  alternates: {
    canonical: 'https://www.prompthub.xin/solutions/chain-of-thought',
  },
}

export default function ChainOfThoughtPage() {
  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "思维链推理技巧 - 让AI像人类一样逐步思考",
    "description": "掌握思维链推理技巧，让AI像人类一样逐步思考，解决复杂的推理和计算问题，提升AI的逻辑推理能力。",
    "url": "https://www.prompthub.xin/solutions/chain-of-thought",
    "datePublished": "2024-12-01",
    "dateModified": "2025-01-01",
    "author": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "publisher": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "https://www.prompthub.xin/solutions/chain-of-thought"
    },
    "articleSection": "提示工程指南",
    "keywords": "思维链推理, Chain of Thought, 复杂推理, 高级技巧",
    "educationalLevel": "高级技巧",
    "timeRequired": "PT15M",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://www.prompthub.xin"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "提示工程指南",
          "item": "https://www.prompthub.xin/solutions"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "思维链推理技巧",
          "item": "https://www.prompthub.xin/solutions/chain-of-thought"
        }
      ]
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link href="/solutions">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="w-4 h-4" />
              返回解决方案
            </Button>
          </Link>
        </div>

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
            <Zap className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            思维链推理技巧
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            让AI像人类一样逐步思考，解决复杂的推理和计算问题
          </p>
          <div className="flex items-center justify-center gap-4 mt-6">
            <Badge variant="secondary">高级技巧</Badge>
            <Badge variant="outline">15分钟阅读</Badge>
          </div>
        </div>

        {/* 核心内容 */}
        <div className="max-w-4xl mx-auto space-y-8">
          
          {/* 概念介绍 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5 text-blue-500" />
                什么是思维链推理？
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700 leading-relaxed">
                思维链（Chain-of-Thought, CoT）提示是一种让AI模型展示其推理过程的技术。
                通过引导模型"一步一步思考"，我们可以显著提高其在复杂推理任务上的表现。
              </p>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-blue-800 font-medium">
                  🧠 核心思想：将复杂问题分解为一系列中间推理步骤，让AI展示完整的思考过程。
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 问题演示 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-red-500" />
                传统方法的局限性
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                让我们先看看传统提示方法在复杂推理任务上的表现：
              </p>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="space-y-3">
                  <div className="bg-white p-3 rounded border-l-4 border-red-500">
                    <p className="text-sm">
                      <strong>问题：</strong>这组数字中的奇数加起来是一个偶数：15、32、5、13、82、7、1。
                    </p>
                  </div>
                  <div className="bg-white p-3 rounded border-l-4 border-red-500">
                    <p className="text-sm">
                      <strong>错误回答：</strong>是的，这组数字中的奇数加起来是107，是一个偶数。
                    </p>
                  </div>
                  <p className="text-red-700 text-sm">
                    ❌ 模型没有展示推理过程，直接给出了错误答案（107是奇数，不是偶数）
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 思维链解决方案 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                思维链推理的解决方案
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                现在让我们使用思维链提示来解决同样的问题：
              </p>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="space-y-3">
                  <div className="bg-white p-3 rounded border-l-4 border-green-500">
                    <p className="text-sm">
                      <strong>改进提示：</strong>这组数字中的奇数加起来是一个偶数：15、32、5、13、82、7、1。<br />
                      请通过分解问题来解决。首先，识别奇数，然后相加，最后指出结果是奇数还是偶数。
                    </p>
                  </div>
                  <div className="bg-white p-3 rounded border-l-4 border-green-500">
                    <p className="text-sm">
                      <strong>正确回答：</strong><br />
                      奇数：15, 5, 13, 7, 1<br />
                      相加：15 + 5 + 13 + 7 + 1 = 41<br />
                      41是一个奇数。
                    </p>
                  </div>
                  <p className="text-green-700 text-sm">
                    ✅ 通过逐步推理，模型给出了正确的答案和完整的思考过程
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 数学推理示例 */}
          <Card>
            <CardHeader>
              <CardTitle>数学推理示例</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                思维链推理在数学问题上特别有效。以下是一个复杂的数学推理示例：
              </p>
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
                <div className="space-y-4">
                  <div className="bg-white p-4 rounded border-l-4 border-blue-500">
                    <p className="text-sm">
                      <strong>问题：</strong>咖啡店有一些苹果。如果他们用掉了一些来制作午餐，剩下11个苹果。
                      然后他们又买了6个苹果。现在他们有17个苹果。他们最初有多少个苹果？
                    </p>
                  </div>
                  <div className="bg-white p-4 rounded border-l-4 border-purple-500">
                    <p className="text-sm">
                      <strong>思维链推理：</strong><br />
                      让我们一步一步解决这个问题。<br />
                      1. 咖啡店用掉一些苹果后剩下11个<br />
                      2. 然后他们买了6个苹果<br />
                      3. 现在他们有17个苹果<br />
                      4. 所以在买6个苹果之前，他们有：17 - 6 = 11个苹果<br />
                      5. 这证实了他们用掉一些后确实剩下11个<br />
                      6. 设最初有x个苹果，用掉了y个，那么：x - y = 11<br />
                      7. 我们需要找到x的值<br />
                      8. 从"剩下11个，买了6个，现在有17个"可以验证：11 + 6 = 17 ✓<br />
                      9. 但我们还需要知道用掉了多少个才能求出最初的数量
                    </p>
                  </div>
                  <p className="text-sm text-gray-600">
                    这个例子展示了思维链如何帮助模型逐步分析问题，即使信息不完整也能进行合理推理。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 自我一致性 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="w-5 h-5 text-orange-500" />
                自我一致性技术
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                自我一致性是思维链推理的进阶技术，通过生成多个推理路径并选择最一致的答案来提高准确性。
              </p>
              <div className="bg-orange-50 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-3">示例：年龄问题</h4>
                <div className="space-y-3">
                  <div className="bg-white p-3 rounded">
                    <p className="text-sm">
                      <strong>问题：</strong>当我6岁时，我的妹妹是我的一半年龄。现在我70岁了，我的妹妹多大？
                    </p>
                  </div>
                  <div className="grid md:grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded border-l-2 border-green-500">
                      <p className="text-xs font-medium text-green-600">推理路径1：</p>
                      <p className="text-sm">当我6岁时，妹妹3岁。年龄差是3岁。现在我70岁，妹妹67岁。</p>
                    </div>
                    <div className="bg-white p-3 rounded border-l-2 border-green-500">
                      <p className="text-xs font-medium text-green-600">推理路径2：</p>
                      <p className="text-sm">妹妹比我小3岁。我现在70岁，所以妹妹70-3=67岁。</p>
                    </div>
                  </div>
                  <div className="bg-white p-3 rounded border-l-2 border-blue-500">
                    <p className="text-sm">
                      <strong>一致答案：</strong>67岁（多个推理路径得出相同结果，增加了答案的可信度）
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 应用场景 */}
          <Card>
            <CardHeader>
              <CardTitle>适用场景</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold text-green-600">✅ 特别适用</h4>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>数学计算和推理</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>逻辑推理问题</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>多步骤问题解决</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>常识推理</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>符号推理</span>
                    </li>
                  </ul>
                </div>
                <div className="space-y-4">
                  <h4 className="font-semibold text-orange-600">⚠️ 注意事项</h4>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <span className="text-orange-500 mt-1">•</span>
                      <span>可能增加响应长度</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-orange-500 mt-1">•</span>
                      <span>需要更多计算资源</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-orange-500 mt-1">•</span>
                      <span>简单任务可能过度复杂化</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-orange-500 mt-1">•</span>
                      <span>推理过程可能包含错误</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 实践技巧 */}
          <Card>
            <CardHeader>
              <CardTitle>实践技巧</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-3">关键提示词：</h4>
                  <div className="grid md:grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded text-sm">
                      <strong>基础版：</strong><br />
                      "让我们一步一步地思考"
                    </div>
                    <div className="bg-white p-3 rounded text-sm">
                      <strong>增强版：</strong><br />
                      "让我们一步一步地解决这个问题，以确保我们有正确的答案"
                    </div>
                  </div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-800 mb-3">结构化模板：</h4>
                  <div className="bg-white p-3 rounded text-sm font-mono">
                    问题：[具体问题]<br />
                    让我们分步骤解决：<br />
                    步骤1：[第一步分析]<br />
                    步骤2：[第二步分析]<br />
                    ...<br />
                    因此，答案是：[最终答案]
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 下一步 */}
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
            <CardContent className="p-6">
              <div className="text-center">
                <h3 className="text-xl font-semibold mb-4">恭喜！你已经掌握了高级提示技巧</h3>
                <p className="text-gray-600 mb-6">
                  现在你可以处理复杂的推理问题了。继续探索更多实用的解决方案吧！
                </p>
                <div className="flex gap-4 justify-center">
                  <Link href="/solutions">
                    <Button variant="outline" className="gap-2">
                      <ArrowLeft className="w-4 h-4" />
                      返回解决方案
                    </Button>
                  </Link>
                  <Link href="/prompts">
                    <Button className="gap-2">
                      探索提示词库
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
    </>
  )
}

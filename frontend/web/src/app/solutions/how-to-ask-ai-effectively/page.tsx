import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  CheckCircle,
  XCircle,
  Lightbulb,
  Target,
  MessageSquare,
  ArrowRight,
  ArrowLeft,
  BookOpen,
  Users
} from 'lucide-react'

export const metadata: Metadata = {
  title: '如何向AI提问更有效 - 5个黄金原则让AI真正理解你',
  description: '掌握向AI提问的核心技巧，学会写出让ChatGPT、Claude等AI模型惊艳的Prompt，避免常见提问误区，获得更精准的AI回答。',
  keywords: [
    '怎么向AI提问更有效', '如何写出让AI惊艳的Prompt', 
    'AI总是不理解我的意思怎么办', '提高ChatGPT回答质量的技巧',
    'AI对话技巧', 'Prompt写作教程', 'AI提问技巧',
    'ChatGPT使用技巧', 'Claude对话技巧', 'AI交互优化',
    'AI提问误区', 'AI沟通技巧', 'AI使用指南'
  ],
  openGraph: {
    title: '如何向AI提问更有效 - 5个黄金原则让AI真正理解你',
    description: '掌握向AI提问的核心技巧，学会写出让ChatGPT、Claude等AI模型惊艳的Prompt，避免常见提问误区。',
    type: 'article',
  },
}

const principles = [
  {
    title: '明确具体的目标',
    description: '告诉AI你想要什么样的结果，而不是让它猜测',
    icon: Target,
    good: '请帮我写一份500字的产品介绍，重点突出环保特性，目标受众是25-35岁的年轻父母',
    bad: '帮我写个产品介绍',
    tip: '越具体越好：字数、风格、受众、用途都要明确'
  },
  {
    title: '提供充足的背景信息',
    description: '给AI足够的上下文，让它理解你的具体情况',
    icon: BookOpen,
    good: '我是一名新手程序员，正在学习Python，请用简单易懂的语言解释什么是列表推导式，并给出3个实际应用例子',
    bad: '什么是列表推导式？',
    tip: '说明你的身份、知识水平、具体需求和期望的解释深度'
  },
  {
    title: '使用角色扮演',
    description: '让AI扮演专业角色，获得更专业的回答',
    icon: Users,
    good: '请你作为一名资深的数字营销专家，分析这个广告文案的优缺点，并提出3个具体的改进建议',
    bad: '这个广告文案怎么样？',
    tip: '明确AI的角色身份：专家、老师、顾问、助手等'
  },
  {
    title: '分步骤引导',
    description: '将复杂问题拆解成多个简单步骤',
    icon: CheckCircle,
    good: '请按以下步骤帮我制定学习计划：1)分析我的现状 2)设定学习目标 3)制定时间安排 4)推荐学习资源',
    bad: '帮我制定一个学习计划',
    tip: '复杂任务要分解，每个步骤都要清晰明确'
  },
  {
    title: '要求特定格式',
    description: '明确你希望得到什么样格式的回答',
    icon: MessageSquare,
    good: '请用表格形式对比iPhone和Android的优缺点，包含价格、性能、生态系统三个维度',
    bad: 'iPhone和Android哪个好？',
    tip: '指定输出格式：表格、列表、段落、代码等'
  }
]

const commonMistakes = [
  {
    mistake: '问题太模糊',
    example: '"帮我写点东西"',
    solution: '明确写什么、给谁看、多少字、什么风格'
  },
  {
    mistake: '一次问太多问题',
    example: '"什么是AI？怎么学编程？推荐几本书？"',
    solution: '一次专注解决一个问题，获得更好的回答'
  },
  {
    mistake: '没有提供背景',
    example: '"这个方案可行吗？"',
    solution: '说明具体方案内容、你的情况、判断标准'
  },
  {
    mistake: '期望AI读心术',
    example: '"你知道我想要什么"',
    solution: 'AI不能读心，要明确表达你的需求'
  }
]

export default function HowToAskAIEffectivelyPage() {
  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "如何向AI提问更有效 - 5个黄金原则让AI真正理解你",
    "description": "掌握向AI提问的核心技巧，学会写出让ChatGPT、Claude等AI模型惊艳的Prompt，避免常见提问误区，获得更精准的AI回答。",
    "url": "https://www.prompthub.xin/solutions/how-to-ask-ai-effectively",
    "datePublished": "2024-12-01",
    "dateModified": "2025-01-01",
    "author": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "publisher": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "https://www.prompthub.xin/solutions/how-to-ask-ai-effectively"
    },
    "articleSection": "提示工程指南",
    "keywords": "AI提问技巧, Prompt写作, ChatGPT使用技巧, AI对话优化",
    "educationalLevel": "新手入门",
    "timeRequired": "PT5M",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://www.prompthub.xin"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "提示工程指南",
          "item": "https://www.prompthub.xin/solutions"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "如何向AI提问更有效",
          "item": "https://www.prompthub.xin/solutions/how-to-ask-ai-effectively"
        }
      ]
    },
    "mainEntity": {
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "为什么AI总是不理解我的问题？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "AI不理解问题通常是因为问题过于模糊、缺乏背景信息或目标不明确。需要提供具体的目标、充足的背景信息和清晰的指令。"
          }
        },
        {
          "@type": "Question",
          "name": "如何让AI给出更准确的回答？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "遵循5个黄金原则：明确具体的目标、提供充足的背景信息、使用清晰的指令、给出具体的例子、设定合理的约束条件。"
          }
        }
      ]
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link href="/solutions">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="w-4 h-4" />
              返回解决方案
            </Button>
          </Link>
        </div>

        <div className="max-w-4xl mx-auto space-y-8">
          {/* 页面头部 */}
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
              <MessageSquare className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              如何向AI提问更有效
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              掌握这5个黄金原则，让ChatGPT、Claude等AI模型真正理解你的需求，
              获得更精准、更有用的回答。
            </p>
            <div className="flex items-center justify-center gap-4 mt-6">
              <Badge variant="secondary">新手入门</Badge>
              <Badge variant="outline">5分钟阅读</Badge>
            </div>
          </div>

      {/* 核心原则 */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center">
          <Lightbulb className="h-6 w-6 text-yellow-500 mr-2" />
          5个黄金原则
        </h2>
        
        {principles.map((principle, index) => {
          const Icon = principle.icon
          return (
            <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 text-white text-sm font-bold mr-3">
                    {index + 1}
                  </div>
                  <Icon className="h-5 w-5 text-purple-600 mr-2" />
                  {principle.title}
                </CardTitle>
                <CardDescription>
                  {principle.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* 好的例子 */}
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-green-800 mb-1">✅ 好的提问方式：</p>
                      <p className="text-green-700 text-sm leading-relaxed">"{principle.good}"</p>
                    </div>
                  </div>
                </div>
                
                {/* 不好的例子 */}
                <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                  <div className="flex items-start">
                    <XCircle className="h-5 w-5 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-red-800 mb-1">❌ 不好的提问方式：</p>
                      <p className="text-red-700 text-sm leading-relaxed">"{principle.bad}"</p>
                    </div>
                  </div>
                </div>
                
                {/* 小贴士 */}
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-blue-800 text-sm">
                    <span className="font-medium">💡 小贴士：</span> {principle.tip}
                  </p>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* 常见误区 */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-900">
          常见提问误区
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {commonMistakes.map((item, index) => (
            <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-3">
                <CardTitle className="text-base text-red-600 flex items-center">
                  <XCircle className="h-4 w-4 mr-2" />
                  {item.mistake}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="p-3 bg-red-50 rounded border border-red-200">
                  <p className="text-sm text-red-700">
                    <span className="font-medium">错误示例：</span> {item.example}
                  </p>
                </div>
                <div className="p-3 bg-green-50 rounded border border-green-200">
                  <p className="text-sm text-green-700">
                    <span className="font-medium">正确做法：</span> {item.solution}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* 实践建议 */}
      <Card className="border-0 bg-gradient-to-r from-purple-50 to-blue-50">
        <CardHeader>
          <CardTitle className="text-xl">立即开始实践</CardTitle>
          <CardDescription>
            理论学会了，现在就去实践吧！以下资源可以帮助你快速上手：
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link href="/prompts" className="block">
              <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                <BookOpen className="h-8 w-8 text-purple-600 mb-2" />
                <h3 className="font-medium mb-1">浏览提示词库</h3>
                <p className="text-sm text-gray-600">查看优质Prompt示例</p>
              </div>
            </Link>
            
            <Link href="/extension" className="block">
              <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                <Target className="h-8 w-8 text-blue-600 mb-2" />
                <h3 className="font-medium mb-1">安装浏览器插件</h3>
                <p className="text-sm text-gray-600">随时使用优质提示词</p>
              </div>
            </Link>
            
            <Link href="/solutions/manage-prompts-efficiently" className="block">
              <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                <Users className="h-8 w-8 text-green-600 mb-2" />
                <h3 className="font-medium mb-1">学习管理技巧</h3>
                <p className="text-sm text-gray-600">高效管理你的提示词</p>
              </div>
            </Link>
          </div>
        </CardContent>
      </Card>

          {/* 下一步 */}
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
            <CardContent className="p-6">
              <div className="text-center">
                <h3 className="text-xl font-semibold mb-4">准备学习更多技巧？</h3>
                <p className="text-gray-600 mb-6">
                  掌握了基础提问技巧后，让我们学习如何高效管理你的提示词库
                </p>
                <Link href="/solutions/manage-prompts-efficiently">
                  <Button className="gap-2">
                    学习管理技巧
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
    </>
  )
}

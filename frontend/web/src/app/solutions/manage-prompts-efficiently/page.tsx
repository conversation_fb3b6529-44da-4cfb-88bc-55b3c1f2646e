import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  FolderOpen,
  Tags,
  Search,
  Star,
  Clock,
  ArrowRight,
  ArrowLeft,
  BookOpen,
  Download,
  Share2,
  Zap
} from 'lucide-react'

export const metadata: Metadata = {
  title: '高效管理你的提示词库 - 告别混乱，让每个好用的Prompt都能快速找到',
  description: '学会建立个人提示词管理体系，掌握分类整理、版本控制、快速检索等技巧，让你的AI提示词库井井有条，随时调用。',
  keywords: [
    '保存和管理常用的AI提示词', '一键调用我的Prompt库',
    '跨浏览器同步AI提示词', '提示词版本控制工具',
    'AI提示词收藏夹', '如何记录我的AI灵感',
    '收藏的AI指令太乱怎么整理', 'AI工作流管理',
    '提示词管理技巧', 'Prompt库整理', 'AI效率工具',
    'PromptHub使用技巧', '提示词分类方法', 'AI助手管理'
  ],
  openGraph: {
    title: '高效管理你的提示词库 - 告别混乱，让每个好用的Prompt都能快速找到',
    description: '学会建立个人提示词管理体系，掌握分类整理、版本控制、快速检索等技巧。',
    type: 'article',
  },
}

const managementStrategies = [
  {
    title: '建立清晰的分类体系',
    description: '按用途、场景、AI模型等维度进行分类',
    icon: FolderOpen,
    steps: [
      '按工作场景分类：写作、编程、营销、设计等',
      '按AI模型分类：ChatGPT、Claude、Midjourney等',
      '按使用频率分类：常用、偶用、备用',
      '按完成度分类：成熟、测试中、待优化'
    ],
    tip: '建议使用2-3级分类，避免过度细分导致查找困难'
  },
  {
    title: '使用标签系统',
    description: '为每个提示词添加多个标签，支持交叉检索',
    icon: Tags,
    steps: [
      '功能标签：文案生成、代码审查、数据分析等',
      '难度标签：新手、进阶、专家级',
      '质量标签：精品、实用、实验性',
      '来源标签：原创、改编、收藏'
    ],
    tip: '每个提示词建议添加3-5个标签，便于多维度检索'
  },
  {
    title: '建立快速检索机制',
    description: '通过关键词、标签、分类快速找到需要的提示词',
    icon: Search,
    steps: [
      '使用描述性的标题，包含核心关键词',
      '在描述中记录使用场景和效果',
      '定期整理和更新标签',
      '建立个人检索习惯和快捷方式'
    ],
    tip: '好的命名比复杂的分类更重要，标题要一眼就能看懂用途'
  },
  {
    title: '实施版本控制',
    description: '追踪提示词的优化历程，保留有效版本',
    icon: Clock,
    steps: [
      '记录每次修改的原因和效果',
      '保留关键版本的备份',
      '标注最佳实践版本',
      '定期回顾和优化'
    ],
    tip: '不要删除旧版本，它们可能在特定场景下仍然有用'
  },
  {
    title: '建立收藏和评级系统',
    description: '标记高质量提示词，建立个人精品库',
    icon: Star,
    steps: [
      '五星评级：根据实用性和效果评分',
      '使用频率统计：记录使用次数',
      '效果反馈：记录使用后的实际效果',
      '定期评估：每月回顾和调整评级'
    ],
    tip: '重点维护5星提示词，它们是你的核心资产'
  }
]

const toolsAndFeatures = [
  {
    name: 'PromptHub 浏览器插件',
    description: '跨浏览器同步，随时随地访问你的提示词库',
    features: ['云端同步', '一键复制', '快速搜索', '分类管理'],
    icon: Download
  },
  {
    name: '团队协作功能',
    description: '与团队成员共享优质提示词，建立团队知识库',
    features: ['权限管理', '协作编辑', '版本历史', '使用统计'],
    icon: Share2
  },
  {
    name: '智能推荐系统',
    description: '基于使用习惯推荐相关提示词，发现新的可能性',
    features: ['个性化推荐', '相似提示词', '热门推荐', '趋势分析'],
    icon: Zap
  }
]

const bestPractices = [
  {
    title: '定期整理和清理',
    content: '每周花15分钟整理新收集的提示词，每月进行一次深度清理，删除无用的、合并重复的。'
  },
  {
    title: '记录使用场景',
    content: '在提示词描述中详细记录适用场景、预期效果、注意事项，方便日后选择使用。'
  },
  {
    title: '建立个人模板',
    content: '总结出适合自己的提示词模板，新建提示词时可以快速套用，提高一致性。'
  },
  {
    title: '持续优化迭代',
    content: '根据使用效果不断优化提示词，记录改进过程，形成个人最佳实践。'
  }
]

export default function ManagePromptsEfficientlyPage() {
  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "如何高效管理AI提示词库 - 从混乱到有序的完整指南",
    "description": "学会科学管理你的AI提示词库，建立分类体系、版本控制和评级系统，让优质Prompt成为你的长期资产。",
    "url": "https://www.prompthub.xin/solutions/manage-prompts-efficiently",
    "datePublished": "2024-12-01",
    "dateModified": "2025-01-01",
    "author": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "publisher": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "https://www.prompthub.xin/solutions/manage-prompts-efficiently"
    },
    "articleSection": "提示工程指南",
    "keywords": "提示词管理, Prompt库管理, AI工具使用, 知识管理",
    "educationalLevel": "进阶应用",
    "timeRequired": "PT8M",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://www.prompthub.xin"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "提示工程指南",
          "item": "https://www.prompthub.xin/solutions"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "高效管理提示词库",
          "item": "https://www.prompthub.xin/solutions/manage-prompts-efficiently"
        }
      ]
    },
    "mainEntity": {
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "如何建立有效的提示词分类体系？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "建议按照使用场景分类，如工作类、学习类、创作类等，每个大类下再细分具体用途，同时使用标签系统进行交叉分类。"
          }
        },
        {
          "@type": "Question",
          "name": "为什么需要对提示词进行版本控制？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "版本控制可以帮你追踪提示词的优化过程，记录每次修改的原因和效果，保留关键版本的备份，避免丢失有效的历史版本。"
          }
        }
      ]
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link href="/solutions">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="w-4 h-4" />
              返回解决方案
            </Button>
          </Link>
        </div>

        <div className="max-w-4xl mx-auto space-y-8">
          {/* 页面头部 */}
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
              <BookOpen className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              高效管理你的提示词库
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              建立个人提示词管理体系，告别混乱，让每个好用的Prompt都能快速找到
            </p>
            <div className="flex items-center justify-center gap-4 mt-6">
              <Badge variant="secondary">进阶技巧</Badge>
              <Badge variant="outline">8分钟阅读</Badge>
            </div>
          </div>

          {/* 管理策略 */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">
              5大管理策略
            </h2>

            {managementStrategies.map((strategy, index) => {
              const Icon = strategy.icon
              return (
                <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center text-lg">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm font-bold mr-3">
                        {index + 1}
                      </div>
                      <Icon className="h-5 w-5 text-blue-600 mr-2" />
                      {strategy.title}
                    </CardTitle>
                    <CardDescription>
                      {strategy.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      {strategy.steps.map((step, stepIndex) => (
                        <div key={stepIndex} className="flex items-start">
                          <div className="flex items-center justify-center w-5 h-5 rounded-full bg-blue-100 text-blue-600 text-xs font-medium mr-3 mt-0.5 flex-shrink-0">
                            {stepIndex + 1}
                          </div>
                          <p className="text-sm text-gray-700">{step}</p>
                        </div>
                      ))}
                    </div>

                    <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <p className="text-blue-800 text-sm">
                        <span className="font-medium">💡 专家建议：</span> {strategy.tip}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* 工具推荐 */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">
              推荐工具和功能
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {toolsAndFeatures.map((tool, index) => {
                const Icon = tool.icon
                return (
                  <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="pb-4">
                      <div className="p-3 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 text-white w-fit">
                        <Icon className="h-6 w-6" />
                      </div>
                      <CardTitle className="text-lg">{tool.name}</CardTitle>
                      <CardDescription className="text-sm">
                        {tool.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent>
                      <ul className="space-y-1">
                        {tool.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                            <div className="w-1.5 h-1.5 rounded-full bg-purple-500 mr-2"></div>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>

          {/* 最佳实践 */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">
              最佳实践建议
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {bestPractices.map((practice, index) => (
                <Card key={index} className="border-0 bg-gradient-to-br from-green-50 to-blue-50">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base text-green-700">
                      {practice.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {practice.content}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* 立即开始 */}
          <Card className="border-0 bg-gradient-to-r from-purple-50 to-blue-50">
            <CardHeader>
              <CardTitle className="text-xl">立即开始整理你的提示词库</CardTitle>
              <CardDescription>
                现在就开始建立你的个人提示词管理体系：
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Link href="/extension" className="block">
                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <Download className="h-8 w-8 text-purple-600 mb-2" />
                    <h3 className="font-medium mb-1">安装浏览器插件</h3>
                    <p className="text-sm text-gray-600">开始云端同步管理</p>
                  </div>
                </Link>

                <Link href="/my-prompts" className="block">
                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <BookOpen className="h-8 w-8 text-blue-600 mb-2" />
                    <h3 className="font-medium mb-1">整理现有提示词</h3>
                    <p className="text-sm text-gray-600">分类标记你的收藏</p>
                  </div>
                </Link>

                <Link href="/solutions/team-prompt-sharing" className="block">
                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <Share2 className="h-8 w-8 text-green-600 mb-2" />
                    <h3 className="font-medium mb-1">学习团队协作</h3>
                    <p className="text-sm text-gray-600">与团队共享知识</p>
                  </div>
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* 下一步 */}
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
            <CardContent className="p-6">
              <div className="text-center">
                <h3 className="text-xl font-semibold mb-4">准备探索更多解决方案？</h3>
                <p className="text-gray-600 mb-6">
                  掌握了管理技巧后，让我们学习更高级的提示词工程技术
                </p>
                <div className="flex gap-4 justify-center">
                  <Link href="/solutions/prompt-elements-guide">
                    <Button variant="outline" className="gap-2">
                      提示词要素
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  </Link>
                  <Link href="/solutions/few-shot-prompting">
                    <Button className="gap-2">
                      少样本技巧
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
    </>
  )
}

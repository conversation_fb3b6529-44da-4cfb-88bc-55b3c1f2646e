import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Lightbulb, Target, Zap, AlertTriangle, ArrowRight } from 'lucide-react'

export const metadata: Metadata = {
  title: '少样本提示技巧 - 通过示例引导AI获得更好效果',
  description: '掌握少样本学习技巧，通过提供示例来引导AI，学会从零样本到少样本的进阶技巧，提升AI回答质量。',
  keywords: [
    '少样本提示', '少样本学习', 'Few-shot Prompting', '示例引导',
    '零样本提示', 'Zero-shot', '样本设计', 'AI示例学习',
    '提示词示例', '模式识别', '示例优化', '进阶技巧'
  ],
  openGraph: {
    title: '少样本提示技巧 - 通过示例引导AI获得更好效果',
    description: '掌握少样本学习技巧，通过提供示例来引导AI，学会从零样本到少样本的进阶技巧。',
    type: 'article',
    url: 'https://www.prompthub.xin/solutions/few-shot-prompting',
    siteName: 'PromptHub',
    locale: 'zh_CN',
  },
  twitter: {
    card: 'summary_large_image',
    title: '少样本提示技巧 - 通过示例引导AI获得更好效果',
    description: '掌握少样本学习技巧，通过提供示例来引导AI，学会从零样本到少样本的进阶技巧。',
  },
  alternates: {
    canonical: 'https://www.prompthub.xin/solutions/few-shot-prompting',
  },
}

export default function FewShotPromptingPage() {
  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "少样本提示技巧 - 通过示例引导AI获得更好效果",
    "description": "掌握少样本学习技巧，通过提供示例来引导AI，学会从零样本到少样本的进阶技巧，提升AI回答质量。",
    "url": "https://www.prompthub.xin/solutions/few-shot-prompting",
    "datePublished": "2024-12-01",
    "dateModified": "2025-01-01",
    "author": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "publisher": {
      "@type": "Organization",
      "name": "PromptHub",
      "url": "https://www.prompthub.xin"
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "https://www.prompthub.xin/solutions/few-shot-prompting"
    },
    "articleSection": "提示工程指南",
    "keywords": "少样本提示, 少样本学习, 示例引导, 进阶技巧",
    "educationalLevel": "进阶技巧",
    "timeRequired": "PT12M",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://www.prompthub.xin"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "提示工程指南",
          "item": "https://www.prompthub.xin/solutions"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "少样本提示技巧",
          "item": "https://www.prompthub.xin/solutions/few-shot-prompting"
        }
      ]
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link href="/solutions">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="w-4 h-4" />
              返回解决方案
            </Button>
          </Link>
        </div>

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
            <Lightbulb className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            少样本提示技巧
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            通过提供示例来引导AI，掌握零样本到少样本的进阶技巧
          </p>
          <div className="flex items-center justify-center gap-4 mt-6">
            <Badge variant="secondary">进阶技巧</Badge>
            <Badge variant="outline">12分钟阅读</Badge>
          </div>
        </div>

        {/* 核心内容 */}
        <div className="max-w-4xl mx-auto space-y-8">
          
          {/* 概念介绍 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-blue-500" />
                什么是少样本提示？
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700 leading-relaxed">
                虽然大型语言模型展示了惊人的零样本能力，但在使用零样本设置时，它们在更复杂的任务上仍然表现不佳。
                少样本提示可以作为一种技术，以启用上下文学习，我们在提示中提供演示以引导模型实现更好的性能。
              </p>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-blue-800 font-medium">
                  💡 核心思想：演示作为后续示例的条件，引导模型生成更准确的响应。
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 零样本 vs 少样本对比 */}
          <div className="grid md:grid-cols-2 gap-6">
            <Card className="border-l-4 border-l-orange-500">
              <CardHeader>
                <CardTitle className="text-orange-600">零样本提示</CardTitle>
                <CardDescription>不提供示例，直接给出指令</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <strong>提示：</strong><br />
                    将文本分类为中性、负面或正面。<br />
                    文本：我认为这次假期还可以。<br />
                    情感：
                  </div>
                  <div className="bg-white p-3 rounded border text-sm">
                    <strong>输出：</strong> 中性
                  </div>
                  <p className="text-xs text-gray-600">
                    适用于简单任务，但复杂任务可能表现不佳
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-green-500">
              <CardHeader>
                <CardTitle className="text-green-600">少样本提示</CardTitle>
                <CardDescription>提供示例来引导模型</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <strong>提示：</strong><br />
                    这太棒了！// 正面<br />
                    这太糟糕了！// 负面<br />
                    哇，那部电影太棒了！//
                  </div>
                  <div className="bg-white p-3 rounded border text-sm">
                    <strong>输出：</strong> 正面
                  </div>
                  <p className="text-xs text-gray-600">
                    通过示例引导，获得更准确的结果
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 经典示例 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-purple-500" />
                经典示例：新词学习
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                以下是Brown等人2020年提出的一个经典例子，展示了少样本提示在新词学习任务中的应用：
              </p>
              <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg">
                <div className="space-y-4">
                  <div className="bg-white p-4 rounded border-l-4 border-purple-500">
                    <p className="text-sm">
                      <strong>示例提供：</strong><br />
                      "whatpu"是坦桑尼亚的一种小型毛茸茸的动物。一个使用whatpu这个词的句子的例子是：<br />
                      我们在非洲旅行时看到了这些非常可爱的whatpus。
                    </p>
                  </div>
                  <div className="bg-white p-4 rounded border-l-4 border-blue-500">
                    <p className="text-sm">
                      <strong>任务提示：</strong><br />
                      "farduddle"是指快速跳上跳下。一个使用farduddle这个词的句子的例子是：
                    </p>
                  </div>
                  <div className="bg-white p-4 rounded border-l-4 border-green-500">
                    <p className="text-sm">
                      <strong>模型输出：</strong><br />
                      当我们赢得比赛时，我们都开始庆祝跳跃。
                    </p>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mt-4">
                  通过提供一个示例（1-shot），模型学会了如何在新的上下文中使用未知词汇。
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 重要发现 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-blue-500" />
                研究发现
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                根据Min等人（2022）的研究结果，以下是在进行少样本学习时关于演示/范例的一些重要发现：
              </p>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-800 mb-2">关键因素：</h4>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• 标签空间和输入文本的分布都很重要</li>
                      <li>• 使用的格式对性能起着关键作用</li>
                      <li>• 即使使用随机标签也比没有标签好</li>
                    </ul>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-green-800 mb-2">优化建议：</h4>
                    <ul className="text-sm text-green-700 space-y-1">
                      <li>• 从真实标签分布中选择随机标签</li>
                      <li>• 保持一致的格式结构</li>
                      <li>• 增加演示数量（3-shot、5-shot等）</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 格式实验 */}
          <Card>
            <CardHeader>
              <CardTitle>格式的重要性</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                即使标签被随机化，保持格式的一致性仍然能帮助模型产生正确的输出：
              </p>
              <div className="space-y-4">
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-yellow-800 mb-2">随机标签示例：</h4>
                  <div className="text-sm font-mono bg-white p-3 rounded">
                    这太棒了！// Negative<br />
                    这太糟糕了！// Positive<br />
                    哇，那部电影太棒了！// Positive<br />
                    多么可怕的节目！//
                  </div>
                  <p className="text-xs text-yellow-700 mt-2">
                    输出：Negative（仍然是正确的！）
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-800 mb-2">不一致格式：</h4>
                  <div className="text-sm font-mono bg-white p-3 rounded">
                    Positive This is awesome!<br />
                    This is bad! Negative<br />
                    Wow that movie was rad!<br />
                    Positive<br />
                    What a horrible show! --
                  </div>
                  <p className="text-xs text-gray-700 mt-2">
                    输出：Negative（新模型对格式变化更加稳健）
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 限制和挑战 */}
          <Card className="border-l-4 border-l-red-500">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600">
                <AlertTriangle className="w-5 h-5" />
                少样本提示的限制
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                标准的少样本提示对许多任务都有效，但仍然不是一种完美的技术，特别是在处理更复杂的推理任务时。
              </p>
              <div className="bg-red-50 p-4 rounded-lg">
                <h4 className="font-semibold text-red-800 mb-2">挑战示例：</h4>
                <div className="text-sm space-y-2">
                  <div className="bg-white p-3 rounded">
                    <strong>问题：</strong>这组数字中的奇数加起来是一个偶数：15、32、5、13、82、7、1。
                  </div>
                  <div className="bg-white p-3 rounded">
                    <strong>错误输出：</strong>是的，这组数字中的奇数加起来是107，是一个偶数。
                  </div>
                  <p className="text-red-700 text-xs">
                    即使提供多个示例，模型在复杂推理任务上仍可能失败
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 实践建议 */}
          <Card>
            <CardHeader>
              <CardTitle>实践建议</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold text-green-600">✅ 最佳实践</h4>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>从1-shot开始，逐步增加示例数量</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>保持示例格式的一致性</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>选择代表性的示例</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>测试不同的示例组合</span>
                    </li>
                  </ul>
                </div>
                <div className="space-y-4">
                  <h4 className="font-semibold text-red-600">❌ 常见误区</h4>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <span className="text-red-500 mt-1">•</span>
                      <span>示例过于复杂或不相关</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-red-500 mt-1">•</span>
                      <span>格式不一致</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-red-500 mt-1">•</span>
                      <span>示例数量过多导致混乱</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-red-500 mt-1">•</span>
                      <span>忽略标签分布的重要性</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 下一步 */}
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
            <CardContent className="p-6">
              <div className="text-center">
                <h3 className="text-xl font-semibold mb-4">准备迎接更大的挑战？</h3>
                <p className="text-gray-600 mb-6">
                  当少样本提示不足以解决复杂推理问题时，思维链提示技术可以帮助你
                </p>
                <Link href="/solutions/chain-of-thought">
                  <Button className="gap-2">
                    学习思维链推理
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
    </>
  )
}

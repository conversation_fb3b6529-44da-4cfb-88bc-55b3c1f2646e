import { Metadata } from 'next'

export const metadata: Metadata = {
  title: {
    default: 'AI提示词工程指南 - 免费提示词设计教程 | PromptHub',
    template: '%s | PromptHub AI提示词工程'
  },
  description: '专业的AI提示词工程指南，提供免费提示词设计教程，涵盖ChatGPT、Claude等AI工具的提示词技巧，助您掌握提示词工程，提升AI使用效率。',
  keywords: [
    // 核心提示工程关键词
    '提示工程', '提示词设计', 'Prompt Engineering', 'AI提示词技巧',
    
    // 提问优化类
    '怎么向AI提问更有效', '如何写出让AI惊艳的Prompt', 
    'AI总是不理解我的意思怎么办', '提高ChatGPT回答质量的技巧',
    'AI对话技巧', 'Prompt写作教程',
    
    // 效率提升类  
    '保存和管理常用的AI提示词', '一键调用我的Prompt库',
    '跨浏览器同步AI提示词', '团队共享Prompt最佳实践',
    '提示词版本控制工具', 'AI工作流管理',
    
    // "我忘了"场景
    '之前用过的神仙Prompt找不到了', '如何记录我的AI灵感',
    '收藏的AI指令太乱怎么整理', 'AI提示词收藏夹',
    
    // 问题解决类
    'AI使用常见问题', 'AI工具使用教程', 'AI效率提升方法'
  ],
  openGraph: {
    title: '提示工程指南 - PromptHub',
    description: '专业的提示工程指南，帮助您掌握AI提示词设计技巧，提升AI交互效率和质量。',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: '提示工程指南 - PromptHub',
    description: '专业的提示工程指南，帮助您掌握AI提示词设计技巧，提升AI交互效率和质量。',
  },
}

export default function SolutionsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {children}
      </div>
    </div>
  )
}

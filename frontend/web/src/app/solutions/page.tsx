import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  MessageSquare,
  BookOpen,
  Users,
  Zap,
  Brain,
  Target,
  Lightbulb,
  ArrowRight,
  CheckCircle
} from 'lucide-react'

const solutions = [
  {
    id: 'how-to-ask-ai-effectively',
    title: '如何向AI提问更有效',
    description: '掌握AI对话的核心技巧，让AI真正理解你的需求，获得更精准的回答',
    icon: MessageSquare,
    difficulty: '新手入门',
    readTime: '5分钟',
    tags: ['提问技巧', 'AI对话', '新手必读'],
    highlights: [
      '5个黄金提问原则',
      '常见提问误区避免',
      '实战案例分析'
    ]
  },
  {
    id: 'prompt-elements-guide',
    title: '提示词要素完全指南',
    description: '深入了解提示词的四大核心要素：指令、上下文、输入数据、输出指示',
    icon: Target,
    difficulty: '基础必学',
    readTime: '6分钟',
    tags: ['提示词要素', '结构化', '基础知识'],
    highlights: [
      '四大核心要素详解',
      '要素组合最佳实践',
      '实际应用案例'
    ]
  },
  {
    id: 'prompt-design-techniques',
    title: '提示词设计通用技巧',
    description: '从简单开始，逐步优化，掌握具体性、明确性等关键设计原则',
    icon: Brain,
    difficulty: '进阶技巧',
    readTime: '10分钟',
    tags: ['设计技巧', '优化方法', '最佳实践'],
    highlights: [
      '从简单到复杂的迭代方法',
      '具体性和明确性原则',
      '避免常见设计陷阱'
    ]
  },
  {
    id: 'manage-prompts-efficiently',
    title: '高效管理你的提示词库',
    description: '建立个人提示词管理体系，告别混乱，让每个好用的Prompt都能快速找到',
    icon: BookOpen,
    difficulty: '进阶技巧',
    readTime: '8分钟',
    tags: ['提示词管理', '效率提升', '工具使用'],
    highlights: [
      '分类整理方法',
      '版本控制技巧',
      'PromptHub插件使用'
    ]
  },
  {
    id: 'few-shot-prompting',
    title: '少样本提示技巧',
    description: '通过提供示例来引导AI，掌握零样本到少样本的进阶技巧',
    icon: Lightbulb,
    difficulty: '进阶技巧',
    readTime: '12分钟',
    tags: ['少样本学习', '示例引导', '进阶技巧'],
    highlights: [
      '零样本vs少样本对比',
      '示例选择和设计',
      '性能优化策略'
    ]
  },
  {
    id: 'chain-of-thought',
    title: '思维链推理技巧',
    description: '让AI像人类一样逐步思考，解决复杂的推理和计算问题',
    icon: Zap,
    difficulty: '高级技巧',
    readTime: '15分钟',
    tags: ['思维链', '推理能力', '复杂问题'],
    highlights: [
      '思维链提示原理',
      '复杂推理问题解决',
      '自我一致性技巧'
    ]
  },
  {
    id: 'team-prompt-sharing',
    title: '团队提示词协作最佳实践',
    description: '企业级提示词管理方案，让团队成员共享优质Prompt，提升整体工作效率',
    icon: Users,
    difficulty: '团队协作',
    readTime: '10分钟',
    tags: ['团队协作', '企业应用', '知识管理'],
    highlights: [
      '团队共享策略',
      '权限管理方案',
      '协作流程设计'
    ]
  },
  {
    id: 'prompt-version-control',
    title: '提示词版本控制指南',
    description: '像管理代码一样管理你的提示词，追踪变更历史，优化迭代过程',
    icon: Target,
    difficulty: '专业技能',
    readTime: '12分钟',
    tags: ['版本控制', '专业技能', '优化迭代'],
    highlights: [
      '版本管理策略',
      '变更追踪方法',
      'A/B测试技巧'
    ]
  },
  {
    id: 'ai-workflow-optimization',
    title: 'AI工作流程优化',
    description: '构建高效的AI辅助工作流程，让AI真正成为你的生产力倍增器',
    icon: Zap,
    difficulty: '效率专家',
    readTime: '20分钟',
    tags: ['工作流程', '效率优化', '生产力'],
    highlights: [
      '工作流设计原则',
      '自动化集成',
      '效果评估方法'
    ]
  }
]

const difficultyColors = {
  '新手入门': 'bg-green-100 text-green-800',
  '进阶技巧': 'bg-blue-100 text-blue-800',
  '团队协作': 'bg-purple-100 text-purple-800',
  '综合提升': 'bg-orange-100 text-orange-800',
  '专业技能': 'bg-red-100 text-red-800',
  '效率专家': 'bg-indigo-100 text-indigo-800'
}

export default function SolutionsPage() {
  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "提示工程指南",
    "description": "专业的提示工程指南，帮助您掌握AI提示词设计的核心技巧，从基础概念到高级应用，让您成为提示工程专家。",
    "url": "https://www.prompthub.xin/solutions",
    "mainEntity": {
      "@type": "ItemList",
      "name": "提示工程指南列表",
      "description": "涵盖从基础到高级的提示工程技巧",
      "itemListElement": solutions.map((solution, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "Article",
          "name": solution.title,
          "description": solution.description,
          "url": `https://www.prompthub.xin/solutions/${solution.id}`,
          "keywords": solution.tags.join(", "),
          "educationalLevel": solution.difficulty,
          "timeRequired": solution.readTime
        }
      }))
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://www.prompthub.xin"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "提示工程指南",
          "item": "https://www.prompthub.xin/solutions"
        }
      ]
    },
    "about": {
      "@type": "Thing",
      "name": "提示工程",
      "description": "设计和优化AI提示词的技术和方法"
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="space-y-8">
      {/* 页面头部 */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          AI提示词工程指南
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          免费AI提示词工程教程，掌握ChatGPT、Claude等AI工具的提示词设计技巧，
          从基础提示词要素到高级提示词优化，助您成为提示词工程专家。
        </p>
      </div>

      {/* 解决方案网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {solutions.map((solution) => {
          const Icon = solution.icon
          return (
            <Card key={solution.id} className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 text-white">
                    <Icon className="h-5 w-5" />
                  </div>
                  <Badge 
                    variant="secondary" 
                    className={`${difficultyColors[solution.difficulty as keyof typeof difficultyColors]} border-0`}
                  >
                    {solution.difficulty}
                  </Badge>
                </div>
                <CardTitle className="text-lg group-hover:text-purple-600 transition-colors">
                  {solution.title}
                </CardTitle>
                <CardDescription className="text-sm text-gray-600 leading-relaxed">
                  {solution.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="pt-0">
                {/* 亮点列表 */}
                <div className="mb-4">
                  <ul className="space-y-1">
                    {solution.highlights.map((highlight, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                        {highlight}
                      </li>
                    ))}
                  </ul>
                </div>
                
                {/* 标签 */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {solution.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                {/* 底部信息 */}
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">
                    阅读时间: {solution.readTime}
                  </span>
                  <Link 
                    href={`/solutions/${solution.id}`}
                    className="inline-flex items-center text-sm font-medium text-purple-600 hover:text-purple-700 transition-colors"
                  >
                    查看详情
                    <ArrowRight className="h-3 w-3 ml-1" />
                  </Link>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
      
      {/* 底部CTA */}
      <div className="text-center py-12">
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 text-white">
          <h2 className="text-2xl font-bold mb-4">
            还有问题？我们来帮你解决
          </h2>
          <p className="text-purple-100 mb-6 max-w-2xl mx-auto">
            如果以上解决方案还不能解决你的问题，欢迎加入我们的社区，
            与其他AI爱好者交流经验，获得更多帮助。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/prompts"
              className="inline-flex items-center px-6 py-3 bg-white text-purple-600 rounded-lg font-medium hover:bg-gray-50 transition-colors"
            >
              浏览提示词库
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
            <Link 
              href="/extension"
              className="inline-flex items-center px-6 py-3 bg-purple-500 text-white rounded-lg font-medium hover:bg-purple-400 transition-colors"
            >
              下载浏览器插件
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </div>
        </div>
      </div>
    </div>
    </>
  )
}

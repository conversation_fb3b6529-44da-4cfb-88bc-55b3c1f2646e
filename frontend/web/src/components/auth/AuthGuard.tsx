'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'

interface AuthGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  redirectTo?: string
}

/**
 * 认证保护组件
 * 用于保护需要登录才能访问的页面
 */
export default function AuthGuard({
  children,
  fallback,
  redirectTo = '/auth/login'
}: AuthGuardProps) {
  const { user, loading } = useAuth() as { user: any; loading: boolean }
  const router = useRouter()

  // 添加调试日志
  console.log('🛡️ AuthGuard状态:', {
    hasUser: !!user,
    loading,
    username: user?.username
  })

  useEffect(() => {
    // 只有在认证状态加载完成且用户未登录时才重定向
    if (!loading && !user) {
      console.log('🚫 用户未登录，重定向到登录页面')
      router.push(redirectTo)
    }
  }, [user, loading, router, redirectTo])

  // 正在加载认证状态
  if (loading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
            <p className="text-gray-600">验证登录状态...</p>
          </div>
        </div>
      )
    )
  }

  // 用户未登录，显示加载状态（同时进行重定向）
  if (!user) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
            <p className="text-gray-600">跳转到登录页面...</p>
          </div>
        </div>
      )
    )
  }

  // 用户已登录，显示受保护的内容
  return <>{children}</>
} 
'use client'

import React from 'react'
import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'

interface BreadcrumbItem {
  label: string
  href?: string
  current?: boolean
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
  className?: string
}

export function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  return (
    <nav 
      aria-label="面包屑导航" 
      className={`flex items-center space-x-1 text-sm text-gray-600 ${className}`}
    >
      {/* 首页链接 */}
      <Link 
        href="/" 
        className="flex items-center hover:text-violet-600 transition-colors"
        title="返回首页"
      >
        <Home className="w-4 h-4" />
        <span className="sr-only">首页</span>
      </Link>
      
      {items.map((item, index) => (
        <React.Fragment key={index}>
          <ChevronRight className="w-4 h-4 text-gray-400" />
          {item.href && !item.current ? (
            <Link 
              href={item.href}
              className="hover:text-violet-600 transition-colors"
              title={item.label}
            >
              {item.label}
            </Link>
          ) : (
            <span 
              className={item.current ? 'text-gray-900 font-medium' : 'text-gray-600'}
              aria-current={item.current ? 'page' : undefined}
            >
              {item.label}
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  )
}

// JSON-LD 结构化数据生成器
export function generateBreadcrumbJsonLd(items: BreadcrumbItem[], baseUrl: string) {
  const breadcrumbList = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.label,
      ...(item.href && { "item": `${baseUrl}${item.href}` })
    }))
  };
  
  return breadcrumbList;
}

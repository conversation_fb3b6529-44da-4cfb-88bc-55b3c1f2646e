'use client'

import { Button } from '@/components/ui/button'
import { ReactNode } from 'react'

interface PromptLinkButtonProps {
  promptId: string
  children: ReactNode
  className?: string
  size?: 'sm' | 'default' | 'lg'
  variant?: 'default' | 'outline' | 'ghost' | 'destructive' | 'secondary'
}

export default function PromptLinkButton({ 
  promptId, 
  children, 
  className,
  size = 'default',
  variant = 'default'
}: PromptLinkButtonProps) {
  const handleClick = () => {
    window.open(`/prompts/${promptId}`, '_blank')
  }

  return (
    <Button 
      onClick={handleClick}
      className={className}
      size={size}
      variant={variant}
    >
      {children}
    </Button>
  )
}

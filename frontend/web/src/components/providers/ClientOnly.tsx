'use client'

import { useState, useEffect } from 'react'

interface ClientOnlyProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

/**
 * 客户端专用组件包装器
 * 解决Next.js SSR/CSR水合不匹配的问题
 * 特别是涉及localStorage等浏览器专用API时
 */
export default function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  // 在服务端渲染或客户端首次加载时显示fallback
  if (!hasMounted) {
    return <>{fallback}</>
  }

  // 客户端水合完成后显示实际内容
  return <>{children}</>
} 
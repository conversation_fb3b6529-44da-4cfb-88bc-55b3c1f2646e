'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { 
  Clock, 
  TrendingUp, 
  Heart,
  Sparkles
} from "lucide-react"
import { api } from '@/lib/api'
import { PromptCard } from '@/components/prompt/PromptCard'

interface Prompt {
  id: number
  title: string
  description: string
  category: string
  categoryDisplayName?: string
  tags: string[]
  content: string
  userId?: number
  likes: number
  downloads: number
  views: number
  createdAt: string
  updatedAt: string
  status?: string
  isPrivate?: boolean
  authorName?: string
}

export default function WaterfallPrompts() {
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('recent')
  const [displayCount, setDisplayCount] = useState(6) // 初始显示6个
  const [isLoadingMore, setIsLoadingMore] = useState(false)

  useEffect(() => {
    fetchPrompts()
  }, [])

  const fetchPrompts = async () => {
    try {
      setLoading(true)
      setError('')
      
      // 获取认证token（如果有）
      const token = localStorage.getItem('authToken');
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加认证头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await fetch(api.prompts.public, {
        headers
      });
      
      if (!response.ok) {
        throw new Error('获取提示词失败')
      }

      const data = await response.json()
      
      if (data.success) {
        // 转换数据格式 - tags 已经在后端解析过了
        const formattedPrompts = data.data.map((prompt: any) => ({
          ...prompt,
          tags: Array.isArray(prompt.tags) ? prompt.tags : JSON.parse(prompt.tags || '[]')
        }))
        setPrompts(formattedPrompts)
      } else {
        throw new Error(data.message || '获取提示词失败')
      }

    } catch (error) {
      console.error('获取提示词失败:', error)
      setError(error instanceof Error ? error.message : '获取提示词失败')
      setPrompts([]) // 清空数据而不是使用模拟数据
    } finally {
      setLoading(false)
    }
  }

  const handleUpdate = (updatedPrompt: Prompt) => {
    setPrompts(prevPrompts => 
      prevPrompts.map(p => 
        p.id === updatedPrompt.id ? updatedPrompt : p
      )
    )
  }

  const getFilteredPrompts = () => {
    if (prompts.length === 0) return []
    
    switch (activeTab) {
      case 'popular':
        // 热门提示词：按点赞数排序
        return [...prompts].sort((a, b) => (b.likes || 0) - (a.likes || 0))
      case 'trending':
        // 趋势提示词：按下载数排序
        return [...prompts].sort((a, b) => (b.downloads || 0) - (a.downloads || 0))
      case 'recent':
      default:
        // 最新提示词：按创建时间排序
        return [...prompts].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    }
  }

  const loadMorePrompts = () => {
    setIsLoadingMore(true)
    // 模拟加载延迟
    setTimeout(() => {
      setDisplayCount(prev => prev + 6) // 每次加载6个
      setIsLoadingMore(false)
    }, 800)
  }

  const renderPromptColumns = () => {
    const filteredPrompts = getFilteredPrompts()
    const displayedPrompts = filteredPrompts.slice(0, displayCount) // 只显示指定数量
    
    if (displayedPrompts.length === 0) {
      return (
        <div className="col-span-full text-center py-16">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Sparkles className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无提示词</h3>
          <p className="text-gray-600 mb-6">目前还没有提示词，请稍后再来看看</p>
        </div>
      )
    }

    // 将提示词分成3列
    const columns = [[], [], []] as Prompt[][]
    displayedPrompts.forEach((prompt, index) => {
      columns[index % 3].push(prompt)
    })

    return (
      <>
        {columns.map((columnPrompts, columnIndex) => (
          <div key={columnIndex} className="space-y-6">
            {columnPrompts.map((prompt) => (
              <PromptCard 
                key={prompt.id} 
                prompt={prompt} 
              />
            ))}
          </div>
        ))}
        
        {/* 加载更多按钮 */}
        {displayCount < filteredPrompts.length && (
          <div className="col-span-full flex justify-center mt-8">
            <Button
              onClick={loadMorePrompts}
              disabled={isLoadingMore}
              variant="outline"
              className="flex items-center gap-2 px-6 py-2 text-violet-600 border-violet-200 hover:border-violet-300 hover:bg-violet-50"
            >
              {isLoadingMore ? (
                <>
                  <div className="w-4 h-4 border-2 border-violet-600 border-t-transparent rounded-full animate-spin"></div>
                  <span>加载中...</span>
                </>
              ) : (
                <>
                  <TrendingUp className="w-4 h-4" />
                  <span>加载更多提示词</span>
                </>
              )}
            </Button>
          </div>
        )}
        
        {/* 已显示全部提示 */}
        {displayCount >= filteredPrompts.length && filteredPrompts.length > 6 && (
          <div className="col-span-full text-center mt-8 py-6 text-gray-500">
            <Sparkles className="w-6 h-6 mx-auto mb-2 opacity-50" />
            <p>已显示全部 {filteredPrompts.length} 个提示词</p>
          </div>
        )}
      </>
    )
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-16">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-16">
        <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Sparkles className="w-12 h-12 text-red-400" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">加载失败</h3>
        <p className="text-gray-600 mb-6">{error}</p>
        <Button onClick={fetchPrompts} variant="outline">
          重试
        </Button>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-2xl font-bold text-gray-900">热门提示词</h2>
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <TrendingUp className="w-4 h-4" />
            <span>{prompts.length} 个提示词</span>
          </div>
        </div>
        <p className="text-gray-600 text-base leading-relaxed">
          您可以浏览所有公共提示词，但需要
          <span className="text-violet-600 font-medium">登录后才能复制到您的提示词库</span>
        </p>
      </div>

      {/* 标签页导航 */}
      <div className="flex justify-center mb-8">
        <div className="inline-flex bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => {
              setActiveTab('recent')
              setDisplayCount(6) // 重置显示数量
            }}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'recent'
                ? 'bg-white text-violet-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Clock className="w-4 h-4 mr-2 inline" />
            最新
          </button>
          <button
            onClick={() => {
              setActiveTab('popular')
              setDisplayCount(6) // 重置显示数量
            }}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'popular'
                ? 'bg-white text-violet-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Heart className="w-4 h-4 mr-2 inline" />
            热门
          </button>
          <button
            onClick={() => {
              setActiveTab('trending')
              setDisplayCount(6) // 重置显示数量
            }}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'trending'
                ? 'bg-white text-violet-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <TrendingUp className="w-4 h-4 mr-2 inline" />
            趋势
          </button>
        </div>
      </div>

      {/* 瀑布流展示 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {renderPromptColumns()}
      </div>
    </div>
  )
} 
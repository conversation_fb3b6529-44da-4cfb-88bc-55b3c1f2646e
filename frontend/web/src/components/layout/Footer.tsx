'use client';

import Link from 'next/link';
import { Sparkles, Heart, Globe, MessageCircle, Mail } from 'lucide-react';

// 社交平台配置 - 可以在这里统一配置图片和链接
const SOCIAL_PLATFORMS = [
  {
    name: '小红书',
    emoji: '📱',
    color: 'from-red-400 to-pink-500',
    bgColor: 'bg-red-500',
    qrCode: '/images/social/xiaohongshu-qr.png', // 小红书二维码图片路径
    icon: '/images/social/xiaohongshu-icon.png', // 小红书图标
    profileUrl: 'https://www.xiaohongshu.com/user/profile/5aea7f504eacab64a90e6fed' // 小红书主页链接
  },
  {
    name: '抖音',
    emoji: '🎵',
    color: 'from-black to-gray-700',
    bgColor: 'bg-gray-800',
    qrCode: '/images/social/douyin-qr.png', // 抖音二维码图片路径
    icon: '/images/social/douyin-icon.png', // 抖音图标
    profileUrl: 'https://www.douyin.com/user/MS4wLjABAAAAhYryCppE36QBd2080nJr5Yu0gOAIeAyROpt1FVDcIfM' // 抖音主页链接
  },
  {
    name: 'B站',
    emoji: '📺',
    color: 'from-pink-400 to-blue-500',
    bgColor: 'bg-pink-500',
    qrCode: '/images/social/bilibili-qr.png', // B站二维码图片路径
    icon: '/images/social/bilibili-icon.png', // B站图标
    profileUrl: 'https://space.bilibili.com/3546555153910411' // B站主页链接
  },
  {
    name: '微信',
    emoji: '💬',
    color: 'from-green-400 to-green-600',
    bgColor: 'bg-green-500',
    qrCode: '/images/social/wechat-qr.png', // 微信二维码图片路径
    icon: '/images/social/wechat-icon.png', // 微信图标
    profileUrl: '#' // 微信通常不需要链接，使用二维码
  }
];

export function Footer() {
  const handleSocialClick = (platform: typeof SOCIAL_PLATFORMS[0]) => {
    if (platform.profileUrl && platform.profileUrl !== '#') {
      window.open(platform.profileUrl, '_blank', 'noopener noreferrer');
    }
  };

  return (
    <footer className="relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-violet-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/3 w-80 h-80 bg-cyan-500/10 rounded-full blur-3xl"></div>
      </div>
      
      <div className="relative mx-auto max-w-7xl px-4 pt-20 pb-8 sm:px-6 lg:px-8">
        <div className="grid gap-12 lg:grid-cols-4 lg:items-center">
          {/* 左侧品牌区域 */}
          <div className="lg:col-span-3">
            {/* Logo区域 */}
            <div className="mb-8">
              <div className="flex items-center space-x-4 mb-6">
                <div className="relative">
                  <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-violet-600 to-cyan-600 shadow-lg">
                    <Sparkles className="h-9 w-9 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-4 border-slate-900 flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>
                <div>
                  <h3 className="text-3xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    PromptHub
                  </h3>
                  <p className="text-violet-300 font-medium">AI提示词分享、创作平台</p>
                </div>
              </div>
              
              {/* 优化后的描述文案 */}
              <div className="space-y-4 max-w-xl">
                <p className="text-slate-300 text-lg leading-relaxed">
                  <span className="text-cyan-400 font-semibold">汇聚全球创意智慧</span>，打造AI时代的提示词创作生态。
                </p>
                <p className="text-slate-400 leading-relaxed">
                  让每个创作者都能轻松掌握AI对话艺术，在这里分享灵感、发现惊喜，与150万+用户一起探索AI的无限可能。
                </p>
              </div>
            </div>

            {/* 社交链接 */}
            <div className="flex items-center space-x-3">
              <span className="text-sm text-slate-400 mr-2">联系我们：</span>
              {[
                { icon: Globe, href: '#', label: '官方网站', color: 'hover:bg-blue-600' },
                { icon: MessageCircle, href: '#', label: '在线社区', color: 'hover:bg-green-600' },
                { icon: Mail, href: 'mailto:<EMAIL>', label: '邮箱联系', color: 'hover:bg-purple-600' },
                { icon: Heart, href: '#', label: '收藏关注', color: 'hover:bg-red-600' }
              ].map((social, index) => (
                <a 
                  key={index} 
                  href={social.href} 
                  title={social.label}
                  className={`group flex h-10 w-10 items-center justify-center rounded-lg bg-slate-800/80 text-slate-400 transition-all duration-300 ${social.color} hover:text-white hover:scale-110 hover:shadow-lg`}
                >
                  <social.icon className="h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
                </a>
              ))}
            </div>
          </div>

          {/* 右侧区域 */}
          <div className="lg:col-span-1 flex flex-col items-center text-center">
            <div className="w-full">
              <h4 className="mb-6 text-lg font-semibold text-white">关注我们</h4>
              <div className="flex items-center justify-center space-x-4">
                {SOCIAL_PLATFORMS.map((platform, index) => (
                  <div key={index} className="relative group">
                    {/* 默认显示的小图标 */}
                    <div 
                      className={`w-12 h-12 ${platform.bgColor} rounded-xl flex items-center justify-center cursor-pointer transition-all duration-300 group-hover:scale-110 shadow-lg`}
                      onClick={() => handleSocialClick(platform)}
                      title={`点击访问${platform.name}`}
                    >
                      {/* 优先显示图标文件，失败时显示emoji */}
                      <img 
                        src={platform.icon} 
                        alt={platform.name}
                        className="w-6 h-6"
                        onError={(e) => {
                          // 如果图标加载失败，隐藏图片显示emoji
                          e.currentTarget.style.display = 'none';
                          const emojiSpan = e.currentTarget.nextElementSibling as HTMLElement;
                          if (emojiSpan) emojiSpan.style.display = 'block';
                        }}
                      />
                      <span className="text-white text-lg" style={{ display: 'none' }}>
                        {platform.emoji}
                      </span>
                    </div>
                    
                    {/* 悬停时显示的二维码 - 重新设计版本 */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 pointer-events-none group-hover:pointer-events-auto">
                      <div className="relative">
                        {/* 二维码卡片 - 更大二维码，更小留白 */}
                        <div className="bg-white rounded-xl p-2 shadow-xl border border-gray-200 min-w-[140px]">
                          <div className="mb-2">
                            <img 
                              src={platform.qrCode} 
                              alt={`${platform.name}二维码`}
                              className="mx-auto rounded-lg border border-gray-100 qr-image"
                              style={{ width: '120px', height: '120px' }}
                              onError={(e) => {
                                console.warn(`⚠️ 二维码图片加载失败: ${platform.qrCode}`);
                                const target = e.currentTarget;
                                target.style.display = 'none';
                                const fallback = target.parentElement?.querySelector('.qr-fallback') as HTMLElement;
                                if (fallback) fallback.style.display = 'flex';
                              }}
                              onLoad={() => {
                                console.log(`✅ 二维码图片加载成功: ${platform.qrCode}`);
                              }}
                            />
                            {/* 备用显示内容 */}
                            <div 
                              className={`qr-fallback mx-auto bg-gradient-to-br ${platform.color} rounded-lg flex flex-col items-center justify-center shadow-lg`}
                              style={{ display: 'none', width: '120px', height: '120px' }}
                            >
                              <div className="text-white text-center">
                                <div className="text-2xl mb-1">{platform.emoji}</div>
                                <div className="text-sm font-bold">{platform.name}</div>
                              </div>
                            </div>
                          </div>
                          <p className="text-xs text-gray-600 text-center font-medium">{platform.name}二维码</p>
                        </div>
                        
                        {/* 箭头 - 重新定位 */}
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 -mt-0.5">
                          <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-transparent border-t-white"></div>
                        </div>
                      </div>
                    </div>
                    
                    {/* 平台名称 */}
                    <p className="text-xs text-slate-400 text-center mt-2">{platform.name}</p>
                  </div>
                ))}
              </div>
              <p className="text-xs text-slate-500 mt-6">悬停图标查看二维码</p>
            </div>
          </div>
        </div>

        {/* 底部版权区域 */}
        <div className="mt-8 pt-6 border-t border-slate-700/50">
          <div className="flex flex-col items-center space-y-4">
            {/* 重要链接 */}
            <div className="flex items-center space-x-8">
              {[
                { label: '关于我们', href: '/about' },
                { label: '隐私政策', href: '/privacy' },
                { label: '使用条款', href: '/terms' }
              ].map((link, index) => (
                <Link 
                  key={index}
                  href={link.href} 
                  className="text-slate-400 text-sm transition-colors duration-300 hover:text-cyan-400"
                >
                  {link.label}
                </Link>
              ))}
            </div>
            
            {/* 版权信息 */}
            <div className="text-center space-y-2">
              <p className="text-slate-400 text-sm">
                © 2024 PromptHub.xin 保留所有权利
              </p>
              <p className="text-slate-500 text-xs">
                <a 
                  href="https://beian.miit.gov.cn" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="hover:text-cyan-400 transition-colors duration-300"
                >
                  沪ICP备2025124934号-2
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
} 
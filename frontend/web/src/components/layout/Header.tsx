'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Search,
  Sparkles,
  Plus,
  User,
  LogOut
} from 'lucide-react'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'

export default function Header() {
  const [searchQuery, setSearchQuery] = useState('')
  const router = useRouter()
  const { user, logout, isAuthenticated } = useAuth()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/prompts?search=${encodeURIComponent(searchQuery)}`)
    }
  }

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('登出失败:', error)
    }
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        {/* Logo - 使用原来的样式 */}
        <Link href="/" className="flex items-center space-x-3 mr-8">
          <div className="relative">
            <div className="w-10 h-10 bg-gradient-to-br from-violet-600 to-cyan-600 rounded-xl flex items-center justify-center">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
          </div>
          <div>
            <span className="text-2xl font-bold bg-gradient-to-r from-violet-600 to-cyan-600 bg-clip-text text-transparent">
              PromptHub
            </span>
            <div className="text-xs text-gray-500 font-medium">AI提示词分享、创作平台</div>
          </div>
        </Link>

        {/* Navigation */}
        <nav className="hidden lg:flex items-center space-x-6 text-sm font-medium mr-6">
          <Link
            href="/prompts"
            className="text-gray-700 hover:text-violet-600 font-medium transition-colors flex items-center space-x-1"
          >
            <span>🎨</span><span>提示词库</span>
          </Link>
          <Link
            href="/solutions"
            className="text-gray-700 hover:text-violet-600 font-medium transition-colors flex items-center space-x-1"
          >
            <span>💡</span><span>提示工程</span>
          </Link>
          <Link
            href="/for"
            className="text-gray-700 hover:text-violet-600 font-medium transition-colors flex items-center space-x-1"
          >
            <span>👥</span><span>职业专区</span>
          </Link>
          <Link
            href="/extension"
            className="text-gray-700 hover:text-violet-600 font-medium transition-colors flex items-center space-x-1"
          >
            <span>🧩</span><span>浏览器插件</span>
          </Link>
        </nav>

        {/* Search - 居中 */}
        <div className="flex-1 max-w-md mx-6">
          <form onSubmit={handleSearch} className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索提示词..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 bg-muted/50"
            />
          </form>
        </div>

        {/* Actions - 使用 ml-auto 推到最右边 */}
        <div className="ml-auto flex items-center space-x-3">
          {/* 认证按钮 */}
          {isAuthenticated ? (
            <div className="flex items-center space-x-3">
              {/* 已登录用户显示"我的提示词"链接 */}
              <Link href="/my-prompts">
                <Button variant="outline" className="text-violet-600 border-violet-200 hover:bg-violet-50 gap-2">
                  <span>📝</span>
                  我的提示词
                </Button>
              </Link>

              {/* 管理员显示管理后台链接 */}
              {((user as any)?.email === '<EMAIL>' || (user as any)?.role === 'admin') && (
                <Link href="/admin">
                  <Button variant="outline" className="text-orange-600 border-orange-200 hover:bg-orange-50 gap-2">
                    <span>⚙️</span>
                    管理后台
                  </Button>
                </Link>
              )}

              <span className="text-sm text-gray-600">
                欢迎，{(user as any)?.username || (user as any)?.email || '用户'}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="gap-2"
              >
                <LogOut className="w-4 h-4" />
                退出
              </Button>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <Link href="/auth/login">
                <Button variant="outline" size="sm" className="gap-2">
                  <User className="w-4 h-4" />
                  登录
                </Button>
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Navigation (可以后续添加) */}
    </header>
  )
} 
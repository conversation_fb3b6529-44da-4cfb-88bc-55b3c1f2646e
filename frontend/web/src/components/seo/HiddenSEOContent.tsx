/**
 * 隐藏的SEO内容组件
 * 这些内容对搜索引擎可见，但对用户不可见
 * 使用screen reader only (sr-only) 类来隐藏内容
 */

interface HiddenSEOContentProps {
  keywords?: string[]
  relatedQueries?: string[]
  categories?: string[]
  tools?: string[]
}

export default function HiddenSEOContent({ 
  keywords = [], 
  relatedQueries = [], 
  categories = [],
  tools = []
}: HiddenSEOContentProps) {
  // 默认核心关键词
  const defaultKeywords = [
    '提示词', 'AI提示词', '免费提示词', 'ChatGPT提示词', 'Claude提示词', 
    'Midjourney提示词', '提示词工程', 'Prompt Engineering', 'AI指令', 
    '提示词模板', '提示词库', 'AI助手提示词', '人工智能', 'AIGC'
  ]

  // 默认相关查询
  const defaultQueries = [
    '如何写好AI提示词',
    '怎么向AI提问更有效', 
    'AI总是不理解我的意思怎么办',
    '提高ChatGPT回答质量的技巧',
    '保存和管理常用的AI提示词',
    '一键调用我的Prompt库',
    '跨浏览器同步AI提示词',
    '团队共享Prompt最佳实践',
    '提示词版本控制工具',
    'AI工作流管理'
  ]

  // 默认分类
  const defaultCategories = [
    'AI写作提示词', 'AI绘画提示词', 'AI编程提示词', 'AI营销提示词', 
    'AI翻译提示词', '商业提示词', '学习提示词', '创意提示词', 
    '办公提示词', '编程助手提示词'
  ]

  // 默认工具
  const defaultTools = [
    'ChatGPT', 'Claude', 'GPT-4', 'Midjourney', '文心一言', 
    '通义千问', 'Gemini', 'Stable Diffusion', 'DALL-E'
  ]

  const allKeywords = [...defaultKeywords, ...keywords]
  const allQueries = [...defaultQueries, ...relatedQueries]
  const allCategories = [...defaultCategories, ...categories]
  const allTools = [...defaultTools, ...tools]

  return (
    <div className="sr-only" aria-hidden="true">
      {/* 核心关键词 */}
      <section>
        <h2>AI提示词相关关键词</h2>
        <p>{allKeywords.join(', ')}</p>
      </section>

      {/* 用户查询意图 */}
      <section>
        <h2>用户常见问题和需求</h2>
        <ul>
          {allQueries.map((query, index) => (
            <li key={index}>{query}</li>
          ))}
        </ul>
      </section>

      {/* 分类和场景 */}
      <section>
        <h2>AI提示词应用分类</h2>
        <p>{allCategories.join(', ')}</p>
      </section>

      {/* 支持的AI工具 */}
      <section>
        <h2>支持的AI工具和平台</h2>
        <p>{allTools.join(', ')}</p>
      </section>

      {/* 长尾关键词 */}
      <section>
        <h2>相关长尾关键词</h2>
        <p>
          免费AI提示词大全, 优质提示词分享, AI提示词库, 提示词模板, 
          提示词生成器, AI助手提示词, 最佳AI指令, 高效提示词, 
          精选Prompt, 创意写作Prompt, 数据分析提示词, 教育学习Prompt,
          AI对话技巧, Prompt写作教程, AI使用指南, AI效率提升方法,
          AI工具使用教程, AI使用常见问题
        </p>
      </section>

      {/* 地域相关 */}
      <section>
        <h2>地域和语言</h2>
        <p>中文AI提示词, 中国AI工具, 国内AI平台, 简体中文Prompt</p>
      </section>

      {/* 技术相关 */}
      <section>
        <h2>技术和方法</h2>
        <p>
          零样本提示, 少样本提示, 思维链推理, 角色扮演提示, 
          结构化提示词, 提示词优化, 提示词调试, 提示词测试,
          Few-shot Prompting, Chain of Thought, Role Playing
        </p>
      </section>
    </div>
  )
}

// 预定义的页面特定SEO内容
export const HomePageSEO = () => (
  <HiddenSEOContent 
    keywords={['首页', '主页', 'PromptHub首页']}
    relatedQueries={[
      'PromptHub是什么',
      '最好的AI提示词网站',
      '免费提示词哪里找',
      'AI提示词平台推荐'
    ]}
  />
)

export const PromptsLibrarySEO = () => (
  <HiddenSEOContent 
    keywords={['提示词库', '提示词大全', 'Prompt库']}
    relatedQueries={[
      '哪里有免费的AI提示词',
      '提示词库推荐',
      '最全的提示词集合',
      'AI提示词资源'
    ]}
  />
)

export const PromptEngineeringSEO = () => (
  <HiddenSEOContent 
    keywords={['提示词工程', 'Prompt Engineering', '提示词设计']}
    relatedQueries={[
      '如何学习提示词工程',
      '提示词工程教程',
      '提示词设计原则',
      'Prompt Engineering指南'
    ]}
  />
)

export const ExtensionSEO = () => (
  <HiddenSEOContent 
    keywords={['浏览器插件', 'Chrome插件', 'AI插件']}
    relatedQueries={[
      'AI提示词插件下载',
      '浏览器AI助手',
      'Chrome AI扩展',
      '免费AI插件推荐'
    ]}
  />
)

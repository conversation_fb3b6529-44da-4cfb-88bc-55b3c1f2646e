"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON>H<PERSON>er, CardContent, CardDescription, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Heart, 
  Copy, 
  User,
  Calendar,
  Eye,
  Plus,
  Check,
  Edit,
  Trash2
} from "lucide-react"
import { useAuth } from '@/hooks/useAuth'
import authService from '@/lib/auth'
import toast from 'react-hot-toast'
import Link from 'next/link'
import { getCategoryDisplayName } from '@/lib/category-utils'
import { API_BASE_URL } from '@/lib/api'

interface PromptCardProps {
  prompt: {
    id: string | number
    title: string
    description: string
    content: string
    category: string
    categoryDisplayName?: string
    tags: string[]
    author?: string
    authorName?: string
    likes?: number
    likes_count?: number
    usage_count?: number
    downloads?: number
    views?: number
    created_at?: string
    createdAt?: string
    isLiked?: boolean
  }
  isMyPrompt?: boolean
  onDelete?: (id: string | number) => void
  onUpdate?: (prompt: any) => void
}

const formatDate = (dateString?: string) => {
  if (!dateString) return '未知时间'
  
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return '今天'
  if (diffDays === 2) return '昨天'
  if (diffDays <= 7) return `${diffDays}天前`
  if (diffDays <= 30) return `${Math.ceil(diffDays / 7)}周前`
  return `${Math.ceil(diffDays / 30)}个月前`
}

export function PromptCard({ prompt, isMyPrompt = false, onDelete, onUpdate }: PromptCardProps) {
  const { user, isAuthenticated } = useAuth()
  
  // 本地状态管理
  const [isLiked, setIsLiked] = useState(prompt.isLiked || false)
  const [likesCount, setLikesCount] = useState(prompt.likes_count || prompt.likes || 0)
  const [downloadsCount, setDownloadsCount] = useState(prompt.usage_count || prompt.downloads || 0)
  const [isCopied, setIsCopied] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  // 当 prompt 数据更新时，同步本地状态
  useEffect(() => {
    setIsLiked(prompt.isLiked || false)
    setLikesCount(prompt.likes_count || prompt.likes || 0)
    setDownloadsCount(prompt.usage_count || prompt.downloads || 0)
  }, [prompt.isLiked, prompt.likes_count, prompt.likes, prompt.usage_count, prompt.downloads])

  const handleCardClick = (e: React.MouseEvent) => {
    // 检查是否点击了按钮
    if ((e.target as HTMLElement).closest('button')) {
      return
    }

    // 需要登录才能查看详情
    if (!isAuthenticated) {
      toast.error('请先登录后查看提示词详情')
      setTimeout(() => {
        window.location.href = '/auth/login'
      }, 2000)
      return
    }

    // 在新标签页打开提示词详情页
    window.open(`/prompts/${prompt.id}`, '_blank')
  }

  const handleLike = async (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!isAuthenticated) {
      toast.error('请先登录后再点赞')
      setTimeout(() => {
        window.location.href = '/auth/login'
      }, 2000)
      return
    }

    if (isProcessing) return
    setIsProcessing(true)

    try {
      // 构建请求头，支持Cookie认证
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      const response = await fetch(`${API_BASE_URL}/prompts/${prompt.id}/like`, {
        method: 'POST',
        headers,
        credentials: 'include' // 支持Cookie认证
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || '操作失败');
      }

      // 使用后端返回的最新状态更新UI
      setIsLiked(result.data.isLiked);
      setLikesCount(result.data.likesCount);
      toast.success(result.data.isLiked ? '点赞成功' : '取消点赞');

    } catch (error) {
      toast.error('操作失败，请重试');
    } finally {
      setIsProcessing(false)
    }
  }

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation()

    if (isProcessing) return
    setIsProcessing(true)

    try {
      // 先复制到剪贴板
      await navigator.clipboard.writeText(prompt.content)

      // 调用后端API增加复制计数（所有用户都计数）
      try {
        const headers: Record<string, string> = {
          'Content-Type': 'application/json'
        }

        // 如果用户已登录，添加认证头（用于将来可能的用户行为记录）
        if (isAuthenticated) {
          const token = authService.getToken();
          if (token && token !== 'cookie-auth') {
            headers.Authorization = `Bearer ${token}`
          }
        }

        const response = await fetch(`${API_BASE_URL}/prompts/${prompt.id}/copy`, {
          method: 'POST',
          headers,
          credentials: 'include' // 支持Cookie认证
        });
        const result = await response.json();
        if (result.success && result.data) {
          // 更新复制次数
          setDownloadsCount(result.data.downloads);
        }
      } catch (apiError) {
        console.warn('更新复制计数失败:', apiError);
        // 不影响复制功能，只是计数可能不准确
      }

      setIsCopied(true)
      toast.success('提示词已复制到剪贴板')

      // 2秒后恢复复制按钮状态
      setTimeout(() => {
        setIsCopied(false)
      }, 2000)
    } catch (error) {
      console.error('复制失败:', error)
      toast.error('复制失败，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleCopyToMyPrompts = async (e: React.MouseEvent) => {
    e.stopPropagation()

    // 添加详细的调试信息
    console.log('🔍 复制到我的提示词 - 详细状态:', {
      user: user,
      isAuthenticated: isAuthenticated,
      hasUser: !!user,
      userInfo: user ? { id: user.id, username: user.username } : null,
      timestamp: new Date().toISOString()
    })

    if (!isAuthenticated) {
      console.log('❌ 认证失败，跳转登录页')
      toast.error('请先登录后再复制到我的提示词')
      setTimeout(() => {
        window.location.href = '/auth/login'
      }, 2000)
      return
    }

    console.log('✅ 认证通过，开始复制操作')

    if (isProcessing) return
    setIsProcessing(true)

    try {

      // 改进的数据验证 - 提供默认值而不是直接抛出错误
      const title = prompt.title?.trim() || '未命名提示词'
      const content = prompt.content?.trim() || '暂无内容'
      const category = prompt.category?.trim() || 'ai-assistant' // 使用数据库中实际存在的分类名称

      // 如果关键字段都为空，才抛出错误
      if (!title || title === '未命名提示词' && (!content || content === '暂无内容')) {
        console.error('提示词数据:', prompt)
        throw new Error('提示词信息不完整，无法复制')
      }

      // 构建请求头，支持Cookie认证
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      const response = await fetch(`${API_BASE_URL}/prompts`, {
        method: 'POST',
        headers,
        credentials: 'include', // 支持Cookie认证
        body: JSON.stringify({
          title: `${title}（副本）`,
          description: prompt.description?.trim() || '从共享提示词复制而来',
          content: content,
          category: category,
          tags: Array.isArray(prompt.tags) ? prompt.tags : [],
          isPrivate: true,  // 设置为私密状态
          status: 'draft'   // 设置为草稿状态
        })
      })

      const result = await response.json()

      if (!response.ok) {
        if (response.status === 401) {
          toast.error('登录已过期，请重新登录')
          authService.clearAuth()
          window.location.href = '/auth/login'
          return
        }
        throw new Error(result.message || '复制失败')
      }

      if (!result.success) {
        throw new Error(result.message || '复制失败')
      }

      toast.success('已复制到我的提示词（私密草稿）')
    } catch (error: unknown) {
      console.error('复制到我的提示词失败:', error)
      const errorMessage = error instanceof Error ? error.message : '复制失败，请重试'
      toast.error(errorMessage)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleLoginToView = () => {
    window.location.href = '/auth/login'
  }

  const categoryDisplayName = getCategoryDisplayName(prompt.category)

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 border border-gray-200/60 hover:border-violet-200 bg-white backdrop-blur-sm h-fit cursor-pointer" onClick={handleCardClick}>
      <CardHeader className="pb-4">
        {/* 第一行：分类 左对齐 + 浏览量 右对齐 */}
          <div className="flex items-center justify-between mb-3">
              <Badge 
                variant="secondary" 
                className="text-xs bg-violet-100 text-violet-700 hover:bg-violet-200"
              >
                {categoryDisplayName}
              </Badge>
          
          <div className="flex items-center gap-1 text-sm text-gray-500">
            <Eye className="w-4 h-4" />
            <span>{prompt.usage_count || prompt.views || 0}</span>
          </div>
        </div>

        {/* 第二行：标题，最多两行 */}
        <CardTitle className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-violet-700 transition-colors">
          {prompt.title}
        </CardTitle>

        {/* 第三行：用户名 时间 左对齐 */}
        <div className="flex items-center gap-2 mb-3 text-xs text-gray-600">
          <div className="w-5 h-5 bg-gradient-to-br from-violet-500 to-cyan-500 rounded-full flex items-center justify-center">
            <User className="w-3 h-3 text-white" />
          </div>
          <span className="font-medium">{prompt.author || prompt.authorName || '匿名创作者'}</span>
          <span className="text-gray-400">•</span>
          <span className="flex items-center">
            <Calendar className="w-3 h-3 mr-1" />
            {formatDate(prompt.created_at || prompt.createdAt)}
          </span>
        </div>
        
        {/* 第四行：提示词模块，最多三行，包含登录查看全部 */}
        <div className="bg-gray-50 rounded-lg p-3 mb-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-gray-700">提示词内容</span>
          </div>
          
          <div className="text-sm text-gray-600 leading-relaxed line-clamp-3 mb-2">
            {prompt.content || '暂无内容预览...'}
          </div>
          
          {!isAuthenticated && !isMyPrompt && (
            <div className="text-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLoginToView}
                className="text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-100"
              >
                登录查看全部
              </Button>
            </div>
          )}
        </div>
      
        {/* 第五行：Tags */}
        {prompt.tags && prompt.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {prompt.tags.slice(0, 4).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs text-gray-600 border-gray-300">
                {tag}
              </Badge>
            ))}
            {prompt.tags.length > 4 && (
              <Badge variant="outline" className="text-xs text-gray-500">
                +{prompt.tags.length - 4}
              </Badge>
            )}
          </div>
        )}

        {/* 第六行：点赞 复制 左对齐 + 编辑/复制到我的提示词 右对齐 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
          <Button 
            size="sm" 
            variant="ghost"
            onClick={handleLike}
            disabled={isProcessing}
              className={`flex items-center gap-1 text-sm px-3 h-9 transition-all duration-200 ${
              isLiked 
                ? 'text-red-500 bg-red-50 hover:bg-red-100' 
                : 'text-gray-500 hover:text-red-500 hover:bg-red-50'
            }`}
              title="点赞"
          >
              <Heart className={`w-5 h-5 transition-all duration-200 ${
              isLiked ? 'fill-current text-red-500' : 'text-red-500'
            }`} />
            <span>{likesCount}</span>
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={handleCopy}
            disabled={isProcessing}
              className={`flex items-center gap-1 text-sm px-3 h-9 transition-all duration-200 ${
                isCopied 
                  ? 'text-green-500 bg-green-50 hover:bg-green-100' 
                  : 'text-gray-500 hover:text-blue-500 hover:bg-blue-50'
              }`}
            title="复制提示词内容"
          >
              {isCopied ? (
                <Check className="w-5 h-5 text-green-500" />
              ) : (
                <Copy className="w-5 h-5 text-blue-500" />
              )}
              <span>{isCopied ? '已复制' : '复制'}</span>
              {downloadsCount > 0 && <span className="ml-1">{downloadsCount}</span>}
          </Button>
          </div>
          
          {isMyPrompt ? (
            <div className="flex items-center gap-2">
              <Link href={`/create?edit=${prompt.id}`}>
                <Button 
                  size="sm" 
                  className="flex items-center gap-1 text-sm px-3 h-8 bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white border-0 transition-all duration-200 shadow-md hover:shadow-lg"
                  title="编辑提示词"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Edit className="w-4 h-4" />
                  <span>编辑</span>
                </Button>
              </Link>
              <Button 
                size="sm" 
                variant="ghost"
                className="flex items-center gap-1 text-sm px-3 h-8 text-red-500 hover:text-red-600 hover:bg-red-50 transition-all duration-200"
                title="删除提示词"
                onClick={(e) => {
                  e.stopPropagation()
                  if (onDelete && window.confirm('确定要删除这个提示词吗？此操作不可恢复。')) {
                    onDelete(prompt.id)
                  }
                }}
                disabled={isProcessing}
              >
                <Trash2 className="w-4 h-4" />
                <span>删除</span>
              </Button>
            </div>
          ) : (
            <Button 
              size="sm" 
              onClick={handleCopyToMyPrompts}
              disabled={isProcessing}
              className="flex items-center gap-1 text-sm px-4 h-8 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 transition-all duration-200 shadow-md hover:shadow-lg"
              title="复制到我的提示词"
            >
              <Plus className="w-4 h-4" />
              <span>{isProcessing ? '复制中...' : '复制到我的提示词'}</span>
            </Button>
          )}
        </div>
      </CardHeader>
    </Card>
  )
} 
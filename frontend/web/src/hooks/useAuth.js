'use client'

import { useState, useEffect, createContext, useContext, useRef } from 'react'
import { useRouter } from 'next/navigation'
import authService from '@/lib/auth'

const AuthContext = createContext({
  user: null,
  loading: false,
  isAuthenticated: false,
  login: async () => {},
  register: async () => {},
  logout: async () => {},
  refreshUserInfo: async () => {},
  verifyTokenForSensitiveOperation: async () => {}
})

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const initRef = useRef(false)

  // 统一的初始化逻辑
  useEffect(() => {
    // 使用ref避免重复初始化（特别是在React严格模式下）
    if (initRef.current) return
    initRef.current = true

    const initAuth = async () => {
      try {
        console.log('🚀 开始初始化认证状态...')

        // 确保在客户端环境中运行
        if (typeof window === 'undefined') {
          setLoading(false)
          return
        }

        // 新的authService已经在构造函数中自动初始化
        const token = authService.getToken()
        const currentUser = authService.getUser()

        console.log('🔍 认证信息检查:', {
          hasToken: !!token,
          hasUser: !!currentUser,
          tokenLength: token ? token.length : 0,
          userName: currentUser ? currentUser.username : 'N/A'
        })

        if (currentUser) {
          // 成功从存储恢复登录状态
          setUser(currentUser)
          console.log('✅ 从存储恢复登录状态:', currentUser.username)

          // 对于Cookie认证，不需要后台验证token
          if (token && token !== 'cookie-auth') {
            // 只有非Cookie认证才需要后台验证
            setTimeout(() => {
              validateTokenInBackground()
            }, 2000)
          }

          // 有用户信息时立即设置loading为false
          setLoading(false)
          console.log('🏁 认证初始化完成')
        } else {
          // 如果本地没有用户信息，等待authService完成Cookie初始化
          try {
            // 等待authService的初始化完成
            await authService.initializeStorage()

            const updatedUser = authService.getUser()
            if (updatedUser) {
              setUser(updatedUser)
              console.log('✅ 通过Cookie恢复登录状态:', updatedUser.username)
            } else {
              console.log('ℹ️ 用户未登录')
              setUser(null)
            }
          } catch (error) {
            console.warn('⚠️ Cookie认证检查失败:', error)
            setUser(null)
          }

          setLoading(false)
          console.log('🏁 认证初始化完成（Cookie检查）')
        }
      } catch (error) {
        console.error('❌ 初始化认证状态失败:', error)
        setUser(null)
        // 清除可能损坏的认证信息
        authService.clearAuth()
        setLoading(false)
        console.log('🏁 认证初始化完成（错误处理）')
      }
    }

    initAuth()
  }, []) // 空依赖数组，配合useRef确保只执行一次

  // 监听认证状态变化事件（来自插件登录等）
  useEffect(() => {
    if (typeof window === 'undefined') return

    const handleAuthStateChange = (event) => {
      console.log('📢 收到认证状态变化事件:', event.detail)

      if (event.detail.type === 'LOGIN_SUCCESS' || event.detail.type === 'login') {
        const { user: newUser, token } = event.detail

        if (newUser) {
          setUser(newUser)
          console.log('✅ 从事件更新登录状态:', newUser.username)
        }
      } else if (event.detail.type === 'logout') {
        console.log('🚪 从事件更新退出登录状态')
        setUser(null)
        authService.clearAuth()
      }
    }

    window.addEventListener('auth-state-changed', handleAuthStateChange)

    return () => {
      window.removeEventListener('auth-state-changed', handleAuthStateChange)
    }
  }, [])

  // 后台验证token，失败时不立即清除状态（避免网络波动导致掉登录）
  const validateTokenInBackground = async () => {
    try {
      console.log('🔍 开始后台验证token...')
      const isValid = await authService.validateToken()
      if (isValid) {
        console.log('✅ Token验证成功')
      } else {
        console.warn('⚠️ Token验证失败，但保持当前登录状态')
      }
    } catch (error) {
      console.warn('⚠️ Token后台验证失败:', error.message)
      
      // 如果是网络错误，不清除登录状态
      if (error.message && (
        error.message.includes('Failed to fetch') || 
        error.message.includes('fetch') ||
        error.message.includes('网络')
      )) {
        console.log('📡 网络错误，保持登录状态')
        return
      }
      
      // 只有在明确token过期或401错误时才清除状态
      if (error.message && (
        error.message.includes('过期') || 
        error.message.includes('401') || 
        error.message.includes('unauthorized') ||
        error.message.includes('Unauthorized')
      )) {
        console.log('⏰ Token已过期，清除登录状态')
        setUser(null)
        authService.clearAuth()
      }
    }
  }

  const login = async (credentials) => {
    setLoading(true)
    try {
      const result = await authService.login(credentials)
      setUser(result.user)
      console.log('✅ 登录成功:', result.user.username)
      return result
    } catch (error) {
      console.error('❌ 登录失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const register = async (userData) => {
    setLoading(true)
    try {
      const result = await authService.register(userData)
      setUser(result.user)
      console.log('✅ 注册成功:', result.user.username)
      return result
    } catch (error) {
      console.error('❌ 注册失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    setLoading(true)
    try {
      await authService.logout()
      setUser(null)
      console.log('👋 已退出登录')
      router.push('/')
    } catch (error) {
      console.error('❌ 登出失败:', error)
      // 即使登出失败也要清除前端状态
      setUser(null)
      authService.clearAuth()
      router.push('/')
    } finally {
      setLoading(false)
    }
  }

  const refreshUserInfo = async () => {
    if (!authService.isAuthenticated()) {
      return null
    }

    try {
      const userInfo = await authService.fetchUserInfo()
      setUser(userInfo)
      console.log('🔄 刷新用户信息成功')
      return userInfo
    } catch (error) {
      console.error('❌ 刷新用户信息失败:', error)
      // 不立即清除用户状态，让用户继续使用
      return null
    }
  }

  // 手动验证token（用于敏感操作前）
  const verifyTokenForSensitiveOperation = async () => {
    try {
      const isValid = await authService.validateToken()
      if (!isValid) {
        setUser(null)
        authService.clearAuth()
        throw new Error('登录已过期，请重新登录')
      }
      return true
    } catch (error) {
      setUser(null)
      authService.clearAuth()
      throw error
    }
  }

  const value = {
    user,
    loading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    refreshUserInfo,
    verifyTokenForSensitiveOperation
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
} 
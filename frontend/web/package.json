{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/line-clamp": "^0.4.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.515.0", "next": "^15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.4.47", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}
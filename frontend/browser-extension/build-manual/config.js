/**
 * PromptHub 浏览器扩展配置文件
 * 统一管理所有配置项，避免硬编码
 */

// 环境检测
const isProduction = (() => {
  try {
    // 检查是否在浏览器扩展环境中
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
      const manifest = chrome.runtime.getManifest();
      // 只有当 version_name 明确包含 'prod' 时才认为是生产环境
      // 开发环境默认为 false
      return manifest.version_name?.includes('prod') || false;
    }
    // 非扩展环境，默认为开发环境
    return false;
  } catch (error) {
    return false;
  }
})();

// 环境检测完成

// 主配置对象
var CONFIG = {
  // 环境标识
  IS_PRODUCTION: isProduction,
  
  // API配置
  API: {
    BASE_URL: isProduction ? 'https://prompthub.xin/api' : 'http://localhost:4000/api',
    TIMEOUT: 10000, // 10秒超时
    RETRY_COUNT: 3
  },
  
  // 前端配置
  FRONTEND: {
    BASE_URL: isProduction ? 'https://prompthub.xin' : 'http://localhost:3000',
    LOGIN_PATH: '/auth/login',
    CALLBACK_PATH: '/auth/callback'
  },
  
  // Cookie配置
  COOKIE: {
    NAME: 'prompthub_token',
    DOMAIN: isProduction ? '.prompthub.xin' : 'localhost',
    SECURE: isProduction,
    SAME_SITE: 'lax'
  },
  
  // 分页配置
  PAGINATION: {
    PAGE_SIZE: 6,
    MAX_PAGES: 50
  },
  
  // 认证配置
  AUTH: {
    BROADCAST_CHANNEL: 'prompthub-auth',
    TOKEN_KEY: 'authToken',
    USER_KEY: 'userData',
    CHECK_INTERVAL: 30000 // 30秒检查一次
  },
  
  // UI配置
  UI: {
    FLOATING_BUTTON: {
      DEFAULT_POSITION: { right: '20px', top: '50%' },
      Z_INDEX: 999999
    },
    SIDEBAR: {
      WIDTH: '384px',
      Z_INDEX: 999998
    },
    MODAL: {
      Z_INDEX: 999997
    }
  },
  
  // 快捷键配置
  SHORTCUTS: {
    TOGGLE_SIDEBAR: 'Ctrl+Shift+P'
  },
  
  // 调试配置
  DEBUG: {
    ENABLED: !isProduction,
    LOG_LEVEL: isProduction ? 'error' : 'debug'
  }
};

// 获取完整的API URL
CONFIG.getApiUrl = function(endpoint) {
  return `${this.API.BASE_URL}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
};

// 获取完整的前端URL
CONFIG.getFrontendUrl = function(path = '') {
  return `${this.FRONTEND.BASE_URL}${path.startsWith('/') ? path : '/' + path}`;
};

// 获取登录URL
CONFIG.getLoginUrl = function(params = {}) {
  const url = new URL(this.getFrontendUrl(this.FRONTEND.LOGIN_PATH));
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.set(key, value);
  });
  return url.toString();
};

// 调试日志函数
CONFIG.log = function(level, ...args) {
  if (!this.DEBUG.ENABLED) return;
  
  const levels = ['debug', 'info', 'warn', 'error'];
  const currentLevelIndex = levels.indexOf(this.DEBUG.LOG_LEVEL);
  const logLevelIndex = levels.indexOf(level);
  
  if (logLevelIndex >= currentLevelIndex) {
    const timestamp = new Date().toISOString();
    console[level](`[PromptHub Extension ${timestamp}]`, ...args);
  }
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CONFIG;
  // 同时也设置到global，方便测试
  if (typeof global !== 'undefined') {
    global.PROMPTHUB_CONFIG = CONFIG;
  }
} else if (typeof window !== 'undefined') {
  window.PROMPTHUB_CONFIG = CONFIG;
} else {
  // 浏览器扩展环境 - Service Worker
  if (typeof self !== 'undefined') {
    self.PROMPTHUB_CONFIG = CONFIG;
  } else {
    this.PROMPTHUB_CONFIG = CONFIG;
  }
}

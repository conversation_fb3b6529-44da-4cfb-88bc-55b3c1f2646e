// PromptHub 浏览器插件 - 内容脚本
// 实现悬浮按钮、侧边栏和拖拽功能

// 使用配置 (config.js已通过manifest.json在此脚本之前加载)
const FRONTEND_BASE_URL = window.PROMPTHUB_CONFIG.FRONTEND.BASE_URL
const API_BASE_URL = window.PROMPTHUB_CONFIG.API.BASE_URL
const EXTENSION_ID = chrome.runtime.id

// PromptHub 内容脚本已加载

// 创建BroadcastChannel用于认证状态同步
let authChannel = null
try {
  authChannel = new BroadcastChannel(window.PROMPTHUB_CONFIG.AUTH.BROADCAST_CHANNEL)

  // 监听认证状态变化
  authChannel.onmessage = (event) => {
    handleAuthStateChange(event.data)
  }
} catch (error) {
  // BroadcastChannel创建失败，继续运行
}

// 处理认证状态变化
function handleAuthStateChange(data) {
  if (data.type === 'logout') {
    // 处理登出状态变化
    // 清除本地认证信息
    localStorage.removeItem('authToken')
    localStorage.removeItem('token')
    localStorage.removeItem('userData')
    localStorage.removeItem('user')

    // 如果当前页面需要登录，可以重定向到登录页
    if (window.location.pathname.includes('/my-prompts') ||
        window.location.pathname.includes('/profile')) {
      window.location.href = '/auth/login'
    }
  } else if (data.type === 'login') {
    // 处理登录状态变化
    // 可以刷新页面或更新UI
    if (data.user) {
      localStorage.setItem('userData', JSON.stringify(data.user))
      localStorage.setItem('user', JSON.stringify(data.user))
    }
    if (data.token) {
      localStorage.setItem('authToken', data.token)
      localStorage.setItem('token', data.token)
    }
  }
}

// 检查是否在 PromptHub 网站上
const isPromptHubSite = (() => {
  const frontendUrl = new URL(window.PROMPTHUB_CONFIG.FRONTEND.BASE_URL)
  return window.location.hostname === frontendUrl.hostname &&
         (window.location.port === frontendUrl.port ||
          (!window.location.port && frontendUrl.port === '80') ||
          (!window.location.port && frontendUrl.port === '443'))
})()

// 全局状态
let sidebarInjected = false
let floatingButton = null
let isDragging = false
let clickTimeout = null
let offsetX = 0
let offsetY = 0

// API请求函数（通过background script发送，避免跨域限制）
async function makeApiRequest(endpoint, options = {}) {
  try {
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        type: 'API_REQUEST',
        endpoint: endpoint,
        options: options
      }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message))
        } else {
          resolve(response)
        }
      })
    })

    if (!response.success) {
      throw new Error(response.error || 'API请求失败')
    }

    return response.data
  } catch (error) {
    console.error('API请求失败:', error)
    throw error
  }
}

// 如果在 PromptHub 网站上，不显示插件功能
if (isPromptHubSite) {
  // 当前在 PromptHub 网站，跳过插件功能
} else {
  // 当前在外部网站，初始化插件
  initPromptHubExtension()
}

// 初始化插件
function initPromptHubExtension() {
  // PromptHub 插件已初始化，等待用户点击插件图标
  // 默认创建悬浮按钮，后续可通过设置控制
  createFloatingButton()
}

// 创建悬浮按钮
function createFloatingButton() {
  if (floatingButton) return

  floatingButton = document.createElement('div')
  floatingButton.id = 'prompthub-floating-btn'

  // 创建SVG图标
  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
  svg.setAttribute('fill', 'none')
  svg.setAttribute('viewBox', '0 0 24 24')
  svg.setAttribute('stroke', 'currentColor')
  svg.setAttribute('stroke-width', '2')

  const path = document.createElementNS('http://www.w3.org/2000/svg', 'path')
  path.setAttribute('stroke-linecap', 'round')
  path.setAttribute('stroke-linejoin', 'round')
  path.setAttribute('d', 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z')

  svg.appendChild(path)
  floatingButton.appendChild(svg)

  // 添加样式
  Object.assign(floatingButton.style, {
    position: 'fixed',
    bottom: '32px',
    right: '32px',
    width: '56px',
    height: '56px',
    borderRadius: '50%',
    background: 'linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%)',
    color: 'white',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'grab',
    zIndex: '10000',
    boxShadow: '0 8px 25px rgba(124, 58, 237, 0.3)',
    transition: 'transform 0.3s ease-in-out, box-shadow 0.2s ease',
    willChange: 'top, left'
  })

  // SVG 样式
  Object.assign(svg.style, {
    width: '28px',
    height: '28px',
    pointerEvents: 'none'
  })

  // 添加拖拽和点击事件
  setupFloatingButtonEvents()

  document.body.appendChild(floatingButton)
  // 悬浮按钮已创建，图标已添加
}

// 设置悬浮按钮事件
function setupFloatingButtonEvents() {
  if (!floatingButton) return

  floatingButton.addEventListener('mousedown', (e) => {
    isDragging = false
    clickTimeout = setTimeout(() => {
      // 区分点击和拖拽
      isDragging = true
      floatingButton.style.cursor = 'grabbing'
      floatingButton.style.transition = 'none'
      offsetX = e.clientX - floatingButton.getBoundingClientRect().left
      offsetY = e.clientY - floatingButton.getBoundingClientRect().top
      document.addEventListener('mousemove', onMouseMove)
    }, 150)

    document.addEventListener('mouseup', onMouseUp, { once: true })
  })

  // 鼠标移动处理
  function onMouseMove(e) {
    if (!isDragging) return

    let newX = e.clientX - offsetX
    let newY = e.clientY - offsetY

    // 限制在视窗内
    const btnRect = floatingButton.getBoundingClientRect()
    const maxX = window.innerWidth - btnRect.width
    const maxY = window.innerHeight - btnRect.height

    newX = Math.max(0, Math.min(newX, maxX))
    newY = Math.max(0, Math.min(newY, maxY))

    floatingButton.style.left = `${newX}px`
    floatingButton.style.top = `${newY}px`
    floatingButton.style.right = 'auto'
    floatingButton.style.bottom = 'auto'
  }

  // 鼠标释放处理
  function onMouseUp(e) {
    clearTimeout(clickTimeout)

    if (isDragging) {
      // 拖拽结束，恢复样式
      isDragging = false
      floatingButton.style.cursor = 'grab'
      floatingButton.style.transition = 'transform 0.3s ease-in-out, box-shadow 0.2s ease'
      document.removeEventListener('mousemove', onMouseMove)

      // 边缘吸附
      const btnRect = floatingButton.getBoundingClientRect()
      const snapThreshold = 50

      if (btnRect.left < snapThreshold) {
        floatingButton.style.left = '0px'
        floatingButton.style.transform = 'translateX(-25%)'
      } else if (window.innerWidth - btnRect.right < snapThreshold) {
        floatingButton.style.left = `${window.innerWidth - btnRect.width}px`
        floatingButton.style.transform = 'translateX(25%)'
      } else {
        floatingButton.style.transform = 'none'
      }
    } else {
      // 点击事件 - 打开侧边栏
      toggleSidebar()
    }
  }

  // 悬停效果
  floatingButton.addEventListener('mouseenter', () => {
    if (!isDragging) {
      const currentTransform = floatingButton.style.transform
      if (currentTransform.includes('translateX')) {
        // 保持边缘吸附的位置，只添加缩放
        floatingButton.style.transform = currentTransform + ' scale(1.1)'
      } else {
        floatingButton.style.transform = 'scale(1.1)'
      }
      floatingButton.style.boxShadow = '0 12px 35px rgba(124, 58, 237, 0.4)'
    }
  })

  floatingButton.addEventListener('mouseleave', () => {
    if (!isDragging) {
      const currentTransform = floatingButton.style.transform
      if (currentTransform.includes('translateX')) {
        // 恢复边缘吸附的位置，移除缩放
        floatingButton.style.transform = currentTransform.replace(' scale(1.1)', '')
      } else {
        floatingButton.style.transform = 'none'
      }
      floatingButton.style.boxShadow = '0 8px 25px rgba(124, 58, 237, 0.3)'
    }
  })
}

// 控制悬浮按钮显示/隐藏
function toggleFloatingButton(show) {
  if (!floatingButton) {
    if (show) {
      createFloatingButton()
    }
    return
  }

  if (show) {
    floatingButton.style.display = 'flex'
    // 悬浮按钮已显示
  } else {
    floatingButton.style.display = 'none'
    // 悬浮按钮已隐藏
  }
}





// 切换侧边栏显示
function toggleSidebar() {
  if (!sidebarInjected) {
    injectSidebar()
  } else {
    const sidebar = document.getElementById('prompthub-sidebar')
    if (sidebar) {
      const isHidden = sidebar.classList.contains('sidebar-hidden')
      if (isHidden) {
        openSidebar()
      } else {
        closeSidebar()
      }
    }
  }
}

// 重新加载侧边栏
function reloadSidebar() {
  const sidebar = document.getElementById('prompthub-sidebar')
  if (sidebar) {
    const iframe = sidebar.querySelector('#prompthub-iframe')
    if (iframe) {
      const popupUrl = chrome.runtime.getURL('popup/popup.html')
      iframe.src = `${popupUrl}?t=${Date.now()}`
      // 侧边栏已重新加载
    }
  }
}

// 注入侧边栏
function injectSidebar() {
  if (sidebarInjected) return

  // 创建侧边栏容器
  const sidebar = document.createElement('div')
  sidebar.id = 'prompthub-sidebar'
  sidebar.className = 'prompthub-sidebar sidebar-hidden'

  // 获取插件的popup.html内容
  const popupUrl = chrome.runtime.getURL('popup/popup.html')

  sidebar.innerHTML = `
    <div class="sidebar-content">
      <iframe
        src="${popupUrl}?t=${Date.now()}"
        frameborder="0"
        width="100%"
        height="100%"
        id="prompthub-iframe">
      </iframe>
    </div>
  `

  // 添加到页面
  document.body.appendChild(sidebar)
  sidebarInjected = true

  // 侧边栏已注入

  // 打开侧边栏
  setTimeout(() => {
    openSidebar()
  }, 100)
}

// 打开侧边栏
function openSidebar() {
  const sidebar = document.getElementById('prompthub-sidebar')

  if (sidebar) {
    sidebar.classList.remove('sidebar-hidden')
    // 侧边栏已打开
  }
}

// 关闭侧边栏
function closeSidebar() {
  const sidebar = document.getElementById('prompthub-sidebar')

  if (sidebar) {
    sidebar.classList.add('sidebar-hidden')
    // 侧边栏已关闭
  }
}

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 检查扩展上下文是否有效
  if (!chrome.runtime?.id) {
    return false
  }

  try {
    if (!message || !message.type) {
      sendResponse({ success: false, error: '无效消息' })
      return false
    }

    switch (message.type) {
      case 'COPY_TEXT_AS_PROMPT':
        handleCopyTextAsPrompt(message.text)
        sendResponse({ success: true })
        return false

      case 'FILL_PROMPT':
        fillPromptToActiveElement(message.content)
        sendResponse({ success: true })
        return false

      case 'GET_SELECTED_TEXT':
        const selectedText = window.getSelection().toString()
        sendResponse({ text: selectedText })
        return false

      case 'GET_AUTH_TOKEN':
        // 获取localStorage中的认证token
        const token = localStorage.getItem('authToken') || localStorage.getItem('token')
        sendResponse({ token: token })
        return false

      case 'CLEAR_AUTH_TOKEN':
        // 清除localStorage中的认证token
        localStorage.removeItem('authToken')
        localStorage.removeItem('token')
        localStorage.removeItem('userData')
        localStorage.removeItem('user')
        sendResponse({ success: true })
        return false

      case 'SHOW_PLUGIN_SIDEBAR':
        // 显示插件侧边栏
        console.log('📱 收到显示插件侧边栏消息')
        if (!sidebarInjected) {
          injectSidebar()
        } else {
          openSidebar()
        }
        sendResponse({ success: true })
        return false

      case 'BROADCAST_AUTH_CHANGE':
        // 处理来自background script的认证状态广播
        if (authChannel) {
          try {
            authChannel.postMessage(message.data)
          } catch (error) {
            console.warn('BroadcastChannel发送失败:', error.message)
          }
        }
        // 直接处理认证状态变化
        handleAuthStateChange(message.data)
        sendResponse({ success: true })
        return false

      case 'SHOW_NOTIFICATION':
        showPageNotification(message.message, message.notificationType || 'info')
        sendResponse({ success: true })
        return false

      case 'OPEN_SIDEBAR_MY_PROMPTS':
        // 打开侧边栏并切换到我的提示词标签
        if (!sidebarInjected) {
          injectSidebar()
        } else {
          openSidebar()
        }
        // 延迟切换标签，确保侧边栏已加载
        setTimeout(() => {
          const sidebar = document.querySelector('.prompthub-sidebar')
          if (sidebar) {
            const iframe = sidebar.querySelector('iframe')
            if (iframe && iframe.contentWindow) {
              // 向iframe发送切换到我的提示词标签的消息
              iframe.contentWindow.postMessage({
                type: 'SWITCH_TO_MY_PROMPTS'
              }, '*')
            }
          }
        }, 500)
        sendResponse({ success: true })
        return false



      case 'TOGGLE_SIDEBAR':
        toggleSidebar()
        sendResponse({ success: true })
        return false

      case 'OPEN_SIDEBAR':
        if (!sidebarInjected) {
          injectSidebar()
        } else {
          openSidebar()
        }
        sendResponse({ success: true })
        return false

      case 'CLOSE_SIDEBAR':
        closeSidebar()
        sendResponse({ success: true })
        return false

      case 'RELOAD_SIDEBAR':
        reloadSidebar()
        sendResponse({ success: true })
        return false

      case 'HIDE_SIDEBAR':
        closeSidebar()
        sendResponse({ success: true })
        return false

      case 'SHOW_USE_PROMPT_MODAL':
        showFullScreenPromptModal(message.prompt, message.currentTab)
          .then(() => {
            if (chrome.runtime?.id) {
              sendResponse({ success: true })
            }
          })
          .catch((error) => {
            if (chrome.runtime?.id) {
              sendResponse({ success: false, error: error.message })
            }
          })
        return true // 异步响应

      case 'TOGGLE_FLOATING_BUTTON':
        toggleFloatingButton(message.show)
        sendResponse({ success: true })
        return false

      default:
        sendResponse({ success: false, error: 'Unknown message type' })
        return false
    }
  } catch (error) {
    console.warn('处理消息时出错:', error.message)
    try {
      sendResponse({ success: false, error: error.message })
    } catch (responseError) {
      // 响应发送失败，忽略
    }
    return false
  }
})

// 处理将选中文本作为提示词复制
function handleCopyTextAsPrompt(text) {
  if (!text) {
    text = window.getSelection().toString();
  }

  if (text.length > 0) {
    // 复制到剪贴板
    navigator.clipboard.writeText(text).then(() => {
      showPageNotification('✅ 文本已复制为提示词', 'success');
    }).catch(() => {
      showPageNotification('❌ 复制失败', 'error');
    });
  } else {
    showPageNotification('⚠️ 请先选择要复制的文本', 'warning');
  }
}

// 填充提示词到活跃元素
function fillPromptToActiveElement(content) {
  const activeElement = document.activeElement;

  // 查找可能的输入框
  const inputElements = [
    activeElement,
    document.querySelector('input[type="text"]:focus'),
    document.querySelector('textarea:focus'),
    document.querySelector('[contenteditable="true"]:focus'),
    // 常见的AI工具输入框选择器
    document.querySelector('textarea[placeholder*="prompt"]'),
    document.querySelector('textarea[placeholder*="提示"]'),
    document.querySelector('input[placeholder*="prompt"]'),
    document.querySelector('input[placeholder*="提示"]'),
    // ChatGPT相关选择器
    document.querySelector('#prompt-textarea'),
    document.querySelector('textarea[data-id]'),
    // Claude相关选择器
    document.querySelector('textarea[placeholder*="Talk to Claude"]'),
    // Midjourney相关选择器
    document.querySelector('input[placeholder*="Imagine"]')
  ].filter(Boolean);

  const targetElement = inputElements[0];

  if (targetElement) {
    // 设置值
    if (targetElement.tagName === 'TEXTAREA' || targetElement.tagName === 'INPUT') {
      // 保存当前光标位置
      const start = targetElement.selectionStart || 0;
      const end = targetElement.selectionEnd || 0;

      // 插入内容
      const currentValue = targetElement.value;
      const newValue = currentValue.substring(0, start) + content + currentValue.substring(end);

      targetElement.value = newValue;

      // 设置新的光标位置
      const newCursorPos = start + content.length;
      targetElement.setSelectionRange(newCursorPos, newCursorPos);

      // 触发事件
      targetElement.dispatchEvent(new Event('input', { bubbles: true }));
      targetElement.dispatchEvent(new Event('change', { bubbles: true }));
    } else if (targetElement.contentEditable === 'true') {
      // 处理可编辑div
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        range.insertNode(document.createTextNode(content));
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
      } else {
        targetElement.textContent += content;
      }
      targetElement.dispatchEvent(new Event('input', { bubbles: true }));
    }

    // 聚焦到输入框
    targetElement.focus();

    // 提示词已填充到输入框
    showPageNotification('✅ 提示词已填充', 'success');
  } else {
    // 如果没有找到输入框，则复制到剪贴板
    navigator.clipboard.writeText(content).then(() => {
      showPageNotification('📋 未找到输入框，已复制到剪贴板', 'info');
    }).catch(() => {
      showPageNotification('❌ 操作失败', 'error');
    });
  }
}



// 获取认证状态（通过background script的AuthManager）
async function getAuthState() {
  try {
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ type: 'GET_AUTH_STATUS' }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });

    return {
      isLoggedIn: response?.isLoggedIn || false,
      user: response?.user || null,
      token: response?.token || null
    };
  } catch (error) {
    // 获取认证状态失败
    return { isLoggedIn: false, user: null, token: null };
  }
}

// 检查用户是否已登录
async function isUserLoggedIn() {
  const authState = await getAuthState();
  return authState.isLoggedIn;
}

// 显示全屏使用提示词弹窗
async function showFullScreenPromptModal(prompt, currentTab = 'public-prompts') {
  // 显示全屏提示词弹窗

  // 先调用API获取完整的提示词信息（这会自动增加浏览量）
  let fullPromptData = prompt;
  try {
    const response = await makeApiRequest(`/prompts/${prompt.id}`);
    if (response && response.success && response.data) {
      fullPromptData = response.data;
    }
  } catch (error) {
    console.error('获取提示词详情失败:', error);
    // 如果获取失败，继续使用原有数据
  }

  const isLoggedIn = await isUserLoggedIn()
  // 检查用户登录状态

  // 移除已存在的弹窗
  const existingModal = document.getElementById('prompthub-fullscreen-modal')
  if (existingModal) {
    existingModal.remove()
  }

  // 创建全屏弹窗
  const modal = document.createElement('div')
  modal.id = 'prompthub-fullscreen-modal'

  // 弹窗HTML结构
  modal.innerHTML = `
    <div class="prompthub-modal-overlay">
      <div class="prompthub-modal-container">
        <!-- 头部 -->
        <div class="prompthub-modal-header">
          <div class="prompthub-modal-brand">
            <div class="prompthub-brand-icon">
              <svg class="prompthub-brand-svg" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/>
                <path d="M5 3v4"/>
                <path d="M19 17v4"/>
                <path d="M3 5h4"/>
                <path d="M17 19h4"/>
              </svg>
            </div>
            <div class="prompthub-brand-text">
              <h1 class="prompthub-brand-title">PromptHub</h1>
              <div class="prompthub-brand-subtitle">AI提示词分享、创作平台</div>
            </div>
          </div>
          <button class="prompthub-modal-close" id="prompthub-close-modal">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- 内容 -->
        <div class="prompthub-modal-content">
          <div class="prompthub-input-section">
            <label class="prompthub-input-label">系统提示词</label>
            <textarea
              class="prompthub-textarea"
              id="prompthub-system-prompt"
              rows="6"
            >${fullPromptData.content || ''}</textarea>
          </div>

          <div class="prompthub-input-section">
            <label class="prompthub-input-label">你的需求</label>
            <textarea
              class="prompthub-textarea"
              id="prompthub-user-prompt"
              placeholder="请在这里输入你的具体需求..."
              rows="4"
            ></textarea>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="prompthub-modal-footer">
          <div class="prompthub-modal-actions-left">
            <button class="prompthub-btn prompthub-btn-like" id="prompthub-like-btn" data-prompt-id="${fullPromptData.id || ''}">
              <span class="prompthub-like-icon">❤️</span>
              <span class="prompthub-like-text">点赞</span>
            </button>
            ${currentTab !== 'my-prompts' ? `
            <button class="prompthub-btn prompthub-btn-copy-to-my" id="prompthub-copy-to-my-btn" data-prompt-id="${fullPromptData.id || ''}" ${!isLoggedIn ? 'data-requires-login="true"' : ''}>
              <span>📋</span>
              <span>${isLoggedIn ? '复制到我的提示词' : '登录后复制'}</span>
            </button>
            ` : ''}
          </div>
          <div class="prompthub-modal-actions-right">
            <button class="prompthub-btn prompthub-btn-secondary" id="prompthub-cancel-btn">
              取消
            </button>
            <button class="prompthub-btn prompthub-btn-primary" id="prompthub-copy-btn" data-prompt-id="${fullPromptData.id || ''}">
              复制到剪贴板
            </button>
          </div>
        </div>
      </div>
    </div>
  `

  // 添加样式
  addFullScreenModalStyles()

  // 添加到页面
  document.body.appendChild(modal)

  // 添加事件监听器
  setupFullScreenModalEvents(modal, prompt, currentTab)

  // 获取并设置点赞状态
  if (prompt.id) {
    checkLikeStatus(modal, prompt.id)
  }

  // 显示动画
  setTimeout(() => {
    modal.classList.add('prompthub-modal-show')
  }, 10)

  // 全屏弹窗已显示
}

// 检查点赞状态
async function checkLikeStatus(modal, promptId) {
  try {
    const response = await makeApiRequest(`/prompts/${promptId}`)
    if (response.success && response.data) {
      const likeBtn = modal.querySelector('#prompthub-like-btn')
      const likeText = likeBtn.querySelector('.prompthub-like-text')

      if (response.data.isLiked) {
        likeBtn.classList.add('liked')
        likeText.textContent = '已点赞'
      } else {
        likeBtn.classList.remove('liked')
        likeText.textContent = '点赞'
      }
    }
  } catch (error) {
    console.error('获取点赞状态失败:', error)
    // 静默失败，不影响用户体验
  }
}

// 显示页面通知
function showPageNotification(message, type = 'info') {
  // 移除已存在的通知
  const existingNotification = document.getElementById('prompthub-notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  // 创建通知元素
  const notification = document.createElement('div');
  notification.id = 'prompthub-notification';
  notification.textContent = message;

  // 样式
  Object.assign(notification.style, {
    position: 'fixed',
    top: '20px',
    left: '50%',
    transform: 'translateX(-50%)',
    padding: '12px 24px',
    backgroundColor: type === 'success' ? '#10B981' :
                    type === 'error' ? '#EF4444' :
                    type === 'warning' ? '#F59E0B' : '#3B82F6',
    color: 'white',
    borderRadius: '12px',
    fontSize: '14px',
    fontWeight: '500',
    zIndex: '99999999',
    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    animation: 'prompthub-notification-slide-in 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
  });

  // 强制设置最高z-index
  notification.style.setProperty('z-index', '99999999', 'important');

  // 添加动画样式
  if (!document.getElementById('prompthub-notification-styles')) {
    const style = document.createElement('style');
    style.id = 'prompthub-notification-styles';
    style.textContent = `
      @keyframes prompthub-notification-slide-in {
        from {
          opacity: 0;
          transform: translateX(-50%) translateY(-20px) scale(0.9);
        }
        to {
          opacity: 1;
          transform: translateX(-50%) translateY(0) scale(1);
        }
      }

      @keyframes prompthub-notification-slide-out {
        from {
          opacity: 1;
          transform: translateX(-50%) translateY(0) scale(1);
        }
        to {
          opacity: 0;
          transform: translateX(-50%) translateY(-20px) scale(0.9);
        }
      }
    `;
    document.head.appendChild(style);
  }

  // 添加到页面
  document.body.appendChild(notification);

  // 3秒后移除
  setTimeout(() => {
    if (notification.parentNode) {
      notification.style.animation = 'prompthub-notification-slide-out 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
      setTimeout(() => {
        notification.remove();
      }, 300);
    }
  }, 3000);
}

// 监听键盘快捷键
document.addEventListener('keydown', (e) => {
  // Ctrl+Shift+P 或 Cmd+Shift+P (切换侧边栏)
  if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
    e.preventDefault();
    toggleSidebar();
  }
});

// 添加全屏弹窗样式
function addFullScreenModalStyles() {
  if (document.getElementById('prompthub-fullscreen-modal-styles')) return

  const style = document.createElement('style')
  style.id = 'prompthub-fullscreen-modal-styles'
  style.textContent = `
    #prompthub-fullscreen-modal {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      z-index: 999999 !important;
      opacity: 0 !important;
      visibility: hidden !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    #prompthub-fullscreen-modal.prompthub-modal-show {
      opacity: 1 !important;
      visibility: visible !important;
    }

    .prompthub-modal-overlay {
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      background: rgba(0, 0, 0, 0.75) !important;
      backdrop-filter: blur(8px) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      padding: 20px !important;
      box-sizing: border-box !important;
    }

    .prompthub-modal-container {
      background: white !important;
      border-radius: 16px !important;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25) !important;
      width: 100% !important;
      max-width: 800px !important;
      max-height: 85vh !important;
      overflow: hidden !important;
      transform: scale(0.9) translateY(20px) !important;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      display: flex !important;
      flex-direction: column !important;
    }

    #prompthub-fullscreen-modal.prompthub-modal-show .prompthub-modal-container {
      transform: scale(1) translateY(0) !important;
    }

    .prompthub-modal-header {
      display: flex !important;
      align-items: center !important;
      justify-content: space-between !important;
      padding: 24px 32px !important;
      border-bottom: 1px solid #e5e7eb !important;
      background: white !important;
      color: #1f2937 !important;
    }

    .prompthub-modal-brand {
      display: flex !important;
      align-items: center !important;
      gap: 12px !important;
    }

    .prompthub-brand-icon {
      position: relative !important;
      width: 40px !important;
      height: 40px !important;
      background: linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%) !important;
      border-radius: 12px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    .prompthub-brand-icon::after {
      content: '' !important;
      position: absolute !important;
      top: -4px !important;
      right: -4px !important;
      width: 16px !important;
      height: 16px !important;
      background: #10b981 !important;
      border-radius: 50% !important;
      border: 2px solid white !important;
    }

    .prompthub-brand-svg {
      width: 24px !important;
      height: 24px !important;
      color: white !important;
    }

    .prompthub-brand-title {
      font-size: 24px !important;
      font-weight: 700 !important;
      margin: 0 !important;
      background: linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%) !important;
      -webkit-background-clip: text !important;
      background-clip: text !important;
      -webkit-text-fill-color: transparent !important;
      color: transparent !important;
    }

    .prompthub-brand-subtitle {
      font-size: 12px !important;
      color: #6b7280 !important;
      font-weight: 500 !important;
      margin: 0 !important;
    }

    .prompthub-modal-close {
      width: 40px !important;
      height: 40px !important;
      border: none !important;
      background: #f3f4f6 !important;
      border-radius: 8px !important;
      color: #6b7280 !important;
      cursor: pointer !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      transition: all 0.2s !important;
    }

    .prompthub-modal-close:hover {
      background: #e5e7eb !important;
      color: #374151 !important;
    }

    .prompthub-modal-close svg {
      width: 20px !important;
      height: 20px !important;
    }

    .prompthub-modal-content {
      padding: 32px !important;
      overflow-y: auto !important;
      flex: 1 !important;
      min-height: 0 !important;
    }

    .prompthub-prompt-info {
      margin-bottom: 24px !important;
    }

    .prompthub-prompt-title {
      font-size: 20px !important;
      font-weight: 600 !important;
      color: #1f2937 !important;
      margin: 0 0 8px 0 !important;
    }

    .prompthub-prompt-description {
      color: #6b7280 !important;
      margin: 0 !important;
      line-height: 1.5 !important;
    }

    .prompthub-input-section {
      margin-bottom: 24px !important;
    }

    .prompthub-input-label {
      display: block !important;
      font-size: 14px !important;
      font-weight: 600 !important;
      color: #374151 !important;
      margin-bottom: 8px !important;
    }

    .prompthub-textarea {
      width: 100% !important;
      padding: 12px 16px !important;
      border: 2px solid #e5e7eb !important;
      border-radius: 8px !important;
      font-size: 14px !important;
      line-height: 1.5 !important;
      resize: vertical !important;
      transition: border-color 0.2s !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      box-sizing: border-box !important;
    }

    .prompthub-textarea:focus {
      outline: none !important;
      border-color: #667eea !important;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    }

    .prompthub-textarea[readonly] {
      background-color: #f9fafb !important;
      color: #374151 !important;
    }

    .prompthub-modal-footer {
      padding: 24px 32px !important;
      border-top: 1px solid #e5e7eb !important;
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
    }

    .prompthub-modal-actions-left {
      display: flex !important;
      gap: 12px !important;
    }

    .prompthub-modal-actions-right {
      display: flex !important;
      gap: 12px !important;
    }

    .prompthub-btn {
      padding: 12px 24px !important;
      border-radius: 8px !important;
      font-size: 14px !important;
      font-weight: 600 !important;
      cursor: pointer !important;
      transition: all 0.2s !important;
      border: none !important;
    }

    .prompthub-btn-secondary {
      background: #f3f4f6 !important;
      color: #374151 !important;
    }

    .prompthub-btn-secondary:hover {
      background: #e5e7eb !important;
    }

    .prompthub-btn-primary {
      background: linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%) !important;
      color: white !important;
    }

    .prompthub-btn-primary:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 8px 25px rgba(124, 58, 237, 0.3) !important;
    }

    .prompthub-btn-like {
      background: #fef2f2 !important;
      color: #dc2626 !important;
      border: 1px solid #fecaca !important;
      display: flex !important;
      align-items: center !important;
      gap: 6px !important;
    }

    .prompthub-btn-like:hover {
      background: #fee2e2 !important;
      border-color: #fca5a5 !important;
    }

    .prompthub-btn-like.liked {
      background: #dc2626 !important;
      color: white !important;
      border-color: #dc2626 !important;
    }

    .prompthub-btn-copy-to-my {
      background: #f0f9ff !important;
      color: #0369a1 !important;
      border: 1px solid #bae6fd !important;
      display: flex !important;
      align-items: center !important;
      gap: 6px !important;
    }

    .prompthub-btn-copy-to-my:hover {
      background: #e0f2fe !important;
      border-color: #7dd3fc !important;
    }
  `
  document.head.appendChild(style)
}

// 设置全屏弹窗事件
function setupFullScreenModalEvents(modal, prompt, currentTab = 'public-prompts') {
  const closeBtn = modal.querySelector('#prompthub-close-modal')
  const cancelBtn = modal.querySelector('#prompthub-cancel-btn')
  const copyBtn = modal.querySelector('#prompthub-copy-btn')
  const likeBtn = modal.querySelector('#prompthub-like-btn')
  const copyToMyBtn = modal.querySelector('#prompthub-copy-to-my-btn')
  const systemPrompt = modal.querySelector('#prompthub-system-prompt')
  const userPrompt = modal.querySelector('#prompthub-user-prompt')

  // 关闭弹窗
  const closeModal = () => {
    modal.classList.remove('prompthub-modal-show')
    setTimeout(() => {
      modal.remove()
    }, 300)
  }

  // 复制内容
  const copyContent = async () => {
    const systemText = systemPrompt.value.trim()
    const userText = userPrompt.value.trim()

    let combinedText = systemText
    if (userText) {
      combinedText += '\n\n---\n\n' + userText
    }

    try {
      await navigator.clipboard.writeText(combinedText)
      showPageNotification('✅ 内容已复制到剪贴板', 'success')

      // 记录复制次数
      if (prompt.id) {
        try {
          await makeApiRequest(`/prompts/${prompt.id}/copy`, { method: 'POST' })
        } catch (error) {
          console.error('记录复制次数失败:', error)
        }
      }

      closeModal()
    } catch (error) {
      showPageNotification('❌ 复制失败', 'error')
    }
  }

  // 点赞功能
  const handleLike = async () => {
    // 开始点赞操作

    // 检查登录状态（使用统一认证架构）
    const authState = await getAuthState()
    // 检查当前认证状态

    if (!authState.isLoggedIn) {
      showPageNotification('请先登录后再点赞', 'error')
      return
    }

    try {
      const response = await makeApiRequest(`/prompts/${prompt.id}/like`, { method: 'POST' })

      // 点赞响应处理

      if (response.success) {
        const likeIcon = likeBtn.querySelector('.prompthub-like-icon')
        const likeText = likeBtn.querySelector('.prompthub-like-text')

        if (response.data.isLiked) {
          likeBtn.classList.add('liked')
          likeText.textContent = '已点赞'
          showPageNotification('❤️ 点赞成功', 'success')
        } else {
          likeBtn.classList.remove('liked')
          likeText.textContent = '点赞'
          showPageNotification('💔 取消点赞', 'success')
        }
      } else {
        // 处理业务逻辑失败的情况
        const errorMessage = response.message || '点赞失败'
        console.error('点赞业务失败:', errorMessage)

        if (errorMessage.includes('登录') || errorMessage.includes('认证') || errorMessage.includes('权限')) {
          showPageNotification('请先登录后再点赞', 'error')
        } else {
          showPageNotification(errorMessage, 'error')
        }
      }
    } catch (error) {
      console.error('点赞请求失败:', error)
      if (error.message.includes('请先登录') || error.message.includes('登录') || error.message.includes('认证') || error.message.includes('401')) {
        showPageNotification('请先登录后再点赞', 'error')
      } else {
        showPageNotification('点赞失败，请重试', 'error')
      }
    }
  }

  // 复制到我的提示词
  const handleCopyToMyPrompts = async () => {
    // 开始复制到我的提示词

    // 检查登录状态（使用统一认证架构）
    const authState = await getAuthState()
    // 检查当前认证状态

    if (!authState.isLoggedIn) {
      showPageNotification('请先登录后再复制到我的提示词', 'error')
      return
    }

    try {
      // 改进的数据验证 - 提供默认值而不是直接抛出错误（与首页逻辑保持一致）
      const title = prompt.title?.trim() || '未命名提示词'
      const content = prompt.content?.trim() || '暂无内容'

      // 保持原有分类，只有在分类真正为空时才使用默认值
      let category = 'ai-assistant' // 默认分类
      if (prompt.category && typeof prompt.category === 'string' && prompt.category.trim()) {
        category = prompt.category.trim()
      }

      // 分类处理完成，准备复制请求

      const response = await makeApiRequest('/prompts', {
        method: 'POST',
        body: JSON.stringify({
          title: `${title}（副本）`,
          description: prompt.description?.trim() || '从共享提示词复制而来',
          content: content,
          category: category,
          tags: Array.isArray(prompt.tags) ? prompt.tags : [],
          isPrivate: true,
          status: 'draft'
        })
      })

      // 复制到我的提示词响应处理

      if (response.success) {
        showPageNotification('✅ 已复制到我的提示词（私密草稿）', 'success')
      } else {
        // 处理业务逻辑失败的情况
        const errorMessage = response.message || '复制失败'
        console.error('复制到我的提示词业务失败:', errorMessage)

        if (errorMessage.includes('登录') || errorMessage.includes('认证') || errorMessage.includes('权限')) {
          showPageNotification('请先登录后再复制到我的提示词', 'error')
        } else {
          showPageNotification(errorMessage, 'error')
        }
      }
    } catch (error) {
      console.error('复制到我的提示词请求失败:', error)
      if (error.message.includes('请先登录') || error.message.includes('登录') || error.message.includes('认证') || error.message.includes('401')) {
        showPageNotification('请先登录后再复制到我的提示词', 'error')
      } else {
        showPageNotification('复制失败，请重试', 'error')
      }
    }
  }

  // 事件监听
  closeBtn.addEventListener('click', closeModal)
  cancelBtn.addEventListener('click', closeModal)
  copyBtn.addEventListener('click', copyContent)
  likeBtn.addEventListener('click', handleLike)

  // 只有当"复制到我的提示词"按钮存在时才绑定事件
  if (copyToMyBtn) {
    copyToMyBtn.addEventListener('click', handleCopyToMyPrompts)
  }

  // 点击遮罩关闭
  modal.querySelector('.prompthub-modal-overlay').addEventListener('click', (e) => {
    if (e.target === e.currentTarget) {
      closeModal()
    }
  })

  // ESC键关闭
  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      closeModal()
      document.removeEventListener('keydown', handleKeyDown)
    }
  }
  document.addEventListener('keydown', handleKeyDown)

  // 聚焦到用户输入框
  setTimeout(() => {
    userPrompt.focus()
  }, 100)
}

// 监听来自主站的登录成功消息
window.addEventListener('message', (event) => {
    // 只处理来自同源的消息
    if (event.origin !== window.location.origin) {
        return;
    }

    const message = event.data;
    if (message && message.type === 'LOGIN_SUCCESS') {
        // 内容脚本收到登录成功消息

        // 转发给background script
        chrome.runtime.sendMessage({
            type: 'LOGIN_SUCCESS',
            token: message.token,
            user: message.user
        }).catch(error => {
            console.error('转发登录消息失败:', error);
        });
    }
});

// 监听自定义事件（从主站回调页面发送）
window.addEventListener('prompthub-login-success', (event) => {
    const message = event.detail;
    if (message && message.type === 'LOGIN_SUCCESS') {
        // 转发给background script
        chrome.runtime.sendMessage({
            type: 'LOGIN_SUCCESS',
            token: message.token,
            user: message.user
        }, (response) => {
            if (chrome.runtime.lastError) {
                console.error('❌ 转发登录消息失败:', chrome.runtime.lastError.message);
            }
        });
    }
});

// PromptHub 内容脚本初始化完成
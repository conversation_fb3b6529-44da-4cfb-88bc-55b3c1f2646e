/* PromptHub 浏览器插件 - Popup 样式 */

/* 重置样式 */
* {
  box-sizing: border-box;
}

/* 基础样式 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f9fafb;
  margin: 0;
  padding: 0;
  width: 384px !important;
  height: 600px;
  min-height: 600px;
  max-height: 600px;
  overflow: hidden;
  box-sizing: border-box;
}

/* Utility Classes */
.w-96 { width: 24rem; }
.h-full { height: 100%; }
.w-full { width: 100%; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-1 { flex: 1 1 0%; }
.flex-shrink-0 { flex-shrink: 0; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.pl-3 { padding-left: 0.75rem; }
.pl-10 { padding-left: 2.5rem; }
.pr-4 { padding-right: 1rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mr-2 { margin-right: 0.5rem; }
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-300 { background-color: #d1d5db; }
.bg-gray-900 { background-color: #111827; }
.bg-red-600 { background-color: #dc2626; }
.text-white { color: #ffffff; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-md { font-size: 1rem; line-height: 1.5rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.border { border-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-b-2 { border-bottom-width: 2px; }
.border-t { border-top-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-transparent { border-color: transparent; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-full { border-radius: 9999px; }
.rounded-b-lg { border-bottom-left-radius: 0.5rem; border-bottom-right-radius: 0.5rem; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.overflow-hidden { overflow: hidden; }
.overflow-y-auto { overflow-y: auto; }
.overflow-y-hidden { overflow-y: hidden; }
.cursor-pointer { cursor: pointer; }
.select-none { user-select: none; }
.appearance-none { appearance: none; }
.outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { box-shadow: 0 0 0 3px rgba(147, 197, 253, 0.5); }
.focus\:ring-purple-400:focus { box-shadow: 0 0 0 3px rgba(196, 181, 253, 0.5); }
.focus\:ring-purple-500:focus { box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.5); }
.hover\:text-gray-700:hover { color: #374151; }
.text-center { text-align: center; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.inset-y-0 { top: 0; bottom: 0; }
.left-0 { left: 0; }
.left-1\/2 { left: 50%; }
.top-5 { top: 1.25rem; }
.-translate-x-1\/2 { transform: translateX(-50%); }
.z-50 { z-index: 50; }
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.w-10 { width: 2.5rem; }
.w-6 { width: 1.5rem; }
.h-6 { height: 1.5rem; }
.h-5 { width: 1.25rem; height: 1.25rem; }
.h-16 { height: 4rem; }
.max-w-2xl { max-width: 42rem; }
.mx-4 { margin-left: 1rem; margin-right: 1rem; }
.pointer-events-none { pointer-events: none; }

/* 头部样式 */
#sidebar-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px !important;
}

/* 确保渐变文字效果正确显示 */
.brand-title {
  background: linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%) !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  color: transparent !important;
}

/* 登录按钮样式 - 与主站一致 */
#login-btn {
  background: linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

#login-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
}

/* 搜索框样式 */
#search-input {
  width: 100%;
  padding: 8px 12px 8px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

#search-input:focus {
  outline: none;
  border-color: #6d28d9;
  box-shadow: 0 0 0 3px rgba(109, 40, 217, 0.1);
}

/* 标签页样式 */
.tab-btn {
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  color: #374151;
}

/* 卡片内容样式 */
.card-body {
  margin-bottom: 12px;
}

.card-body h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
  word-wrap: break-word; /* 确保长标题能够换行 */
  word-break: break-word; /* 确保长标题能够换行 */
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制标题最多显示2行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-body p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 增加显示行数从2行到3行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word; /* 确保长单词能够换行 */
  word-break: break-word; /* 确保长单词能够换行 */
  max-height: 50.4px; /* 3行 * 16.8px (12px * 1.4行高) = 50.4px */
}

/* 卡片按钮样式 */
.card button {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
  background: white;
  color: #374151;
}

.card button:hover:not([data-action="use"]) {
  background: #f3f4f6;
}

.card button[data-action="use"] {
  background: linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%);
  color: white;
  border: none;
}

.card button[data-action="use"]:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);
}

/* 设置面板样式 */
#settings-panel {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

/* 开关样式已使用 Tailwind CSS 原生类实现 */

/* 额外的工具类 */
.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.rounded-full { border-radius: 9999px; }
.opacity-0 { opacity: 0; }
.opacity-100 { opacity: 1; }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.origin-top-right { transform-origin: top right; }
.ring-1 { box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05); }
.ring-black { --tw-ring-color: rgba(0, 0, 0, 1); }
.ring-opacity-5 { --tw-ring-opacity: 0.05; }
.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:bg-red-50:hover { background-color: #fef2f2; }
.text-red-600 { color: #dc2626; }
.border-gray-100 { border-color: #f3f4f6; }
.my-1 { margin-top: 0.25rem; margin-bottom: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.w-48 { width: 12rem; }
.z-10 { z-index: 10; }
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.h-12 { height: 3rem; }
.w-12 { width: 3rem; }
.stroke-width-1 { stroke-width: 1; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-6 { margin-top: 1.5rem; }
.py-1\.5 { padding-top: 0.375rem; padding-bottom: 0.375rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.mb-1\.5 { margin-bottom: 0.375rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.mb-3 { margin-bottom: 0.75rem; }
.pt-2 { padding-top: 0.5rem; }
.border-gray-100 { border-color: #f3f4f6; }
.hover\:text-purple-600:hover { color: #9333ea; }

#prompt-sidebar {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease-in-out;
  will-change: transform;
}

/* 模态框背景 */
.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.no-scroll {
  overflow: hidden;
}

/* 按钮样式 - 与主站一致 */
.btn-primary {
  background-image: linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%);
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  box-shadow: 0 6px 20px 0 rgba(124, 58, 237, 0.15);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
}

/* 活跃标签页 - 与主站一致 */
.active-tab {
  border-bottom-color: #7c3aed;
  color: #7c3aed;
  font-weight: 600;
}



/* 下拉菜单 */
.dropdown-menu {
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}

/* 用户头像下拉菜单位置修复 - 向左展示避免被截断 */
#user-dropdown {
  /* 位置对齐 */
  right: 20px !important; /* 向左偏移 20px，避免被边界截断 */
  left: auto !important;
  transform-origin: top right !important;
  /* 移除 translateX 避免与 scale 类冲突导致闪烁 */
}

/* 加载器 */
.loader {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #6d28d9;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 卡片样式 */
.card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  min-height: auto;
  max-height: 150px; /* 限制卡片最大高度 */
  overflow: hidden; /* 隐藏超出部分 */
}

.card:hover {
  border-color: #6d28d9;
  box-shadow: 0 2px 8px rgba(109, 40, 217, 0.1);
}

/* 按钮组 */
.card .flex {
  margin-top: 8px;
}

/* 卡片按钮样式 */
.card button {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.card button:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.card a {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-block;
}

.card a:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* 空状态和错误状态 */
.text-center {
  text-align: center;
}

/* 主内容区域样式 */
#prompt-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #f9fafb;
  min-height: 0; /* 确保flex子元素可以收缩 */
  /* 防止滚动穿透 */
  overscroll-behavior: contain;
}

/* 间距工具类 */
.space-y-3 > * + * {
  margin-top: 0.75rem;
}

/* 滚动条样式 */
#prompt-list-container::-webkit-scrollbar {
  width: 6px;
}

#prompt-list-container::-webkit-scrollbar-track {
  background: #f1f5f9;
}

#prompt-list-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

#prompt-list-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 响应式调整 */
@media (max-width: 440px) {
  body {
    width: 380px;
  }
}

/* 用户头像样式优化 */
#user-menu-btn img {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  object-fit: cover !important;
}

/* 滑动开关样式 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 开关背景 */
input[type="checkbox"].peer + div {
  width: 44px;
  height: 24px;
  background-color: #d1d5db;
  border-radius: 12px;
  position: relative;
  transition: background-color 0.2s;
}

/* 开关选中状态背景 */
input[type="checkbox"].peer:checked + div {
  background-color: #9333ea;
}

/* 开关滑块 */
input[type="checkbox"].peer + div::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 开关选中状态滑块 */
input[type="checkbox"].peer:checked + div::after {
  transform: translateX(20px);
}

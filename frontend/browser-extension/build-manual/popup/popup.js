document.addEventListener('DOMContentLoaded', () => {
    // 检测是否在独立popup窗口中运行
    const isStandalonePopup = window.top === window && window.opener === null && window.parent === window

    if (isStandalonePopup) {
        // 独立popup窗口：调整尺寸，与侧边栏宽度保持一致
        const body = document.getElementById('popup-body')
        if (body) {
            body.style.width = '384px'
            body.style.height = '600px'
            body.style.minHeight = '600px'
        }
        console.log('🔧 独立popup模式，尺寸已调整为384x600')
    } else {
        console.log('🔧 侧边栏iframe模式，保持100vh高度')
    }

    // 检查扩展上下文是否有效
    if (!chrome.runtime?.id) {
        console.error('❌ 扩展上下文已失效，请重新加载扩展');
        // 显示错误提示
        const errorDiv = document.createElement('div');
        errorDiv.innerHTML = `
            <div style="padding: 20px; text-align: center; color: #666; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                <h3 style="margin: 0 0 10px 0; color: #333;">扩展需要重新加载</h3>
                <p style="margin: 0 0 15px 0; font-size: 14px;">请在扩展管理页面重新加载 PromptHub 扩展</p>
                <button onclick="window.close()" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">关闭</button>
            </div>
        `;
        document.body.innerHTML = '';
        document.body.appendChild(errorDiv);
        return;
    }

    // --- API Configuration ---
    // 使用统一配置，兼容不同环境
    const PLUGIN_CONFIG = typeof PROMPTHUB_CONFIG !== 'undefined' ? PROMPTHUB_CONFIG : (typeof CONFIG !== 'undefined' ? CONFIG : null)

    if (!PLUGIN_CONFIG) {
        console.error('❌ 插件配置未找到')
        return
    }

    const API_BASE_URL = PLUGIN_CONFIG.API.BASE_URL
    const FRONTEND_URL = PLUGIN_CONFIG.FRONTEND.BASE_URL
    const PAGE_SIZE = PLUGIN_CONFIG.PAGINATION.PAGE_SIZE

    // 插件API配置加载完成

    // --- DOM Elements ---
    const sidebarHeader = document.getElementById('sidebar-header');
    const promptListContainer = document.getElementById('prompt-list-container');
    const searchInput = document.getElementById('search-input');
    const clearSearchBtn = document.getElementById('clear-search-btn');
    const tabs = document.querySelectorAll('.tab-btn');
    const toggleFloatingBtn = document.getElementById('toggle-floating-btn');
    const usePromptModal = document.getElementById('use-prompt-modal');
    const systemPromptInput = document.getElementById('system-prompt-input');
    const userPromptInput = document.getElementById('user-prompt-input');
    const useModalCancelBtn = document.getElementById('use-modal-cancel-btn');
    const useModalCopyBtn = document.getElementById('use-modal-copy-btn');
    const copyFeedback = document.getElementById('copy-feedback');
    const loadingIndicator = document.getElementById('loading-indicator');

    // --- 状态恢复 ---
    // 从localStorage恢复状态
    const savedState = localStorage.getItem('prompthub-popup-state');
    let restoredState = null;
    if (savedState) {
        try {
            restoredState = JSON.parse(savedState);
        } catch (e) {
            console.warn('Failed to parse saved state:', e);
        }
    }

    // --- 消息发送辅助函数 ---
    const sendMessageToContentScript = (message, callback) => {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (chrome.runtime.lastError) {
                console.error('❌ 查询标签页失败:', chrome.runtime.lastError.message);
                if (callback) callback(null, chrome.runtime.lastError);
                return;
            }

            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, message, (response) => {
                    if (chrome.runtime.lastError) {
                        console.warn('内容脚本未响应:', chrome.runtime.lastError.message);
                        if (callback) callback(null, chrome.runtime.lastError);
                        return;
                    }
                    if (callback) callback(response, null);
                });
            } else {
                const error = new Error('没有找到活跃标签页');
                if (callback) callback(null, error);
            }
        });
    };

    // --- API Helper Functions ---
    const getAuthToken = async () => {
        // 检查扩展上下文是否有效
        if (!chrome.runtime?.id) {
            console.warn('扩展上下文已失效，无法获取认证状态');
            return null;
        }

        // 插件通过chrome.cookies API读取同一张HttpOnly Cookie
        try {
            const response = await new Promise((resolve, reject) => {
                // 设置超时，避免无限等待
                const timeout = setTimeout(() => {
                    reject(new Error('获取认证状态超时'));
                }, 5000);

                chrome.runtime.sendMessage({ type: 'GET_AUTH_STATUS' }, (response) => {
                    clearTimeout(timeout);
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message))
                    } else {
                        resolve(response)
                    }
                })
            })

            console.log('🔍 Background认证响应:', response)

            if (response && response.success && response.isLoggedIn) {
                // 返回一个标识符表示使用Cookie认证
                return 'cookie-auth'
            }
        } catch (error) {
            console.warn('从background script获取认证状态失败:', error.message)
            // 如果是扩展上下文失效，直接返回null
            if (error.message.includes('Extension context invalidated') ||
                error.message.includes('扩展上下文已失效') ||
                !chrome.runtime?.id) {
                return null;
            }
        }

        return null;
    };

    const makeApiRequest = async (endpoint, options = {}) => {
        // 检查扩展上下文是否有效
        if (!chrome.runtime?.id) {
            throw new Error('扩展上下文已失效，无法发送API请求');
        }

        const token = await getAuthToken();
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };

        // 只有当token不是'cookie-auth'标识时才添加Authorization头
        if (token && token !== 'cookie-auth') {
            headers.Authorization = `Bearer ${token}`;
        }

        try {
            const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                ...options,
                headers,
                credentials: 'include', // 支持Cookie认证
                mode: 'cors'
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            return response.json();
        } catch (error) {
            console.error('API请求错误:', error);
            // 如果是扩展上下文失效，抛出特定错误
            if (!chrome.runtime?.id) {
                throw new Error('扩展上下文已失效');
            }
            throw error;
        }
    };

    // --- State ---
    let isLoggedIn = false;
    let currentUser = null;
    let currentTab = restoredState?.currentTab || 'public-prompts';
    let pagesLoaded = { my: 1, public: 1 };
    let isLoading = false;
    let hasMore = { my: true, public: true };
    let searchQuery = '';

    // 新增: 标记设置面板刚刚打开，避免被全局点击监听立即关闭
    let settingsPanelJustOpened = false;

    let isInitializing = false; // 添加初始化状态标志

    // 检查登录状态
    const checkAuthStatus = async () => {
        try {
            const token = await getAuthToken();
            if (!token) {
                isLoggedIn = false;
                return;
            }

            const response = await makeApiRequest('/auth/me');
            if (response.success) {
                isLoggedIn = true;
                currentUser = response.data;
            } else {
                isLoggedIn = false;
                // 避免触发storage事件导致无限循环，只在必要时清除token
                if (token !== 'cookie-auth') {
                    console.log('🗑️ 清除无效的token');
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('token');
                }
            }
        } catch (error) {
            console.error('检查认证状态失败:', error);
            isLoggedIn = false;
            // 只有在网络错误以外的情况下才清除token
            if (!error.message.includes('Failed to fetch') && !error.message.includes('ERR_INSUFFICIENT_RESOURCES')) {
                const token = await getAuthToken();
                if (token !== 'cookie-auth') {
                    console.log('🗑️ 清除无效的token');
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('token');
                }
            }
        }
    };

    // 保存状态到localStorage
    const saveState = () => {
        const state = {
            isLoggedIn,
            currentTab,
            timestamp: Date.now()
        };
        localStorage.setItem('prompthub-popup-state', JSON.stringify(state));
    };

    // --- Core Functions ---
    const renderHeader = () => {
        // 保存设置面板的当前状态
        const settingsPanel = document.getElementById('settings-panel');
        const settingsPanelWasVisible = settingsPanel && !settingsPanel.classList.contains('hidden');

        // 使用与主站完全一致的品牌图标和样式
        const brandHTML = `
          <div id="brand-logo" style="display: flex; align-items: center; gap: 12px; cursor: pointer; padding: 4px; border-radius: 8px; transition: background-color 0.2s;" title="访问 PromptHub 主站">
            <div style="position: relative;">
              <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                <svg style="width: 24px; height: 24px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/>
                  <path d="M5 3v4"/>
                  <path d="M19 17v4"/>
                  <path d="M3 5h4"/>
                  <path d="M17 19h4"/>
                </svg>
              </div>
              <div style="position: absolute; top: -4px; right: -4px; width: 16px; height: 16px; background: #10b981; border-radius: 50%; border: 2px solid white;"></div>
            </div>
            <div>
              <span style="font-size: 24px; font-weight: 700; background: linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; color: transparent;">
                PromptHub
              </span>
              <div style="font-size: 12px; color: #6b7280; font-weight: 500;">AI提示词分享、创作平台</div>
            </div>
          </div>
        `;
        let rightSideHTML;
        if (isLoggedIn) {
            // 使用真实用户信息
            const userName = currentUser?.username || currentUser?.email || '用户';

            rightSideHTML = `
              <div style="position: relative;">
                <button id="user-menu-btn" style="display: flex; align-items: center; gap: 6px; padding: 4px 8px; border-radius: 8px; border: none; background: transparent; cursor: pointer; transition: background-color 0.2s;">
                  <span style="font-size: 14px; color: #374151; font-weight: 500;">${userName}</span>
                  <svg style="width: 16px; height: 16px; color: #9ca3af;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                <div id="user-dropdown" style="display: none; position: absolute; right: 0; top: 100%; margin-top: 8px; width: 192px; background: white; border-radius: 6px; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); z-index: 9999; border: 1px solid rgba(0, 0, 0, 0.05); transform: scale(1); opacity: 1; transition: all 0.2s ease;">
                  <div style="padding: 4px 0;">
                    <a href="${PLUGIN_CONFIG.getFrontendUrl('/create')}" target="_blank" id="new-prompt-link" style="display: block; padding: 8px 16px; font-size: 14px; color: #374151; text-decoration: none; transition: background-color 0.2s;">新增提示词</a>
                    <a href="#" id="settings-menu-item" style="display: block; padding: 8px 16px; font-size: 14px; color: #374151; text-decoration: none; transition: background-color 0.2s;">设置</a>
                    <div style="border-top: 1px solid #f3f4f6; margin: 4px 0;"></div>
                    <a href="#" id="logout-btn" style="display: block; padding: 8px 16px; font-size: 14px; color: #dc2626; text-decoration: none; transition: background-color 0.2s;">退出登录</a>
                  </div>
                </div>
              </div>
            `;
        } else {
            rightSideHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <button id="login-btn" style="background: linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%); color: white; padding: 8px 16px; border-radius: 8px; border: none; font-size: 14px; font-weight: 600; cursor: pointer; transition: all 0.2s ease;">登录</button>
                </div>
            `;
        }
        sidebarHeader.innerHTML = `
          <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
            ${brandHTML}
            <div style="display: flex; align-items: center; gap: 8px;">
              ${rightSideHTML}
              <button id="close-plugin-btn" style="width: 32px; height: 32px; border: none; background: #f3f4f6; border-radius: 6px; color: #6b7280; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s;" title="关闭插件">
                <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
          </div>
        `;

        // 恢复设置面板的状态
        if (settingsPanelWasVisible) {
            setTimeout(() => {
                const newSettingsPanel = document.getElementById('settings-panel');
                if (newSettingsPanel) {
                    newSettingsPanel.classList.remove('hidden');
                    console.log('🔧 设置面板状态已恢复');
                }
            }, 0);
        }

        // 重新绑定头部按钮的事件监听器（因为innerHTML重新生成了DOM）
        bindHeaderEvents();
    };

    // 显示空状态
    const showEmptyState = () => {
        // 检查是否有搜索条件
        const isSearching = searchQuery && searchQuery.trim().length > 0;

        if (currentTab === 'my-prompts' && !isSearching) {
            // 我的提示词 - 无搜索条件时显示完整的空状态
            promptListContainer.innerHTML = `
                <div class="flex flex-col items-center justify-center py-12 px-4">
                    <div class="w-16 h-16 mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">还没有提示词</h3>
                    <p class="text-gray-500 text-center mb-4 text-sm leading-relaxed">
                        您还没有创建任何提示词。<br>
                        可以从公共库复制提示词，或者访问网站创建新的提示词。
                    </p>
                    <div class="flex flex-col gap-3 w-full max-w-xs">
                        <button id="browse-public-prompts-btn" class="px-6 py-3 text-white rounded-lg transition-all duration-200 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:scale-105" style="background: linear-gradient(135deg, #7c3aed 0%, #06b6d4 100%);">
                            🔍 浏览公共提示词
                        </button>
                        <a href="${PLUGIN_CONFIG.getFrontendUrl('/prompts/new')}" target="_blank" class="px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-200 text-sm font-semibold text-center border-2 border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md transform hover:scale-105">
                            ✨ 创建新提示词
                        </a>
                    </div>
                </div>
            `;
        } else {
            // 搜索无结果或公共提示词无内容时，显示统一的简洁样式
            promptListContainer.innerHTML = `
                <div class="flex flex-col items-center justify-center py-12 px-4">
                    <div class="w-16 h-16 mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-500 text-center text-sm">暂无符合条件的提示词</p>
                </div>
            `;
        }
    };

    const appendPrompts = (promptsToAppend) => {
        const html = promptsToAppend.map(prompt => {
            // 截断内容显示，并处理换行符
            let truncatedContent = prompt.content || '';
            if (truncatedContent.length > 100) {
                truncatedContent = truncatedContent.substring(0, 100) + '...';
            }
            // 将换行符替换为空格，避免代码内容占用过多行
            truncatedContent = truncatedContent.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();

            // 显示统计信息 - 兼容不同的字段名
            const stats = [];
            // 尝试不同的字段名来获取统计数据
            const downloads = prompt.downloads || prompt.copyCount || prompt.copy_count || 0;
            const likes = prompt.likes || prompt.likeCount || prompt.like_count || 0;
            const views = prompt.views || prompt.viewCount || prompt.view_count || 0;

            if (downloads > 0) stats.push(`复制 ${downloads}`);
            if (likes > 0) stats.push(`点赞 ${likes}`);
            if (views > 0) stats.push(`浏览 ${views}`);

            // 如果是我的提示词且没有统计数据，显示默认值
            if (currentTab === 'my-prompts' && stats.length === 0) {
                stats.push(`复制 0`, `点赞 0`, `浏览 0`);
            }

            const statsText = stats.length > 0 ? `<span class="text-xs text-gray-500">${stats.join(' • ')}</span>` : '';

            const myPromptsButtons = `
                <a href="${PLUGIN_CONFIG.getFrontendUrl(`/create?edit=${prompt.id}`)}" target="_blank" data-action="edit-link" class="text-blue-600 hover:text-blue-800 text-sm">编辑</a>
                <button data-action="copy" class="text-green-600 hover:text-green-800 text-sm">复制</button>
                <button data-action="use" class="text-purple-600 hover:text-purple-800 text-sm">使用</button>
            `;
            const publicPromptsButtons = `
                <button data-action="copy" class="text-green-600 hover:text-green-800 text-sm">复制</button>
                <button data-action="use" class="text-purple-600 hover:text-purple-800 text-sm">使用</button>
            `;

            return `<div class="card" data-id="${prompt.id}" data-full-content="${(prompt.content || '').replace(/"/g, '&quot;')}" data-category="${prompt.category || ''}">
                <div class="card-body" data-action="use">
                    <h3 class="font-semibold text-gray-900 mb-2">${prompt.title || '无标题'}</h3>
                    <p class="text-gray-600 text-sm leading-relaxed">${truncatedContent}</p>
                </div>
                <div class="flex items-center justify-between mt-3">
                    <div class="flex items-center">
                        ${statsText}
                    </div>
                    <div class="flex items-center space-x-3">
                        ${currentTab === 'my-prompts' ? myPromptsButtons : publicPromptsButtons}
                    </div>
                </div>
            </div>`;
        }).join('');

        // 将新内容插入到scroll-trigger之前
        const scrollTrigger = document.getElementById('scroll-trigger');
        if (scrollTrigger) {
            scrollTrigger.insertAdjacentHTML('beforebegin', html);
        } else {
            promptListContainer.insertAdjacentHTML('beforeend', html);
        }
    };
    
    const fetchPrompts = async (page) => {
        isLoading = true;
        loadingIndicator.classList.remove('hidden');

        try {
            let endpoint;
            let params = new URLSearchParams({
                page: page.toString(),
                limit: PAGE_SIZE.toString(),
                sortBy: 'downloads' // 按复制次数排序
            });

            if (searchQuery) {
                params.append('search', searchQuery);
            }

            if (currentTab === 'my-prompts') {
                // 我的提示词也需要分页参数
                const myParams = new URLSearchParams({
                    page: page.toString(),
                    limit: PAGE_SIZE.toString(),
                    sortBy: 'latest'
                });
                if (searchQuery) {
                    myParams.append('search', searchQuery);
                }
                endpoint = `/prompts/my?${myParams}`;
            } else {
                endpoint = `/prompts/public?${params}`;
            }

            console.log('🔍 获取提示词:', { endpoint, currentTab, isLoggedIn });
            const response = await makeApiRequest(endpoint);
            console.log('📡 API响应:', response);

            if (response.success) {
                // 处理不同的API响应格式
                let prompts = [];
                if (Array.isArray(response.data)) {
                    prompts = response.data;
                } else if (response.data && Array.isArray(response.data.prompts)) {
                    prompts = response.data.prompts;
                } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
                    prompts = response.data.data;
                }

                console.log('✅ 解析后的提示词:', prompts);

                const currentHasMoreKey = currentTab === 'my-prompts' ? 'my' : 'public';

                // 检查是否还有更多数据（对公共提示词和我的提示词都适用）
                // 优先使用后端返回的分页信息
                if (response.data.pagination && response.data.pagination.hasNext !== undefined) {
                    hasMore[currentHasMoreKey] = response.data.pagination.hasNext;
                    console.log('📄 使用后端分页信息:', response.data.pagination);
                } else if (prompts.length < PAGE_SIZE) {
                    hasMore[currentHasMoreKey] = false;
                    console.log('📭 没有更多数据了，当前页返回', prompts.length, '条，期望', PAGE_SIZE, '条');
                } else {
                    console.log('📄 还有更多数据，当前页返回', prompts.length, '条');
                }

                return prompts;
            } else {
                throw new Error(response.message || '获取提示词失败');
            }
        } catch (error) {
            console.error('获取提示词失败:', error);
            showCopyFeedback(`获取提示词失败: ${error.message}`, true);
            return [];
        } finally {
            isLoading = false;
            loadingIndicator.classList.add('hidden');
        }
    };

    const loadInitialPrompts = async () => {
        promptListContainer.innerHTML = '<div id="scroll-trigger" class="h-4"></div>';
        const pageKey = currentTab === 'my-prompts' ? 'my' : 'public';
        pagesLoaded[pageKey] = 1; hasMore[pageKey] = true;
        if (currentTab === 'my-prompts' && !isLoggedIn) {
            promptListContainer.innerHTML = `<div class="text-center text-gray-500 py-16 px-4"><svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1"><path stroke-linecap="round" stroke-linejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" /></svg><h3 class="mt-2 text-lg font-medium text-gray-900">请先登录</h3><p class="mt-1 text-sm text-gray-500">登录后即可查看和管理你的个人提示词。</p><div class="mt-6"><button type="button" id="login-in-content-btn" class="btn-primary text-sm font-semibold px-4 py-2 rounded-md">前往登录</button></div></div>`;
            document.getElementById('login-in-content-btn').addEventListener('click', handleLogin);
            return;
        }
        const prompts = await fetchPrompts(1);
        if (prompts.length === 0) {
            showEmptyState();
        } else {
            appendPrompts(prompts);
            
            // 设置无限滚动观察器，确保只在有提示词的情况下设置
            setTimeout(() => {
                setupScrollObserver();
            }, 100);
        }
    };
    
    const switchTab = (tabName) => {
        currentTab = tabName;
        tabs.forEach(tab => { tab.classList.toggle('active-tab', tab.dataset.tab === tabName); tab.classList.toggle('border-transparent', tab.dataset.tab !== tabName); tab.classList.toggle('text-gray-500', tab.dataset.tab !== tabName); });

        if (tabName === 'my-prompts' && !isLoggedIn) {
            showLoginPrompt();
            return;
        }

        loadInitialPrompts();
        saveState();
    };

    const showCopyFeedback = (message = "已复制到剪贴板!", isError = false) => {
        copyFeedback.textContent = message;
        copyFeedback.className = `fixed top-5 left-1/2 -translate-x-1/2 text-white px-4 py-2 rounded-md shadow-lg text-sm ${isError ? 'bg-red-600' : 'bg-gray-900'}`;
        copyFeedback.style.zIndex = '9999999'; // 确保显示在最顶层
        copyFeedback.classList.remove('hidden');
        setTimeout(() => copyFeedback.classList.add('hidden'), 2500);
    };

    // 显示手动复制选项
    const showManualCopyOption = (content) => {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10001;
        `;

        const dialog = document.createElement('div');
        dialog.style.cssText = `
            background: white;
            border-radius: 8px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
            max-height: 400px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        `;

        dialog.innerHTML = `
            <h3 style="margin: 0 0 15px 0; color: #333;">自动复制失败，请手动复制</h3>
            <textarea readonly style="
                width: 100%;
                height: 200px;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 10px;
                font-family: monospace;
                font-size: 12px;
                resize: vertical;
                box-sizing: border-box;
            ">${content}</textarea>
            <div style="margin-top: 15px; text-align: right;">
                <button id="manual-copy-close" style="
                    background: #6366f1;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                ">关闭</button>
            </div>
        `;

        modal.appendChild(dialog);
        document.body.appendChild(modal);

        // 自动选中文本
        const textarea = dialog.querySelector('textarea');
        textarea.select();

        // 关闭按钮事件
        dialog.querySelector('#manual-copy-close').onclick = () => {
            document.body.removeChild(modal);
        };

        // 点击背景关闭
        modal.onclick = (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        };
    };

    // 模态框操作函数（暂时保留以防需要）
    // 显示全屏使用提示词弹窗
    const showUsePromptModal = (prompt) => {
        // 向内容脚本发送消息，在页面上显示全屏弹窗
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (chrome.runtime.lastError) {
                console.error('❌ 查询标签页失败:', chrome.runtime.lastError.message);
                return;
            }

            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, {
                    type: 'SHOW_USE_PROMPT_MODAL',
                    prompt: prompt,
                    currentTab: currentTab  // 传递当前标签页信息
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.warn('内容脚本未响应，可能页面不支持全屏弹窗:', chrome.runtime.lastError.message);
                        // 不做任何回退操作，保持插件页面状态
                        return;
                    }
                    // 消息发送成功，不需要额外操作
                })
            }
        })
    };
    const handleLogin = () => {
        // 打开主站登录页，添加extension=true参数
        const loginUrl = PLUGIN_CONFIG.getLoginUrl({ extension: 'true', callback: 'extension' });
        chrome.tabs.create({ url: loginUrl });
        // 显示提示信息
        showCopyFeedback('请在新标签页中登录，登录后返回插件', false);
    };

    // 显示登录引导界面（使用现有样式）
    const showLoginPrompt = () => {
        promptListContainer.innerHTML = `<div class="text-center text-gray-500 py-16 px-4"><svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1"><path stroke-linecap="round" stroke-linejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" /></svg><h3 class="mt-2 text-lg font-medium text-gray-900">请先登录</h3><p class="mt-1 text-sm text-gray-500">登录后即可查看和管理你的个人提示词。</p><div class="mt-6"><button type="button" id="login-in-content-btn" class="btn-primary text-sm font-semibold px-4 py-2 rounded-md">前往登录</button></div></div>`;
        document.getElementById('login-in-content-btn').addEventListener('click', handleLogin);
    };

    const handleLogout = () => {
        console.log('🚪 插件执行登出操作');

        // 通过background script删除Cookie并调用后端登出接口
        if (chrome.runtime?.id) {
            chrome.runtime.sendMessage({ type: 'LOGOUT' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.warn('插件登出消息发送失败:', chrome.runtime.lastError.message)
                } else if (response && response.success) {
                    console.log('✅ 插件登出成功，Cookie已删除')
                } else {
                    console.warn('插件登出失败:', response)
                }
            })
        }

        // 立即更新本地状态
        isLoggedIn = false;
        currentUser = null;

        // 更新UI
        switchTab('public-prompts');
        renderHeader(); // renderHeader() 会自动调用 bindHeaderEvents()
        saveState();
        showCopyFeedback('已退出登录', false);
    };

    // 复制提示词并记录复制次数
    const copyPromptAndTrack = async (prompt) => {
        console.log('📋 开始复制提示词:', prompt);

        if (!prompt.content) {
            console.error('❌ 提示词内容为空:', prompt);
            showCopyFeedback('复制失败：内容为空', true);
            return;
        }

        // 抽离记录复制次数的逻辑，确保两种复制方式成功后都会调用
        const recordCopy = async () => {
            try {
                console.log('🔢 开始记录复制次数:', prompt.id, API_BASE_URL);
                const result = await makeApiRequest(`/prompts/${prompt.id}/copy`, {
                    method: 'POST'
                });
                console.log('✅ 复制次数记录成功:', result);
            } catch (error) {
                console.error('❌ 记录复制次数失败:', error);
                // 记录失败不影响复制功能，静默处理
            }
        };

        // 创建一个更安全的复制函数 - 专为浏览器扩展优化
        const safeCopy = async (text) => {
            console.log('🔄 开始尝试复制，文本长度:', text.length);

            // 在浏览器扩展中，直接使用 execCommand 通常更可靠
            console.log('🔄 使用 execCommand 方法...');
            return new Promise((resolve, reject) => {
                try {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;

                    // 设置样式使其不可见但仍可操作
                    textArea.style.cssText = `
                        position: fixed !important;
                        left: 0 !important;
                        top: 0 !important;
                        width: 2em !important;
                        height: 2em !important;
                        padding: 0 !important;
                        border: none !important;
                        outline: none !important;
                        box-shadow: none !important;
                        background: transparent !important;
                        font-size: 12px !important;
                        z-index: 999999 !important;
                    `;

                    document.body.appendChild(textArea);

                    // 聚焦并选择文本
                    textArea.focus();
                    textArea.select();
                    textArea.setSelectionRange(0, text.length);

                    // 立即执行复制命令
                    try {
                        const successful = document.execCommand('copy');
                        document.body.removeChild(textArea);

                        if (successful) {
                            console.log('✅ execCommand 成功');
                            resolve(true);
                        } else {
                            console.warn('❌ execCommand 返回 false');
                            reject(new Error('execCommand 返回 false'));
                        }
                    } catch (execError) {
                        console.error('❌ execCommand 异常:', execError);
                        if (textArea.parentNode) {
                            document.body.removeChild(textArea);
                        }
                        reject(execError);
                    }

                } catch (error) {
                    console.error('❌ 创建 textarea 失败:', error);
                    reject(error);
                }
            });
        };

        try {
            const success = await safeCopy(prompt.content);
            if (success) {
                console.log('✅ 复制到剪贴板成功');
                showCopyFeedback('已复制到剪贴板!');
                await recordCopy(); // 复制成功后记录
            }
        } catch (error) {
            console.error('❌ 所有复制方法都失败:', error);

            // 显示手动复制选项
            showManualCopyOption(prompt.content);
            await recordCopy(); // 即使自动复制失败，也记录用户的复制意图
        }
    };
    
    // --- Event Listeners ---
    let eventListenersAdded = false; // 防止重复绑定事件监听器

    // 方案B：分离"渲染 & 绑定" - 每次renderHeader后重新绑定事件
    const bindHeaderEvents = () => {
        console.log('🔧 重新绑定头部事件监听器');

        // 先移除可能存在的旧事件监听器（通过克隆节点的方式）
        const elementsToRebind = ['#brand-logo', '#login-btn', '#close-plugin-btn', '#user-menu-btn', '#logout-btn', '#settings-menu-item', '#close-settings-btn', '#new-prompt-link'];
        elementsToRebind.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                const newElement = element.cloneNode(true);
                element.parentNode.replaceChild(newElement, element);
            }
        });

        // 品牌Logo点击事件
        const brandLogo = document.querySelector('#brand-logo');
        if (brandLogo) {
            brandLogo.addEventListener('click', () => {
                console.log('🔗 品牌Logo被点击，跳转到主站:', FRONTEND_URL);
                chrome.tabs.create({ url: FRONTEND_URL });
            });

            // 添加悬停效果
            brandLogo.addEventListener('mouseenter', () => {
                brandLogo.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
            });
            brandLogo.addEventListener('mouseleave', () => {
                brandLogo.style.backgroundColor = 'transparent';
            });

        }

        // 登录按钮
        const loginBtn = document.querySelector('#login-btn');
        if (loginBtn) {
            loginBtn.addEventListener('click', handleLogin);
        }

        // 关闭按钮
        const closeBtn = document.querySelector('#close-plugin-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                // 检查扩展上下文是否有效
                if (!chrome.runtime?.id) {
                    console.warn('扩展上下文已失效，直接关闭窗口');
                    if (window.parent !== window) {
                        // 如果是在iframe中，尝试通过postMessage通知父窗口
                        try {
                            window.parent.postMessage({ type: 'CLOSE_SIDEBAR' }, '*');
                        } catch (e) {
                            console.warn('无法通知父窗口关闭侧边栏');
                        }
                    } else {
                        window.close();
                    }
                    return;
                }

                if (window.parent !== window) {
                    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                        if (chrome.runtime.lastError) {
                            console.error('❌ 查询标签页失败:', chrome.runtime.lastError.message);
                            return;
                        }
                        if (tabs[0]) {
                            chrome.tabs.sendMessage(tabs[0].id, { type: 'CLOSE_SIDEBAR' }, (response) => {
                                if (chrome.runtime.lastError) {
                                    // 忽略内容脚本不存在的错误
                                    console.debug('内容脚本未响应:', chrome.runtime.lastError.message);
                                }
                            });
                        }
                    });
                } else {
                    window.close();
                }
            });
            // 关闭按钮已绑定
        }

        // 用户菜单按钮
        const userMenuBtn = document.querySelector('#user-menu-btn');
        console.log('🔍 查找用户菜单按钮:', userMenuBtn);
        if (userMenuBtn) {
            userMenuBtn.addEventListener('click', (e) => {
                console.log('👤 用户菜单按钮被点击！', e);
                e.preventDefault();
                e.stopPropagation();

                const dropdown = document.querySelector('#user-dropdown');
                console.log('🔍 查找下拉菜单:', dropdown);
                if (dropdown) {
                    const isHidden = dropdown.style.display === 'none' || dropdown.style.display === '';
                    console.log('👤 下拉菜单当前状态 - isHidden:', isHidden);

                    if (isHidden) {
                        dropdown.style.display = 'block';
                        dropdown.style.opacity = '1';
                        dropdown.style.transform = 'scale(1)';
                        dropdown.style.zIndex = '9999';
                        console.log('👤 下拉菜单已显示');
                    } else {
                        dropdown.style.opacity = '0';
                        dropdown.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            dropdown.style.display = 'none';
                            console.log('👤 下拉菜单已隐藏');
                        }, 200);
                    }
                } else {
                    console.debug('用户下拉菜单元素未找到');
                }
            });
            console.log('✅ 用户菜单按钮已绑定');
        } else {
            console.debug('用户菜单按钮元素未找到');
        }

        // 退出登录按钮
        const logoutBtn = document.querySelector('#logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', handleLogout);
            console.log('✅ 退出登录按钮已绑定');
        }

        // 新增提示词链接
        const newPromptLink = document.querySelector('#new-prompt-link');
        if (newPromptLink) {
            newPromptLink.addEventListener('click', () => {
                // 新增提示词链接被点击
                // 关闭用户下拉菜单
                const dropdown = document.querySelector('#user-dropdown');
                if (dropdown) {
                    dropdown.style.opacity = '0';
                    dropdown.style.transform = 'scale(0.95)';
                    setTimeout(() => dropdown.style.display = 'none', 200);
                }
                // 链接会自动跳转，不需要阻止默认行为
            });
            console.log('✅ 新增提示词链接已绑定');
        }

        // 设置菜单项
        const settingsMenuItem = document.querySelector('#settings-menu-item');
        if (settingsMenuItem) {
            settingsMenuItem.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const settingsPanel = document.querySelector('#settings-panel');
                if (settingsPanel) {
                    settingsPanel.classList.toggle('hidden');
                }
                // 关闭用户下拉菜单
                const dropdown = document.querySelector('#user-dropdown');
                if (dropdown) {
                    dropdown.style.opacity = '0';
                    dropdown.style.transform = 'scale(0.95)';
                    setTimeout(() => dropdown.style.display = 'none', 200);
                }
            });
            console.log('✅ 设置菜单项已绑定');
        }

        // 关闭设置面板按钮
        const closeSettingsBtn = document.querySelector('#close-settings-btn');
        if (closeSettingsBtn) {
            closeSettingsBtn.addEventListener('click', () => {
                const settingsPanel = document.querySelector('#settings-panel');
                if (settingsPanel) {
                    settingsPanel.classList.add('hidden');
                }
            });
            console.log('✅ 关闭设置按钮已绑定');
        }
    };

    const addHeaderEventListeners = () => {
        if (eventListenersAdded) {
            console.log('⚠️ 事件监听器已绑定，跳过重复绑定');
            return;
        }

        // 初始化时绑定头部事件
        bindHeaderEvents();



        // 头部按钮事件现在由 bindHeaderEvents() 在 renderHeader() 后自动处理



        eventListenersAdded = true; // 标记事件监听器已添加
        console.log('✅ 所有事件监听器已绑定完成');
    };
    
    // 全局点击事件处理
    document.addEventListener('click', (e) => {
        console.log('🖱️ 全局点击事件:', e.target);

        // 处理用户下拉菜单
        const dropdown = document.getElementById('user-dropdown');
        const userMenuBtn = document.getElementById('user-menu-btn');

        // 检查是否点击了用户菜单按钮或其子元素
        const clickedUserMenuBtn = userMenuBtn && userMenuBtn.contains(e.target);
        console.log('👤 点击了用户菜单按钮:', clickedUserMenuBtn);

        // 只有在下拉菜单显示且点击不在用户菜单按钮和下拉菜单内时才关闭
        const isDropdownVisible = dropdown && dropdown.style.display !== 'none' && dropdown.style.display !== '';
        console.log('👤 全局点击检查:', {
            isDropdownVisible,
            clickedUserMenuBtn,
            targetInDropdown: dropdown ? dropdown.contains(e.target) : false,
            target: e.target.tagName + (e.target.id ? '#' + e.target.id : '') + (e.target.className ? '.' + e.target.className : '')
        });

        if (isDropdownVisible && !clickedUserMenuBtn && !dropdown.contains(e.target)) {
            console.log('👤 关闭用户下拉菜单（点击外部）');
            dropdown.style.opacity = '0';
            dropdown.style.transform = 'scale(0.95)';
            setTimeout(() => dropdown.style.display = 'none', 200);
        }

        // 处理设置面板 - 只有点击面板外部且不是设置按钮时才关闭
        const settingsPanel = document.getElementById('settings-panel');
        const settingsMenuItem = document.getElementById('settings-menu-item');
        const userDropdown = document.getElementById('user-dropdown');

        // 如果刚刚打开设置面板，不处理关闭逻辑
        if (settingsPanelJustOpened) {
            return;
        }

        // 如果点击的是设置菜单项或者用户下拉菜单内的元素，不处理设置面板关闭
        if (settingsMenuItem && settingsMenuItem.contains(e.target)) {
            return;
        }
        if (userDropdown && userDropdown.contains(e.target)) {
            return;
        }

        // 只有点击设置面板外部时才关闭
        if (settingsPanel && !settingsPanel.classList.contains('hidden') && !settingsPanel.contains(e.target)) {
            settingsPanel.classList.add('hidden');
        }
    });
    
    tabs.forEach(tab => tab.addEventListener('click', () => switchTab(tab.dataset.tab)));

    // 搜索功能 - 回车搜索
    const handleSearch = () => {
        searchQuery = searchInput.value.trim();
        console.log('🔍 执行搜索:', searchQuery);
        loadInitialPrompts();
    };

    // 清除搜索
    const clearSearch = () => {
        searchInput.value = '';
        searchQuery = '';
        clearSearchBtn.classList.add('hidden');
        console.log('🧹 清除搜索');
        loadInitialPrompts();
    };

    // 搜索输入框事件
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleSearch();
        }
    });

    // 监听输入变化以显示/隐藏清除按钮
    searchInput.addEventListener('input', (e) => {
        const hasValue = e.target.value.trim().length > 0;
        if (hasValue) {
            clearSearchBtn.classList.remove('hidden');
        } else {
            clearSearchBtn.classList.add('hidden');
        }
    });

    // 清除按钮点击事件
    clearSearchBtn.addEventListener('click', clearSearch);
    if (toggleFloatingBtn) {
        toggleFloatingBtn.addEventListener('change', (e) => {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (chrome.runtime.lastError) {
                    console.error('❌ 查询标签页失败:', chrome.runtime.lastError.message);
                    return;
                }
                if (tabs[0]) {
                    chrome.tabs.sendMessage(tabs[0].id, {
                        type: 'TOGGLE_FLOATING_BUTTON',
                        show: e.target.checked
                    }, (response) => {
                        if (chrome.runtime.lastError) {
                            console.error('❌ 发送悬浮窗开关消息失败:', chrome.runtime.lastError.message);
                            return;
                        }
                        // 保存开关状态
                        localStorage.setItem('floatingButtonEnabled', e.target.checked);
                    });
                }
            });
        });

        // 恢复开关状态
        const savedState = localStorage.getItem('floatingButtonEnabled');
        if (savedState !== null) {
            toggleFloatingBtn.checked = savedState === 'true';
        }
    }

    promptListContainer.addEventListener('click', async (e) => {
        const target = e.target;
        console.log('🖱️ 点击目标:', target.tagName, target.className);

        // 处理空状态按钮点击
        if (target.id === 'browse-public-prompts-btn') {
            switchTab('public-prompts');
            return;
        }

        const actionTarget = target.closest('[data-action]');
        if (!actionTarget) {
            return;
        }

        const card = target.closest('.card');
        if (!card) {
            return;
        }

        const promptId = card.dataset.id;
        const action = actionTarget.dataset.action;
        console.log('🔍 提示词ID:', promptId, '操作:', action);

        if (!promptId) {
            console.log('❌ 提示词ID为空');
            return;
        }

        // 从卡片中获取提示词数据
        const titleElement = card.querySelector('h3');
        const fullContent = card.getAttribute('data-full-content') || '';
        const category = card.getAttribute('data-category') || '';

        // 解码HTML实体
        const decodedContent = fullContent.replace(/&quot;/g, '"').replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>');

        const prompt = {
            id: promptId,
            title: titleElement ? titleElement.textContent : '',
            content: decodedContent,
            category: category
        };

        console.log('🎯 点击的提示词数据:', prompt);

        if (action === 'use') {
            console.log('🎯 执行使用操作');
            showUsePromptModal(prompt);
        } else if (action === 'copy') {
            console.log('🎯 执行复制操作');
            await copyPromptAndTrack(prompt);
        } else {
            console.log('❓ 未知操作:', action);
        }
    });
    
    useModalCancelBtn.addEventListener('click', () => closeModal(usePromptModal));
    useModalCopyBtn.addEventListener('click', async () => {
        const combinedPrompt = `${systemPromptInput.value}\n\n---\n\n${userPromptInput.value}`;

        // 复制到剪贴板 - 使用相同的安全复制函数
        const safeCopyModal = async (text) => {
            console.log('🔄 弹窗复制，文本长度:', text.length);

            // 在浏览器扩展中，直接使用 execCommand
            console.log('🔄 弹窗使用 execCommand 方法...');
            return new Promise((resolve, reject) => {
                try {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;

                    textArea.style.cssText = `
                        position: fixed !important;
                        left: 0 !important;
                        top: 0 !important;
                        width: 2em !important;
                        height: 2em !important;
                        padding: 0 !important;
                        border: none !important;
                        outline: none !important;
                        box-shadow: none !important;
                        background: transparent !important;
                        font-size: 12px !important;
                        z-index: 999999 !important;
                    `;

                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    textArea.setSelectionRange(0, text.length);

                    try {
                        const successful = document.execCommand('copy');
                        document.body.removeChild(textArea);

                        if (successful) {
                            console.log('✅ 弹窗 execCommand 成功');
                            resolve(true);
                        } else {
                            console.warn('❌ 弹窗 execCommand 返回 false');
                            reject(new Error('execCommand 返回 false'));
                        }
                    } catch (execError) {
                        console.error('❌ 弹窗 execCommand 异常:', execError);
                        if (textArea.parentNode) {
                            document.body.removeChild(textArea);
                        }
                        reject(execError);
                    }

                } catch (error) {
                    console.error('❌ 弹窗创建 textarea 失败:', error);
                    reject(error);
                }
            });
        };

        try {
            const success = await safeCopyModal(combinedPrompt);
            if (success) {
                showCopyFeedback('已复制到剪贴板!');
            }
        } catch (error) {
            console.error('❌ 所有复制方法都失败:', error);
            showManualCopyOption(combinedPrompt);
        }

        // 记录复制次数（从当前弹窗的提示词ID获取）
        const currentPromptId = usePromptModal.getAttribute('data-current-prompt-id');
        if (currentPromptId) {
            try {
                console.log('🔢 使用弹窗记录复制次数:', currentPromptId, API_BASE_URL);
                const result = await makeApiRequest(`/prompts/${currentPromptId}/copy`, {
                    method: 'POST'
                });
                console.log('✅ 使用弹窗复制次数记录成功:', result);
            } catch (error) {
                console.error('❌ 使用弹窗记录复制次数失败:', error);
                // 不影响复制功能，静默失败
            }
        } else {
            console.warn('⚠️ 未找到当前提示词ID，无法记录复制次数');
        }

        closeModal(usePromptModal);
    });
    
    // Infinite scroll observer
    const observer = new IntersectionObserver(async (entries) => {
        console.log('🔍 IntersectionObserver 触发:', {
            isIntersecting: entries[0].isIntersecting,
            isLoading,
            currentTab,
            target: entries[0].target.id
        });

        if (entries[0].isIntersecting && !isLoading) {
            const pageKey = currentTab === 'my-prompts' ? 'my' : 'public';
            console.log('🔄 触发无限滚动:', {
                pageKey,
                hasMore: hasMore[pageKey],
                pagesLoaded: pagesLoaded[pageKey],
                scrollTop: promptListContainer.scrollTop,
                scrollHeight: promptListContainer.scrollHeight,
                clientHeight: promptListContainer.clientHeight
            });

            if(hasMore[pageKey]) {
                pagesLoaded[pageKey]++;
                console.log('📄 加载第', pagesLoaded[pageKey], '页');
                const newPrompts = await fetchPrompts(pagesLoaded[pageKey]);
                if (newPrompts.length > 0) {
                    appendPrompts(newPrompts);
                    console.log('✅ 成功加载', newPrompts.length, '条新提示词');
                } else {
                    console.log('📭 没有更多提示词了');
                }
            } else {
                console.log('❌ 没有更多数据可加载');
            }
        }
    }, { root: promptListContainer, rootMargin: "50px", threshold: 0.1 });

    // 重新设置观察器的函数
    const setupScrollObserver = () => {
        // 先断开之前的所有观察
        observer.disconnect();
        
        const currentScrollTrigger = document.getElementById('scroll-trigger');
        if (currentScrollTrigger) {
            observer.observe(currentScrollTrigger);
            console.log('✅ 无限滚动观察器已设置，目标元素:', currentScrollTrigger);
        } else {
            console.debug('scroll-trigger元素未找到');
        }
    };

    // 防止滚动穿透 - 当容器滚动到底部时阻止事件冒泡
    promptListContainer.addEventListener('wheel', (e) => {
        const { scrollTop, scrollHeight, clientHeight } = promptListContainer;
        const isAtTop = scrollTop === 0;
        const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1;

        // 如果在顶部向上滚动，或在底部向下滚动，阻止事件传播
        if ((isAtTop && e.deltaY < 0) || (isAtBottom && e.deltaY > 0)) {
            e.preventDefault();
            e.stopPropagation();
        }
    }, { passive: false });
    
    // 监听来自主站的登录成功消息
    const listenForAuthUpdates = () => {
        // 监听Chrome runtime消息（主站登录成功时发送）
        if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
            chrome.runtime.onMessage.addListener(async (message, _sender, sendResponse) => {
                // 插件收到消息

                if (message.type === 'LOGIN_SUCCESS') {
                    console.log('✅ 收到登录成功消息，更新认证状态');

                    // 如果消息包含token，保存到localStorage
                    if (message.token) {
                        localStorage.setItem('authToken', message.token);
                        localStorage.setItem('token', message.token);
                    }

                    // 如果消息包含用户信息，保存到localStorage
                    if (message.user) {
                        localStorage.setItem('userData', JSON.stringify(message.user));
                        localStorage.setItem('user', JSON.stringify(message.user));
                    }

                    // 重新检查认证状态并更新界面
                    await checkAuthStatus();
                    renderHeader();

                    // 如果当前在我的提示词页面，重新加载
                    if (currentTab === 'my-prompts' && !isInitializing) {
                        loadInitialPrompts();
                    }

                    showCopyFeedback('登录成功！', false);
                    sendResponse({ success: true });
                } else if (message.type === 'AUTH_STATE_CHANGED') {
                    console.log('🔄 收到认证状态变化消息');
                    await checkAuthStatus();
                    renderHeader();
                    if (currentTab === 'my-prompts' && !isInitializing) {
                        loadInitialPrompts();
                    }
                }
            });
        }

        // 监听postMessage（主站登录成功时发送）
        window.addEventListener('message', async (event) => {
            console.log('📨 收到postMessage:', event.origin, event.data);

            // 处理来自content script的消息（没有origin限制）或来自PromptHub主站的消息
            const message = event.data;
            if (message && message.type === 'SWITCH_TO_MY_PROMPTS') {
                console.log('📨 收到切换到我的提示词标签的消息');
                // 切换到我的提示词标签
                switchTab('my-prompts');
                return;
            }

            // 只处理来自PromptHub主站的登录消息
            if (!event.origin.includes('prompthub.xin') &&
                !event.origin.includes('localhost:3000')) {
                console.log('⚠️ 忽略非PromptHub来源的消息:', event.origin);
                return;
            }

            const loginMessage = event.data;
            if (loginMessage && loginMessage.type === 'LOGIN_SUCCESS') {
                console.log('📨 收到postMessage登录成功消息:', loginMessage);

                // 保存认证信息
                if (loginMessage.token) {
                    localStorage.setItem('authToken', loginMessage.token);
                    localStorage.setItem('token', loginMessage.token);
                }
                if (loginMessage.user) {
                    localStorage.setItem('userData', JSON.stringify(loginMessage.user));
                    localStorage.setItem('user', JSON.stringify(loginMessage.user));
                }

                // 更新认证状态
                await checkAuthStatus();
                renderHeader();
                if (currentTab === 'my-prompts' && !isInitializing) {
                    loadInitialPrompts();
                }
                showCopyFeedback('登录成功！', false);
            }
        });

        // 防抖变量
        let authCheckTimeout = null;
        let isCheckingAuth = false;

        // 监听storage变化（当主站登录成功时会更新localStorage）
        window.addEventListener('storage', async (e) => {
            if (e.key === 'authToken' || e.key === 'token') {
                console.log('📦 检测到localStorage变化，使用防抖检查登录状态');

                // 防止重复检查
                if (isCheckingAuth) {
                    console.log('⚠️ 认证检查正在进行中，跳过重复检查');
                    return;
                }

                // 清除之前的定时器
                if (authCheckTimeout) {
                    clearTimeout(authCheckTimeout);
                }

                // 设置防抖定时器
                authCheckTimeout = setTimeout(async () => {
                    try {
                        isCheckingAuth = true;
                        console.log('🔍 开始防抖认证检查');
                        await checkAuthStatus();
                        renderHeader();
                        // 如果当前在我的提示词页面，重新加载
                        if (currentTab === 'my-prompts' && !isInitializing) {
                            loadInitialPrompts();
                        }
                    } catch (error) {
                        console.error('❌ 防抖认证检查失败:', error);
                    } finally {
                        isCheckingAuth = false;
                    }
                }, 1000); // 1秒防抖
            } else if (e.key === 'prompthub-login-event' && e.newValue) {
                // 监听专门的登录事件
                try {
                    const loginEvent = JSON.parse(e.newValue);
                    if (loginEvent.type === 'LOGIN_SUCCESS') {
                        console.log('📦 收到storage登录事件:', loginEvent);

                        // 保存认证信息
                        if (loginEvent.token) {
                            localStorage.setItem('authToken', loginEvent.token);
                            localStorage.setItem('token', loginEvent.token);
                        }
                        if (loginEvent.user) {
                            localStorage.setItem('userData', JSON.stringify(loginEvent.user));
                            localStorage.setItem('user', JSON.stringify(loginEvent.user));
                        }

                        // 更新认证状态
                        await checkAuthStatus();
                        renderHeader();
                        if (currentTab === 'my-prompts' && !isInitializing) {
                            loadInitialPrompts();
                        }
                        showCopyFeedback('登录成功！', false);
                    }
                } catch (error) {
                    console.error('解析登录事件失败:', error);
                }
            }
        });

        // 不再需要定期检查，因为我们使用Cookie监听机制
        console.log('📡 使用Cookie监听机制，无需定期检查认证状态');
    };

    // --- Initial Render ---
    const initializeApp = async () => {
        console.log('🚀 Popup初始化开始...');
        isInitializing = true; // 设置初始化状态

        try {
            // 测试与background script的连接
            try {
                const pingResponse = await new Promise((resolve, reject) => {
                    if (!chrome.runtime?.id) {
                        reject(new Error('扩展上下文已失效'))
                        return
                    }

                    chrome.runtime.sendMessage({ type: 'PING' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message))
                        } else {
                            resolve(response)
                        }
                    })
                })
                console.log('🏓 Background连接测试:', pingResponse)
            } catch (error) {
                console.warn('Background连接测试失败:', error.message)
            }

            // 检查认证状态
            await checkAuthStatus();
            console.log('🔍 认证状态检查完成:', { isLoggedIn, currentUser });

            // 渲染界面
            renderHeader();

            // 恢复之前的标签页状态，如果没有保存状态则默认为public-prompts
            switchTab(currentTab);

            // 添加事件监听器
            addHeaderEventListeners();

            // 开始监听认证状态变化
            listenForAuthUpdates();
            console.log('✅ Popup初始化完成');
        } catch (error) {
            console.error('初始化应用失败:', error);
            // 即使认证检查失败，也要渲染基本界面
            renderHeader();
            addHeaderEventListeners();
            listenForAuthUpdates();
            
            // 只有在没有成功执行过 switchTab 的情况下才执行
            if (!document.querySelector('.card')) {
                switchTab('public-prompts');
            }
        } finally {
            isInitializing = false; // 清除初始化状态
        }
    };

    // 启动应用
    initializeApp();
});

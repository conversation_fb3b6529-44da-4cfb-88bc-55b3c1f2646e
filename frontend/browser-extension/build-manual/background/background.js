// PromptHub 浏览器插件 - 后台脚本

// 内联配置 - 避免 importScripts 路径问题
const isProduction = (() => {
  try {
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
      const manifest = chrome.runtime.getManifest();
      return manifest.version_name?.includes('prod') || false;
    }
    return false;
  } catch (error) {
    return false;
  }
})();

const PROMPTHUB_CONFIG = {
  IS_PRODUCTION: isProduction,

  // API配置
  API: {
    BASE_URL: isProduction ? 'https://prompthub.xin/api' : 'http://localhost:4000/api',
    TIMEOUT: 10000
  },

  // 前端配置
  FRONTEND: {
    BASE_URL: isProduction ? 'https://prompthub.xin' : 'http://localhost:3000'
  },

  // Cookie配置
  COOKIE: {
    NAME: 'prompthub_token',
    DOMAIN: isProduction ? '.prompthub.xin' : 'localhost',
    SECURE: isProduction,
    SAME_SITE: 'lax'
  }
};

// 配置加载成功

// 导入认证管理器
let authManager = null

try {
  importScripts('auth-manager.js')

  // 初始化认证管理器
  if (typeof AuthManager !== 'undefined') {
    authManager = new AuthManager()
  } else {
    console.error('❌ AuthManager类未找到')
  }
} catch (error) {
  console.error('❌ 认证管理器加载失败:', error)
}

// 安全发送消息到内容脚本的辅助函数
function sendMessageToTab(tabId, message, callback) {
  try {
    chrome.tabs.sendMessage(tabId, message, (response) => {
      if (chrome.runtime.lastError) {
        // 内容脚本可能未加载，这是正常情况，不需要报错
        if (callback) callback(null, chrome.runtime.lastError)
      } else {
        if (callback) callback(response, null)
      }
    })
  } catch (error) {
    console.warn('发送消息失败:', error.message)
    if (callback) callback(null, error)
  }
}

// 监听标签页切换，动态设置popup行为
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  try {
    const tab = await chrome.tabs.get(activeInfo.tabId)
    await updateActionForTab(tab)
  } catch (error) {
    console.warn('⚠️ 更新标签页action失败:', error)
  }
})

// 监听标签页更新
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    await updateActionForTab(tab)
  }
})

// 根据标签页类型更新action行为
async function updateActionForTab(tab) {
  if (!tab || !tab.id) return

  // 判断是否为特殊页面
  const isSpecialPage = tab.url.startsWith('chrome://') ||
                       tab.url.startsWith('chrome-extension://') ||
                       tab.url.startsWith('edge://') ||
                       tab.url.startsWith('about:') ||
                       tab.url.startsWith('moz-extension://') ||
                       tab.url === 'chrome://newtab/' ||
                       !tab.url.startsWith('http')

  try {
    if (isSpecialPage) {
      // 特殊页面：启用popup，宽度与侧边栏保持一致(384px)
      await chrome.action.setPopup({
        tabId: tab.id,
        popup: 'popup/popup.html'
      })
      console.log('🔧 特殊页面，已启用popup')
    } else {
      // 普通页面：禁用popup，使用侧边栏
      await chrome.action.setPopup({
        tabId: tab.id,
        popup: ''
      })
      console.log('🔧 普通页面，已禁用popup')
    }
  } catch (error) {
    console.warn('⚠️ 设置action失败:', error)
  }
}

// 监听插件图标点击事件（只在普通页面触发，因为特殊页面会显示popup）
chrome.action.onClicked.addListener(async (tab) => {
  if (!tab || !tab.id) {
    console.warn('无效的标签页')
    return
  }

  // 这个事件只在popup被禁用时触发（即普通页面）
  console.log('🔧 普通页面，切换侧边栏')
  sendMessageToTab(tab.id, { type: 'TOGGLE_SIDEBAR' })
})

// 监听快捷键命令
chrome.commands.onCommand.addListener((command) => {
  if (command === 'toggle-sidebar') {
    // 获取当前活跃标签页
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (chrome.runtime.lastError) {
        console.warn('查询标签页失败:', chrome.runtime.lastError.message)
        return
      }
      if (tabs && tabs[0] && tabs[0].id) {
        sendMessageToTab(tabs[0].id, { type: 'TOGGLE_SIDEBAR' })
      }
    })
  }
})

// 创建右键菜单的函数
function createContextMenu() {
  console.log('🔧 开始创建右键菜单')

  // 先清除可能存在的菜单项
  chrome.contextMenus.removeAll(() => {
    if (chrome.runtime.lastError) {
      console.error('❌ 清除菜单项失败:', chrome.runtime.lastError)
      return
    }

    // 创建新建提示词菜单项
    chrome.contextMenus.create({
      id: 'create-prompt-from-selection',
      title: 'PromptHub 快速新增提示词',
      contexts: ['selection', 'page'], // 在选中文本和页面上都显示
      documentUrlPatterns: ['<all_urls>']
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('❌ 创建右键菜单失败:', chrome.runtime.lastError)
      } else {
        console.log('✅ 右键菜单创建成功')
      }
    })
  })
}

// 扩展安装时创建菜单
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('🔧 扩展安装完成，原因:', details.reason)
  createContextMenu()
  await initializeCurrentTab()
})

// 扩展启动时也创建菜单
chrome.runtime.onStartup.addListener(async () => {
  console.log('🚀 扩展启动')
  createContextMenu()
  await initializeCurrentTab()
})

// 初始化当前活跃标签页的action设置
async function initializeCurrentTab() {
  try {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
    if (tabs && tabs[0]) {
      await updateActionForTab(tabs[0])
    }
  } catch (error) {
    console.warn('⚠️ 初始化当前标签页失败:', error)
  }
}

// Service Worker启动时立即创建菜单
console.log('🎯 PromptHub Background Script 启动')
createContextMenu()

// 监听右键菜单点击事件
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
  console.log('🖱️ 右键菜单被点击:', info.menuItemId)
  console.log('📝 选中的文本:', info.selectionText)

  if (info.menuItemId === 'create-prompt-from-selection') {
    // 获取选中的文本
    const selectedText = info.selectionText

    if (selectedText && selectedText.trim()) {
      console.log('✅ 有选中文本，开始创建提示词')
      try {
        await createPromptFromSelection(selectedText.trim(), tab)
      } catch (error) {
        console.error('❌ 创建提示词失败:', error)
        // 向当前标签页发送错误通知
        sendMessageToTab(tab.id, {
          type: 'SHOW_NOTIFICATION',
          message: '❌ 创建提示词失败，请稍后重试',
          notificationType: 'error'
        })
      }
    } else {
      console.log('⚠️ 没有选中文本，提示用户选择')
      // 向当前标签页发送提示通知
      sendMessageToTab(tab.id, {
        type: 'SHOW_NOTIFICATION',
        message: '💡 请先选中要创建提示词的文本',
        notificationType: 'info'
      })
    }
  }
})

// 从选中文本快速创建提示词
async function createPromptFromSelection(selectedText, tab) {
  // 检查认证状态
  if (!authManager) {
    throw new Error('认证管理器未初始化')
  }

  const authState = await authManager.getAuthState()
  if (!authState.isLoggedIn) {
    // 用户未登录，提示登录并打开插件页
    sendMessageToTab(tab.id, {
      type: 'SHOW_NOTIFICATION',
      message: '⚠️ 请先登录后再创建提示词',
      notificationType: 'warning'
    })

    // 打开插件侧边栏页面并切换到我的提示词标签
    try {
      // 向当前标签页发送消息，让content script显示插件侧边栏并切换到我的提示词
      sendMessageToTab(tab.id, {
        type: 'OPEN_SIDEBAR_MY_PROMPTS'
      })
      console.log('✅ 已发送显示插件侧边栏并切换到我的提示词消息')
    } catch (error) {
      console.warn('⚠️ 无法显示插件侧边栏:', error)
    }
    return
  }

  // 自动生成标题和描述
  const title = generateTitleFromText(selectedText)
  const description = generateDescriptionFromText()

  // 构建提示词数据
  const promptData = {
    title: title,
    description: description,
    content: selectedText,
    category: '其他', // 默认分类
    tags: [],
    isPrivate: true, // 私有
    status: 'draft' // 草稿状态
  }

  // 调用创建提示词API
  const response = await fetch(`${PROMPTHUB_CONFIG.API.BASE_URL}/prompts`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...(authState.token && authState.token !== 'cookie-auth' ?
        { 'Authorization': `Bearer ${authState.token}` } : {})
    },
    credentials: 'include',
    body: JSON.stringify(promptData)
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.message || '创建提示词失败')
  }

  const result = await response.json()

  // 创建成功，显示通知
  sendMessageToTab(tab.id, {
    type: 'SHOW_NOTIFICATION',
    message: '✅ 提示词创建成功！',
    notificationType: 'success'
  })

  // 延迟后打开插件侧边栏并切换到我的提示词标签
  setTimeout(() => {
    sendMessageToTab(tab.id, {
      type: 'OPEN_SIDEBAR_MY_PROMPTS'
    })
  }, 1500)
}

// 从文本生成标题（取前10个字符）
function generateTitleFromText(text) {
  const cleanText = text.trim().replace(/\s+/g, ' ')
  // 取前10个字符作为标题
  if (cleanText.length <= 10) {
    return cleanText
  }
  return cleanText.substring(0, 10)
}

// 生成描述（使用创建时间）
function generateDescriptionFromText() {
  const now = new Date()
  const timeString = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
  return `创建于 ${timeString}`
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // 检查扩展上下文是否有效
  if (!chrome.runtime?.id) {
    console.warn('扩展上下文已失效')
    return false
  }

  try {
    if (request.type === 'GET_AUTH_STATUS') {
      if (!authManager) {
        sendResponse({
          success: false,
          error: '认证管理器未初始化'
        })
        return false
      }

      // 获取认证状态
      authManager.getAuthState()
        .then(authState => {
          // 检查响应是否仍然有效
          if (chrome.runtime?.id) {
            sendResponse({
              success: true,
              isLoggedIn: authState.isLoggedIn,
              user: authState.user,
              token: authState.token
            })
          }
        })
        .catch(error => {
          if (chrome.runtime?.id) {
            sendResponse({
              success: false,
              error: error.message
            })
          }
        })

      return true // 异步响应
    }

    if (request.type === 'LOGOUT') {
      if (!authManager) {
        sendResponse({
          success: false,
          error: '认证管理器未初始化'
        })
        return false
      }

      // 执行登出操作
      authManager.logout()
        .then(() => {
          if (chrome.runtime?.id) {
            sendResponse({ success: true })
          }
        })
        .catch(error => {
          if (chrome.runtime?.id) {
            sendResponse({
              success: false,
              error: error.message
            })
          }
        })

      return true // 异步响应
    }

    // 处理API请求（解决跨域问题）
    if (request.type === 'API_REQUEST') {
      handleApiRequest(request, sendResponse)
      return true // 异步响应
    }

    // 未知消息类型
    sendResponse({
      success: false,
      error: '未知消息类型'
    })
    return false

  } catch (error) {
    console.warn('消息处理错误:', error.message)
    try {
      sendResponse({
        success: false,
        error: '消息处理失败'
      })
    } catch (responseError) {
      // 响应发送失败，忽略
    }
    return false
  }
})

// 处理API请求（在background script中执行，避免跨域限制）
async function handleApiRequest(request, sendResponse) {
  try {
    const { endpoint, options = {} } = request
    const url = `${PROMPTHUB_CONFIG.API.BASE_URL}${endpoint}`

    const defaultHeaders = {
      'Content-Type': 'application/json'
    }

    // 获取认证状态
    const authState = await authManager.getAuthState()

    // 只有当token存在且不是'cookie-auth'标识时才添加Authorization头
    if (authState.token && authState.token !== 'cookie-auth') {
      defaultHeaders.Authorization = `Bearer ${authState.token}`
    }

    const config = {
      method: 'GET',
      headers: { ...defaultHeaders, ...options.headers },
      credentials: 'include', // 支持Cookie认证
      ...options
    }

    console.log('🌐 Background发起API请求:', url, config)

    const response = await fetch(url, config)
    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.message || `HTTP ${response.status}`)
    }

    console.log('✅ Background API请求成功:', data)
    sendResponse({
      success: true,
      data: data
    })

  } catch (error) {
    console.error('❌ Background API请求失败:', error)
    sendResponse({
      success: false,
      error: error.message
    })
  }
}

// PromptHub 后台脚本初始化完成


/* PromptHub 浏览器插件 - 内容脚本样式 */



/* 侧边栏样式 */
.prompthub-sidebar {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  width: 384px !important;
  height: 100vh !important;
  background: white !important;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
  z-index: 9999 !important;
  transition: transform 0.3s ease-in-out !important;
  will-change: transform !important;
  border-radius: 8px 0 0 8px !important;
}

.prompthub-sidebar.sidebar-hidden {
  transform: translateX(100%) !important;
  pointer-events: none !important;
}

.prompthub-sidebar .sidebar-content {
  width: 100% !important;
  height: 100% !important;
  overflow: visible !important;
}

.prompthub-sidebar iframe {
  border: none !important;
  width: 100% !important;
  height: 100% !important;
}

/* 通知样式 */
#prompthub-notification {
  position: fixed !important;
  top: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  padding: 12px 24px !important;
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  border-radius: 12px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  z-index: 10001 !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  animation: prompthub-notification-slide-in 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 高亮选中的输入框 */
.prompthub-highlight-input {
  outline: 2px solid #4F46E5 !important;
  outline-offset: 2px !important;
  border-radius: 4px !important;
  animation: prompthub-pulse 1s ease-in-out !important;
}

/* 动画 */
@keyframes prompthub-notification-slide-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}

@keyframes prompthub-notification-slide-out {
  from {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px) scale(0.9);
  }
}

@keyframes prompthub-pulse {
  0% { outline-color: #4F46E5; }
  50% { outline-color: #818CF8; }
  100% { outline-color: #4F46E5; }
}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PromptHub 助手</title>
  <!-- 使用本地CSS文件 -->
  <link rel="stylesheet" href="popup.css">
</head>
<body class="w-full bg-gray-50" id="popup-body" style="width: 384px !important; height: 100vh; min-height: 100vh; overflow: hidden; box-sizing: border-box;">
  <!-- 插件侧边栏主体 (h-full 保证 flexbox 正确计算高度) -->
  <aside id="prompt-sidebar" class="h-full w-full bg-white shadow-lg flex flex-col">
    <header id="sidebar-header" class="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0"></header>
    
    <!-- 设置面板 -->
    <div id="settings-panel" class="hidden p-4 border-b border-gray-200 bg-gray-50">
      <div class="mb-3">
        <h3 class="text-md font-semibold text-gray-700">设置</h3>
      </div>
      <div class="flex items-center justify-between py-1">
        <label for="toggle-floating-btn" class="text-sm text-gray-600 leading-6">显示悬浮按钮</label>
        <label class="relative inline-flex items-center cursor-pointer">
          <input type="checkbox" id="toggle-floating-btn" class="sr-only peer" checked>
          <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
        </label>
      </div>

    </div>

    <!-- 搜索区域 -->
    <div class="p-4 border-b border-gray-200">
      <div class="relative">
        <input id="search-input" type="text" placeholder="搜索标题、描述、内容... (按回车搜索)" class="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <button id="clear-search-btn" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors hidden">
          <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

        <nav class="flex border-b border-gray-200 px-4">
            <button data-tab="my-prompts" class="tab-btn flex-1 py-3 text-sm font-medium text-center border-b-2 border-transparent text-gray-500 hover:text-gray-700">我的提示词</button>
            <button data-tab="public-prompts" class="tab-btn flex-1 py-3 text-sm font-medium text-center border-b-2 active-tab">公共提示词</button>
        </nav>

    <!-- 主内容区域 -->
    <main id="prompt-list-container" class="flex-1 overflow-y-auto p-4 space-y-3 bg-gray-50">
      <!-- 提示词列表将在这里动态生成 -->
      <!-- 无限滚动触发器 -->
      <div id="scroll-trigger" class="h-4"></div>
    </main>

    <!-- 加载指示器 -->
    <div id="loading-indicator" class="hidden h-16 flex items-center justify-center">
      <div class="loader"></div>
    </div>
  </aside>

  <!-- 使用提示词模态框 -->
  <div id="use-prompt-modal" class="hidden fixed inset-0 z-50 flex items-center justify-center modal-backdrop">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 flex flex-col" style="height: 75vh; max-height: 800px;">
      <div class="p-5 border-b">
        <h2 class="text-xl font-bold text-gray-900">使用提示词</h2>
      </div>
      <div class="flex-1 p-5 space-y-4 overflow-y-hidden flex flex-col">
        <div class="flex-shrink-0">
          <label class="block text-sm font-semibold text-gray-700 mb-2">系统提示词 (可编辑)</label>
          <textarea id="system-prompt-input" rows="5" class="w-full p-3 bg-gray-100 rounded-md text-gray-700 text-sm border-gray-200 border focus:outline-none focus:ring-2 focus:ring-purple-400"></textarea>
        </div>
        <div class="flex-grow flex flex-col">
          <label for="user-prompt-input" class="block text-sm font-semibold text-gray-700 mb-2 flex-shrink-0">你的需求</label>
          <textarea id="user-prompt-input" class="w-full h-full flex-grow p-3 bg-white rounded-md text-gray-800 border-gray-300 border focus:outline-none focus:ring-2 focus:ring-purple-400" placeholder="请在这里输入你的具体要求..."></textarea>
        </div>
      </div>
      <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3 border-t rounded-b-lg">
        <button type="button" id="use-modal-cancel-btn" class="py-2 px-4 rounded-md text-sm font-medium btn-secondary">取消</button>
        <button type="button" id="use-modal-copy-btn" class="py-2 px-4 rounded-md text-sm font-medium btn-primary">复制组合内容</button>
      </div>
    </div>
  </div>

  <!-- 复制反馈提示 -->
  <div id="copy-feedback" class="hidden fixed top-5 left-1/2 -translate-x-1/2 bg-gray-900 text-white px-4 py-2 rounded-md shadow-lg text-sm" style="z-index: 99999999;"></div>

  <script src="../config.js"></script>
  <script src="popup.js"></script>
</body>
</html>

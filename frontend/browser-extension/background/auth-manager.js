/**
 * 插件认证管理器 - 基于Cookie共享登录状态
 * 实现网站与插件的无缝登录状态同步
 */

class AuthManager {
  constructor() {
    // 使用统一配置
    this.CONFIG = typeof PROMPTHUB_CONFIG !== 'undefined' ? PROMPTHUB_CONFIG : (typeof CONFIG !== 'undefined' ? CONFIG : null)
    if (!this.CONFIG) {
      console.error('❌ 配置未找到，AuthManager初始化失败')
      throw new Error('配置未找到，AuthManager初始化失败')
    }
    this.API_BASE_URL = this.CONFIG.API.BASE_URL
    this.COOKIE_NAME = this.CONFIG.COOKIE.NAME
    this.COOKIE_DOMAIN = this.CONFIG.COOKIE.DOMAIN
    this.isInitialized = true

    // AuthManager配置加载成功

    // 监听Cookie变化
    this.initCookieListener()
  }

  /**
   * 获取前端基础URL
   */
  getFrontendBaseUrl() {
    return this.CONFIG.FRONTEND.BASE_URL
  }

  /**
   * 初始化Cookie监听器
   */
  initCookieListener() {
    if (typeof chrome !== 'undefined' && chrome.cookies) {
      // 监听Cookie变化
      chrome.cookies.onChanged.addListener((changeInfo) => {
        if (changeInfo.cookie.name === this.COOKIE_NAME) {
          this.handleAuthStateChange(changeInfo)
        }
      })

      // 初始化时检查现有Cookie
      this.checkExistingCookie()
    }
  }

  /**
   * 检查现有Cookie状态
   */
  async checkExistingCookie() {
    try {
      const cookies = await chrome.cookies.getAll({
        name: this.COOKIE_NAME,
        domain: this.CONFIG.COOKIE.DOMAIN
      })

      if (cookies.length > 0) {
        const user = await this.getCurrentUser()
        if (user) {
          await this.saveLocalAuth(user, cookies[0].value)
        }
      }
    } catch (error) {
      console.error('❌ 检查现有Cookie失败:', error)
    }
  }

  /**
   * 处理认证状态变化
   */
  async handleAuthStateChange(changeInfo) {
    try {
      if (changeInfo.removed) {
        // Cookie被删除 - 用户登出
        await this.clearLocalAuth()
        this.notifyAuthChange({ type: 'LOGOUT' })
      } else {
        // Cookie被设置或更新 - 用户登录
        const user = await this.getCurrentUser()
        if (user) {
          await this.saveLocalAuth(user, changeInfo.cookie.value)
          this.notifyAuthChange({ type: 'LOGIN', user })
        }
      }
    } catch (error) {
      console.error('❌ 处理认证状态变化失败:', error)
    }
  }

  /**
   * 获取当前登录状态
   */
  async getAuthState() {
    try {
      // 1. 首先尝试从Cookie获取
      const cookie = await this.getCookie()
      if (cookie) {
        const user = await this.getCurrentUser()
        if (user) {
          return { isLoggedIn: true, user, token: cookie.value }
        }
      }

      // 2. 如果Cookie无效，检查本地存储
      const localAuth = await this.getLocalAuth()
      if (localAuth && localAuth.token) {
        // 验证本地token是否有效
        const user = await this.getCurrentUserWithToken(localAuth.token)
        if (user) {
          return { isLoggedIn: true, user, token: localAuth.token }
        } else {
          // 本地token无效，清除
          await this.clearLocalAuth()
        }
      }

      return { isLoggedIn: false, user: null, token: null }
    } catch (error) {
      console.error('❌ 获取认证状态失败:', error)
      return { isLoggedIn: false, user: null, token: null }
    }
  }

  /**
   * 获取Cookie
   */
  async getCookie() {
    return new Promise((resolve) => {
      if (typeof chrome === 'undefined' || !chrome.cookies) {
        resolve(null)
        return
      }

      const urls = [
        this.CONFIG.FRONTEND.BASE_URL,  // 前端服务器
        this.CONFIG.API.BASE_URL.replace('/api', ''),  // 后端服务器
      ]

      // 尝试从多个可能的域获取Cookie
      const checkCookie = async (urlIndex = 0) => {
        if (urlIndex >= urls.length) {
          resolve(null)
          return
        }

        chrome.cookies.get({
          url: urls[urlIndex],
          name: this.COOKIE_NAME
        }, (cookie) => {
          if (chrome.runtime.lastError) {
            // Cookie API错误，尝试下一个URL
            checkCookie(urlIndex + 1)
            return
          }
          if (cookie && cookie.value) {
            resolve(cookie)
          } else {
            checkCookie(urlIndex + 1)
          }
        })
      }

      checkCookie()
    })
  }

  /**
   * 通过Cookie获取当前用户信息
   */
  async getCurrentUser() {
    try {
      // 获取Cookie
      const cookie = await this.getCookie()
      if (!cookie) {
        return null
      }

      const response = await fetch(`${this.API_BASE_URL}/auth/me`, {
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `${cookie.name}=${cookie.value}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        return data.data
      }
      return null
    } catch (error) {
      console.error('❌ 获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 通过Token获取当前用户信息
   */
  async getCurrentUserWithToken(token) {
    try {
      const response = await fetch(`${this.API_BASE_URL}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        return data.data
      }
      return null
    } catch (error) {
      console.error('❌ 通过Token获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 保存本地认证信息
   */
  async saveLocalAuth(user, token) {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      await chrome.storage.local.set({
        authUser: user,
        authToken: token,
        authTimestamp: Date.now()
      })
    }
  }

  /**
   * 获取本地认证信息
   */
  async getLocalAuth() {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      const result = await chrome.storage.local.get(['authUser', 'authToken', 'authTimestamp'])
      return result
    }
    return null
  }

  /**
   * 删除认证Cookie
   */
  async removeCookie() {
    try {
      if (typeof chrome !== 'undefined' && chrome.cookies) {
        await chrome.cookies.remove({
          url: this.COOKIE_DOMAIN.startsWith('.') ? `https://${this.COOKIE_DOMAIN.slice(1)}` : `https://${this.COOKIE_DOMAIN}`,
          name: this.COOKIE_NAME
        })
        // 认证Cookie已删除
      }
    } catch (error) {
      console.error('❌ 删除Cookie失败:', error)
    }
  }

  /**
   * 清除本地认证信息
   */
  async clearLocalAuth() {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      // 清除chrome.storage.local中的认证信息
      await chrome.storage.local.remove([
        'authUser', 'authToken', 'authTimestamp', // authManager使用的key
        'userData', 'user', 'token', 'isLoggedIn'  // popup使用的key
      ])
      // chrome.storage.local认证信息已清除
    }
  }

  /**
   * 启动登录流程
   */
  async startLogin() {
    try {
      const loginUrl = this.CONFIG.getLoginUrl({ extension: 'true', callback: 'extension' })
      
      if (typeof chrome !== 'undefined' && chrome.identity && chrome.identity.launchWebAuthFlow) {
        // 使用Chrome Identity API
        const redirectUrl = await chrome.identity.launchWebAuthFlow({
          url: loginUrl,
          interactive: true
        })
        
        // 登录重定向URL已生成
        return true
      } else {
        // 降级方案：打开新标签页
        if (typeof chrome !== 'undefined' && chrome.tabs) {
          await chrome.tabs.create({ url: loginUrl })
          return true
        }
      }
      
      return false
    } catch (error) {
      console.error('❌ 启动登录流程失败:', error)
      return false
    }
  }

  /**
   * 登出
   */
  async logout() {
    try {
      // 1. 删除Cookie（插件有权限删除HttpOnly Cookie）
      await this.removeCookie()

      // 2. 调用后端登出接口（确保服务端也清除session）
      await fetch(`${this.API_BASE_URL}/auth/logout`, {
        method: 'POST',
        credentials: 'include'
      })

      // 3. 清除本地认证信息
      await this.clearLocalAuth()

      // 4. 通知状态变化（网页端会通过BroadcastChannel收到）
      this.notifyAuthChange({ type: 'LOGOUT' })

      // 插件登出完成：Cookie已删除，后端已通知
      return true
    } catch (error) {
      console.error('❌ 插件登出失败:', error)
      return false
    }
  }

  /**
   * 通知认证状态变化
   */
  notifyAuthChange(data) {
    // 向所有监听的页面发送消息
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.sendMessage({
        type: 'AUTH_STATE_CHANGED',
        data
      }, (response) => {
        if (chrome.runtime.lastError) {
          // 忽略没有监听器的错误，这是正常情况
        }
      })
    }
  }
}

// 不再创建全局实例，由background.js负责创建和管理

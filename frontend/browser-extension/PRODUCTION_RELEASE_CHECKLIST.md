# PromptHub 浏览器扩展生产发布检查清单

## 🔧 配置修改

### 1. manifest.json 修改
- [ ] 添加 `"version_name": "x.x.x-prod"` (包含 'prod' 字符串)
- [ ] 移除开发环境的 host_permissions:
  - [ ] 删除 `"http://localhost:3000/*"`
  - [ ] 删除 `"http://localhost:4000/*"`
- [ ] 保留生产环境权限:
  - [ ] `"https://*.prompthub.xin/*"`
  - [ ] `"<all_urls>"`

### 2. 环境检测验证
- [ ] config.js 中的 `isProduction` 逻辑正确
- [ ] background.js 中的 `isProduction` 逻辑正确
- [ ] 两者都基于 `manifest.version_name?.includes('prod')` 判断

## 🧪 功能测试

### 基础功能测试
- [ ] 扩展能正常加载和启动
- [ ] Service Worker 正常工作(可以显示"无效"，这是正常的)
- [ ] 点击扩展图标能打开/关闭侧边栏
- [ ] 快捷键 Ctrl+Shift+P (Mac: Cmd+Shift+P) 正常工作

### 网络连接测试
- [ ] 能正常连接到 https://prompthub.xin/api
- [ ] 登录功能正常
- [ ] 提示词加载正常
- [ ] 复制功能正常

### 跨页面测试
- [ ] 在不同网站上都能正常工作
- [ ] 悬浮按钮正常显示
- [ ] 侧边栏正常注入

## 🔍 代码质量检查

### 语法检查
- [ ] `node -c background/background.js` 通过
- [ ] `node -c popup/popup.js` 通过  
- [ ] `node -c content-script/content.js` 通过
- [ ] `node -c config.js` 通过

### 控制台检查
- [ ] 开发者工具 Console 无红色错误
- [ ] Service Worker DevTools 无启动错误
- [ ] 网络请求正常

## 📦 构建和打包

### 自动构建
- [ ] 运行 `./build-production.sh` 成功
- [ ] 生成的 build-production 目录内容正确
- [ ] 生成的 zip 文件完整

### 手动验证
- [ ] 在 Chrome 中加载 build-production 目录测试
- [ ] 所有功能在构建版本中正常工作

## 🚀 发布准备

### Chrome Web Store 准备
- [ ] 准备扩展描述文本
- [ ] 准备截图和宣传图片
- [ ] 准备隐私政策链接
- [ ] 设置合适的分类和标签

### 最终检查
- [ ] 版本号正确递增
- [ ] 更新日志准备完毕
- [ ] 所有测试通过
- [ ] 代码已提交到版本控制

## 📋 发布后验证

### 发布后测试
- [ ] 从 Chrome Web Store 安装测试
- [ ] 验证所有功能正常
- [ ] 检查用户反馈

## 🔄 回滚计划

如果发现问题需要回滚:
- [ ] 准备上一个稳定版本
- [ ] 了解回滚流程
- [ ] 准备用户通知

---

## 📝 发布命令

```bash
# 1. 构建生产版本
./build-production.sh

# 2. 测试构建版本
# 在 Chrome 中加载 build-production 目录

# 3. 上传到 Chrome Web Store
# 使用生成的 zip 文件上传
```

## 🎯 关键配置总结

**生产环境标识**: `version_name` 包含 'prod'
**API地址**: https://prompthub.xin/api  
**前端地址**: https://prompthub.xin
**Cookie域**: .prompthub.xin

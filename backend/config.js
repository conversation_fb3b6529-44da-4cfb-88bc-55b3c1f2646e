// 🔧 PromptHub 后端配置管理
// 统一管理所有配置项，提供默认值和类型转换

module.exports = {
  // 服务器配置
  SERVER: {
    PORT: parseInt(process.env.PORT, 10) || 9000,
    NODE_ENV: process.env.NODE_ENV || 'development',
    HOST: process.env.HOST || 'localhost'
  },
  
  // JWT认证配置
  JWT: {
    SECRET: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',
    ALGORITHM: process.env.JWT_ALGORITHM || 'HS256'
  },

  // Cookie配置
  COOKIE: {
    DOMAIN: process.env.COOKIE_DOMAIN || (process.env.NODE_ENV === 'production' ? '.prompthub.xin' : 'localhost'),
    SECURE: process.env.COOKIE_SECURE === 'true' || process.env.NODE_ENV === 'production',
    SAME_SITE: process.env.COOKIE_SAME_SITE || 'lax',
    MAX_AGE: parseInt(process.env.COOKIE_MAX_AGE, 10) || 7 * 24 * 60 * 60 * 1000, // 7天
    HTTP_ONLY: process.env.COOKIE_HTTP_ONLY !== 'false' // 默认启用
  },
  
  // MySQL数据库配置
  DATABASE: {
    HOST: process.env.DB_HOST || 'localhost',
    PORT: parseInt(process.env.DB_PORT, 10) || 3306,
    USER: process.env.DB_USER || 'prompthub_user',
    PASSWORD: process.env.DB_PASSWORD || '',
    NAME: process.env.DB_NAME || 'prompthub',
    CHARSET: 'utf8mb4',
    TIMEZONE: '+08:00',
    CONNECTION_LIMIT: parseInt(process.env.DB_CONNECTION_LIMIT, 10) || 10,
    ACQUIRE_TIMEOUT: parseInt(process.env.DB_ACQUIRE_TIMEOUT, 10) || 60000,
    TIMEOUT: parseInt(process.env.DB_TIMEOUT, 10) || 60000
  },
  
  // CORS配置
  CORS: {
    ALLOWED_ORIGINS: function(origin, callback) {
      // 允许的域名列表22222
      const allowedOrigins = process.env.ALLOWED_ORIGINS ?
        process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim()) :
        ['http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:3000', 'http://127.0.0.1:3001'];

      console.log('🔍 CORS检查 - Origin:', origin, 'Allowed:', allowedOrigins);

      // 允许浏览器扩展
      if (!origin ||
          allowedOrigins.includes(origin) ||
          origin.startsWith('chrome-extension://') ||
          origin.startsWith('moz-extension://')) {
        console.log('✅ CORS允许访问');
        callback(null, true);
      } else {
        console.log('❌ CORS拒绝访问');
        callback(new Error('Not allowed by CORS'));
      }
    },
    ALLOWED_METHODS: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    ALLOWED_HEADERS: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Accept',
      'Origin',
      'Access-Control-Request-Method',
      'Access-Control-Request-Headers'
    ],
    CREDENTIALS: true,
    EXPOSE_HEADERS: ['Content-Length', 'X-Total-Count'],
    MAX_AGE: 86400 // 24小时
  },
  
  // 速率限制配置
  RATE_LIMIT: {
    WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 15 * 60 * 1000, // 15分钟
    MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100,
    SKIP_SUCCESSFUL_REQUESTS: process.env.RATE_LIMIT_SKIP_SUCCESS === 'true',
    SKIP_FAILED_REQUESTS: process.env.RATE_LIMIT_SKIP_FAILED === 'true'
  },
  
  // 文件上传配置
  UPLOAD: {
    DIR: process.env.UPLOAD_DIR || 'uploads',
    MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE, 10) || 5 * 1024 * 1024, // 5MB
    ALLOWED_TYPES: process.env.ALLOWED_FILE_TYPES ? 
      process.env.ALLOWED_FILE_TYPES.split(',').map(type => type.trim()) :
      ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  },
  
  // 日志配置
  LOGGING: {
    LEVEL: process.env.LOG_LEVEL || 'info',
    DIR: process.env.LOG_DIR || 'logs',
    MAX_SIZE: process.env.LOG_MAX_SIZE || '20m',
    MAX_FILES: parseInt(process.env.LOG_MAX_FILES, 10) || 14
  },
  
  // Redis配置（如果使用）
  REDIS: {
    HOST: process.env.REDIS_HOST || 'localhost',
    PORT: parseInt(process.env.REDIS_PORT, 10) || 6379,
    PASSWORD: process.env.REDIS_PASSWORD || '',
    DB: parseInt(process.env.REDIS_DB, 10) || 0
  },
  
  // 业务配置
  BUSINESS: {
    PAGINATION: {
      DEFAULT_PAGE_SIZE: parseInt(process.env.DEFAULT_PAGE_SIZE, 10) || 10,
      MAX_PAGE_SIZE: parseInt(process.env.MAX_PAGE_SIZE, 10) || 100
    },
    CACHE: {
      PROMPT_TTL: parseInt(process.env.CACHE_PROMPT_TTL, 10) || 60 * 60, // 1小时
      USER_TTL: parseInt(process.env.CACHE_USER_TTL, 10) || 30 * 60 // 30分钟
    }
  },
  
  // 开发配置
  DEV: {
    ENABLE_MOCK_DATA: process.env.ENABLE_MOCK_DATA === 'true',
    API_DOCS: process.env.ENABLE_API_DOCS !== 'false', // 默认启用
    DEBUG_SQL: process.env.DEBUG_SQL === 'true'
  }
} 
# 🚀 PromptHub 后端服务配置示例
# 复制此文件为 .env 并根据你的环境修改配置

# 服务器配置
PORT=9000
NODE_ENV=development
HOST=localhost

# JWT认证配置 (生产环境请使用更强的密钥)
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d
JWT_ALGORITHM=HS256

# Cookie配置
# 开发环境
COOKIE_DOMAIN=localhost
# 生产环境 (注意前面的点号，允许子域名)
# COOKIE_DOMAIN=.prompthub.xin
COOKIE_SECURE=false  # 生产环境设为true
COOKIE_SAME_SITE=lax
COOKIE_MAX_AGE=604800000  # 7天 (毫秒)
COOKIE_HTTP_ONLY=true

# MySQL数据库配置
# 本地开发环境
DB_HOST=localhost
DB_PORT=3306
DB_USER=prompthub_user
DB_PASSWORD=
DB_NAME=prompthub
DB_CONNECTION_LIMIT=10
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000

# 生产环境 (腾讯云MySQL)
# DB_HOST=sh-cynosdbmysql-grp-knh8ui5y.sql.tencentcdb.com
# DB_PORT=27410
# DB_USER=root
# DB_PASSWORD=Baby901221
# DB_NAME=prompthub_prod

# CORS配置 - 允许的前端域名
# 开发环境
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000,http://127.0.0.1:3001
# 生产环境 (添加你的域名)
# ALLOWED_ORIGINS=https://prompthub.xin,http://localhost:3000,http://localhost:3001

# 速率限制配置
RATE_LIMIT_WINDOW_MS=900000  # 15分钟
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESS=false
RATE_LIMIT_SKIP_FAILED=false

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880  # 5MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# 日志配置
LOG_LEVEL=info
LOG_DIR=logs
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14

# Redis配置 (可选)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 业务配置
DEFAULT_PAGE_SIZE=10
MAX_PAGE_SIZE=100
CACHE_PROMPT_TTL=3600  # 1小时
CACHE_USER_TTL=1800    # 30分钟

# 开发配置
ENABLE_MOCK_DATA=false
ENABLE_API_DOCS=true
DEBUG_SQL=false 
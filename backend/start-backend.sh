#!/bin/bash

# AI提示词平台后端启动脚本
echo "🚀 启动AI提示词平台后端服务..."

# 切换到后端目录
cd "$(dirname "$0")"

# 检查端口是否被占用
if lsof -Pi :3002 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️ 端口 3002 已被占用，正在停止现有进程..."
    pkill -f "node src/app.js"
    sleep 2
fi

# 设置环境变量
export NODE_ENV=development
export PORT=3002

# 启动服务器
echo "📱 启动服务器在端口 3002..."

# 使用while循环实现自动重启
while true; do
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 启动后端服务..."
    node src/app.js
    
    # 如果进程正常退出（exit code 0），则不重启
    if [ $? -eq 0 ]; then
        echo "服务正常退出"
        break
    fi
    
    echo "⚠️ 服务异常退出，5秒后重启..."
    sleep 5
done

echo "🔚 后端服务已停止" 
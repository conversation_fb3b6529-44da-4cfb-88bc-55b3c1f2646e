const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser'); // 添加cookie-parser
const mysql = require('mysql2/promise');
const jwt = require('jsonwebtoken');
require('dotenv').config();

// 引入配置
const config = require('../config');

const app = express();

// 引入路由
const authRoutes = require('./routes/auth');
const categoryRoutes = require('./routes/categories');
const { authenticateToken, optionalAuth } = require('./middleware/auth');

// 中间件 - 使用配置化的CORS设置
app.use(cors({
  origin: config.CORS.ALLOWED_ORIGINS,
  methods: config.CORS.ALLOWED_METHODS,
  allowedHeaders: config.CORS.ALLOWED_HEADERS,
  credentials: config.CORS.CREDENTIALS
}));
app.use(express.json());
app.use(cookieParser()); // 启用Cookie解析



// 使用路由
app.use('/api/auth', authRoutes);
app.use('/api/categories', categoryRoutes);

// 创建数据库连接池
const pool = mysql.createPool({
  host: config.DATABASE.HOST,
  port: config.DATABASE.PORT,
  user: config.DATABASE.USER,
  password: config.DATABASE.PASSWORD,
  database: config.DATABASE.NAME,
  charset: config.DATABASE.CHARSET
});





// 获取公开提示词 (前端使用的端点) - 支持分页、搜索、筛选
app.get('/api/prompts/public', optionalAuth, async (req, res) => {
  try {
    // 获取分页参数
    const page = parseInt(req.query.page || '1');
    const limit = parseInt(req.query.limit || '6');
    const sortBy = req.query.sortBy || 'newest'; // newest, downloads, popular
    const search = req.query.search || '';
    const category = req.query.category || '';
    const tags = req.query.tags || ''; // 标签筛选，多个标签用逗号分隔
    const offset = (page - 1) * limit;

    // 构建WHERE条件
    let whereConditions = ['p.status = ?', 'p.isPrivate = ?'];
    let whereParams = ['published', 0]; // 使用0而不是false，因为数据库存储的是TINYINT

    // 搜索条件
    if (search) {
      whereConditions.push('(p.title LIKE ? OR p.description LIKE ? OR p.content LIKE ? OR p.tags LIKE ?)');
      whereParams.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
    }

    // 分类筛选
    if (category && category !== 'all') {
      whereConditions.push('p.category = ?');
      whereParams.push(category);
    }

    // 标签筛选
    if (tags) {
      const tagList = tags.split(',').map(tag => tag.trim()).filter(tag => tag);
      if (tagList.length > 0) {
        // 使用更灵活的标签匹配，支持部分匹配
        const tagConditions = tagList.map(() => 'p.tags LIKE ?').join(' OR ');
        whereConditions.push(`(${tagConditions})`);
        tagList.forEach(tag => {
          // 匹配包含该关键词的标签，不要求完全匹配
          whereParams.push(`%${tag}%`);
        });
      }
    }

    const whereClause = whereConditions.join(' AND ');

    // 根据排序类型选择ORDER BY子句
    let orderClause = 'p.createdAt DESC'; // 默认最新
    if (sortBy === 'downloads') {
      orderClause = 'p.downloads DESC, p.likes DESC'; // 热门推荐 = 按下载量排序
    } else if (sortBy === 'popular') {
      orderClause = 'p.likes DESC, p.views DESC'; // 最受欢迎 = 按点赞排序
    }

    // 获取总数
    const [countResult] = await pool.query(`
      SELECT COUNT(*) as total
      FROM prompts p
      WHERE ${whereClause}
    `, whereParams);
    const total = countResult[0].total;

    // 获取当前用户ID（如果已登录）
    const userId = req.userId || null;

    // 获取分页数据 - 构建动态SQL查询，包含用户点赞状态
    const query = `
      SELECT
        p.id,
        p.title,
        p.description,
        p.content,
        p.category,
        p.tags,
        p.likes,
        p.downloads,
        p.views,
        p.createdAt,
        p.updatedAt,
        p.status,
        p.isPrivate,
        p.userId,
        u.username as authorName,
        c.description as categoryDisplayName,
        CASE WHEN l.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked
      FROM prompts p
      LEFT JOIN users u ON p.userId = u.id
      LEFT JOIN categories c ON p.category = c.name
      LEFT JOIN likes l ON p.id = l.promptId AND l.userId = ?
      WHERE ${whereClause}
      ORDER BY ${orderClause}
      LIMIT ? OFFSET ?
    `;

    const [prompts] = await pool.query(query, [userId, ...whereParams, limit, offset]);
    
    // 解析tags字段并转换isLiked
    const formattedPrompts = prompts.map(prompt => ({
      ...prompt,
      tags: typeof prompt.tags === 'string' ? JSON.parse(prompt.tags || '[]') : prompt.tags || [],
      isLiked: !!prompt.isLiked // 将 1/0 转换为 true/false
    }));
    
    res.json({
      success: true,
      data: formattedPrompts,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('获取公开提示词错误:', error);
    res.status(500).json({
      success: false,
      message: '获取公开提示词失败',
      error: error.message
    });
  }
});

// 获取提示词 (通用端点)
app.get('/api/prompts', async (req, res) => {
  try {
    const [prompts] = await pool.query(`
      SELECT
        p.id,
        p.title,
        p.description,
        p.category,
        p.tags,
        p.likes,
        p.downloads,
        p.views,
        c.description as categoryDisplayName
      FROM prompts p
      LEFT JOIN categories c ON p.category = c.name
      WHERE p.status = 'published'
      ORDER BY p.createdAt DESC
    `);

    // 解析tags字段
    const formattedPrompts = prompts.map(prompt => ({
      ...prompt,
      tags: typeof prompt.tags === 'string' ? JSON.parse(prompt.tags || '[]') : prompt.tags
    }));

    res.json({
      success: true,
      data: formattedPrompts
    });
  } catch (error) {
    console.error('获取提示词错误:', error);
    res.status(500).json({
      success: false,
      message: '获取提示词失败',
      error: error.message
    });
  }
});

// 获取所有公开提示词的ID和更新时间 (用于sitemap生成)
app.get('/api/prompts/all-public-ids', async (req, res) => {
  try {
    const [prompts] = await pool.query(`
      SELECT
        p.id,
        p.updatedAt as updated_at
      FROM prompts p
      WHERE p.status = 'published' AND p.isPrivate = 0
      ORDER BY p.updatedAt DESC
    `);

    res.json({
      success: true,
      data: prompts
    });
  } catch (error) {
    console.error('获取公开提示词ID列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取公开提示词ID列表失败',
      error: error.message
    });
  }
});



// 获取我的提示词
app.get('/api/prompts/my', authenticateToken, async (req, res) => {
  try {
    const userId = req.userId;
    const { sortBy = 'latest', status, search } = req.query;

    // 获取分页参数
    const page = parseInt(req.query.page || '1');
    const limit = parseInt(req.query.limit || '6');
    const offset = (page - 1) * limit;

    // 构建WHERE条件
    let whereConditions = ['p.userId = ?'];
    let whereParams = [userId];

    // 状态筛选
    if (status && status !== 'all') {
      whereConditions.push('p.status = ?');
      whereParams.push(status);
    }

    // 搜索条件
    if (search) {
      whereConditions.push('(p.title LIKE ? OR p.description LIKE ? OR p.content LIKE ? OR p.tags LIKE ?)');
      whereParams.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
    }

    const whereClause = whereConditions.join(' AND ');

    // 确定排序方式
    let orderBy = 'p.createdAt DESC'; // 默认最新
    switch (sortBy) {
      case 'popular':
        orderBy = 'p.likes DESC, p.views DESC, p.createdAt DESC';
        break;
      case 'views':
        orderBy = 'p.views DESC, p.createdAt DESC';
        break;
      case 'downloads':
        orderBy = 'p.downloads DESC, p.createdAt DESC';
        break;
      case 'latest':
      default:
        orderBy = 'p.createdAt DESC';
        break;
    }

    // 获取总数
    const [totalCount] = await pool.query(`
      SELECT COUNT(*) as total
      FROM prompts p
      WHERE ${whereClause}
    `, whereParams);

    const total = totalCount[0].total;

    // 在查询中加入对 likes 表的连接，以判断当前用户是否点赞
    const [prompts] = await pool.query(`
      SELECT
        p.*,
        u.username as authorName,
        u.avatar as authorAvatar,
        c.description as categoryDisplayName,
        CASE WHEN l.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked
      FROM prompts p
      LEFT JOIN users u ON p.userId = u.id
      LEFT JOIN categories c ON p.category = c.name
      LEFT JOIN likes l ON p.id = l.promptId AND l.userId = ?
      WHERE ${whereClause}
      ORDER BY ${orderBy}
      LIMIT ? OFFSET ?
    `, [userId, ...whereParams, limit, offset]);

    // 解析tags字段并转换isLiked
    const promptsWithParsedTags = prompts.map(prompt => ({
      ...prompt,
      tags: Array.isArray(prompt.tags) ? prompt.tags : JSON.parse(prompt.tags || '[]'),
      isLiked: !!prompt.isLiked, // 将 1/0 转换为 true/false
      // 确保包含作者信息
      authorName: prompt.authorName || req.user?.username || '匿名创作者',
      authorAvatar: prompt.authorAvatar || req.user?.avatar || null
    }));

    res.json({
      success: true,
      data: promptsWithParsedTags,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('获取我的提示词失败:', error);
    res.status(500).json({
      success: false,
      message: '获取我的提示词失败'
    });
  }
});

// 获取单个提示词详情
app.get('/api/prompts/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.userId || null; // 从 optionalAuth 中间件获取

    const [prompts] = await pool.query(`
      SELECT
        p.*,
        u.username as authorName,
        u.avatar as authorAvatar,
        c.description as categoryDisplayName,
        CASE WHEN l.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked
      FROM prompts p
      LEFT JOIN users u ON p.userId = u.id
      LEFT JOIN categories c ON p.category = c.name
      LEFT JOIN likes l ON p.id = l.promptId AND l.userId = ?
      WHERE p.id = ?
    `, [userId, id]);

    const prompt = prompts[0];

    if (!prompt) {
      return res.status(404).json({ success: false, message: '提示词不存在' });
    }

    // 权限检查：私有提示词只能作者本人或管理员查看
    if (prompt.isPrivate && prompt.userId !== userId) {
      // 检查是否是管理员
      if (userId) {
        const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
        const user = users[0];
        const isAdmin = user && (user.email === '<EMAIL>' || user.role === 'admin');

        if (!isAdmin) {
          return res.status(403).json({ success: false, message: '无权查看该提示词' });
        }
      } else {
        return res.status(403).json({ success: false, message: '无权查看该提示词' });
      }
    }

    // 增加浏览量 (仅在非作者本人访问时)
    if (prompt.userId !== userId) {
      await pool.query('UPDATE prompts SET views = views + 1 WHERE id = ?', [id]);
      prompt.views = (prompt.views || 0) + 1;
    }

    const formattedPrompt = {
      ...prompt,
      isLiked: !!prompt.isLiked,
      tags: Array.isArray(prompt.tags) ? prompt.tags : JSON.parse(prompt.tags || '[]')
    };

    res.json({ success: true, data: formattedPrompt });
  } catch (error) {
    console.error('获取提示词详情失败:', error);
    res.status(500).json({ success: false, message: '获取提示词详情失败' });
  }
});

// 创建提示词
app.post('/api/prompts', authenticateToken, async (req, res) => {
  try {
    const userId = req.userId;
    const { title, description, category, tags, content, status, isPrivate } = req.body;

    // 验证必填字段
    if (!title || !content || !category) {
      return res.status(400).json({
        success: false,
        message: '标题、内容和分类不能为空'
      });
    }

    // 创建提示词
    const [result] = await pool.query(`
      INSERT INTO prompts (userId, title, description, category, tags, content, status, isPrivate, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [userId, title, description || '', category, JSON.stringify(tags || []), content, status || 'draft', isPrivate || false]);

    res.json({
      success: true,
      message: '提示词创建成功',
      data: {
        id: result.insertId
      }
    });
  } catch (error) {
    console.error('创建提示词失败:', error);
    res.status(500).json({
      success: false,
      message: '创建提示词失败',
      error: error.message
    });
  }
});

// 更新提示词
app.put('/api/prompts/:id', authenticateToken, async (req, res) => {
  try {
    const userId = req.userId;
    const { id } = req.params;
    const { title, description, content, category, tags, isPrivate, status } = req.body;

    // 验证必填字段
    if (!title || !content || !category) {
      return res.status(400).json({
        success: false,
        message: '标题、内容和分类不能为空'
      });
    }

    // 先检查提示词是否存在
    const [existing] = await pool.query('SELECT * FROM prompts WHERE id = ?', [id]);

    if (existing.length === 0) {
      return res.status(404).json({
        success: false,
        message: '提示词不存在'
      });
    }

    const prompt = existing[0];

    // 检查权限：只有作者或管理员可以编辑
    const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
    const user = users[0];
    const isAdmin = user && (user.email === '<EMAIL>' || user.role === 'admin');

    if (prompt.userId !== userId && !isAdmin) {
      return res.status(403).json({
        success: false,
        message: '没有权限编辑此提示词'
      });
    }

    // 确保所有参数都有默认值，避免undefined
    const updateData = {
      title: title || prompt.title,
      description: description !== undefined ? description : (prompt.description || ''),
      content: content || prompt.content,
      category: category || prompt.category,
      tags: JSON.stringify(tags || []),
      isPrivate: isPrivate !== undefined ? (isPrivate ? 1 : 0) : prompt.isPrivate,
      status: status || prompt.status
    };

    await pool.query(`
      UPDATE prompts SET
        title = ?, description = ?, content = ?, category = ?,
        tags = ?, isPrivate = ?, status = ?, updatedAt = NOW()
      WHERE id = ?
    `, [
      updateData.title,
      updateData.description,
      updateData.content,
      updateData.category,
      updateData.tags,
      updateData.isPrivate,
      updateData.status,
      id
    ]);

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新提示词失败:', error);
    res.status(500).json({
      success: false,
      message: '更新失败'
    });
  }
});

// 删除提示词
app.delete('/api/prompts/:id', authenticateToken, async (req, res) => {
  try {
    const userId = req.userId;
    const { id } = req.params;

    // 先检查提示词是否存在
    const [existing] = await pool.query('SELECT * FROM prompts WHERE id = ?', [id]);

    if (existing.length === 0) {
      return res.status(404).json({
        success: false,
        message: '提示词不存在'
      });
    }

    const prompt = existing[0];

    // 检查权限：只有作者或管理员可以删除
    const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
    const user = users[0];
    const isAdmin = user && (user.email === '<EMAIL>' || user.role === 'admin');

    if (prompt.userId !== userId && !isAdmin) {
      return res.status(403).json({
        success: false,
        message: '没有权限删除此提示词'
      });
    }

    // 删除相关的点赞记录
    await pool.query('DELETE FROM likes WHERE promptId = ?', [id]);

    // 删除提示词
    const [result] = await pool.query('DELETE FROM prompts WHERE id = ?', [id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '删除失败'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除提示词失败:', error);
    res.status(500).json({
      success: false,
      message: '删除失败'
    });
  }
});

// 增加复制次数
app.post('/api/prompts/:id/copy', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.userId || 'anonymous';

    // 检查提示词是否存在
    const [prompts] = await pool.query('SELECT * FROM prompts WHERE id = ?', [id]);

    if (prompts.length === 0) {
      return res.status(404).json({
        success: false,
        message: '提示词不存在'
      });
    }

    // 增加复制次数（使用downloads字段表示复制次数）
    await pool.query('UPDATE prompts SET downloads = downloads + 1 WHERE id = ?', [id]);

    // 获取更新后的复制次数
    const [updatedPrompts] = await pool.query('SELECT downloads FROM prompts WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '复制次数已更新',
      data: {
        downloads: updatedPrompts[0].downloads
      }
    });
  } catch (error) {
    console.error('更新复制次数失败:', error);
    res.status(500).json({
      success: false,
      message: '更新失败'
    });
  }
});

// 点赞/取消点赞提示词
app.post('/api/prompts/:id/like', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.userId;

    // 检查提示词是否存在
    const [prompts] = await pool.query('SELECT * FROM prompts WHERE id = ?', [id]);
    if (prompts.length === 0) {
      return res.status(404).json({
        success: false,
        message: '提示词不存在'
      });
    }

    // 检查用户是否已经点赞过
    const [existingLikes] = await pool.query(
      'SELECT * FROM likes WHERE promptId = ? AND userId = ?',
      [id, userId]
    );

    if (existingLikes.length > 0) {
      // 取消点赞
      await pool.query('DELETE FROM likes WHERE promptId = ? AND userId = ?', [id, userId]);
      await pool.query('UPDATE prompts SET likes = likes - 1 WHERE id = ?', [id]);

      // 获取更新后的点赞数
      const [updatedPrompts] = await pool.query('SELECT likes FROM prompts WHERE id = ?', [id]);

      res.json({
        success: true,
        message: '取消点赞成功',
        data: {
          isLiked: false,
          likesCount: updatedPrompts[0].likes
        }
      });
    } else {
      // 添加点赞
      await pool.query('INSERT INTO likes (promptId, userId) VALUES (?, ?)', [id, userId]);
      await pool.query('UPDATE prompts SET likes = likes + 1 WHERE id = ?', [id]);

      // 获取更新后的点赞数
      const [updatedPrompts] = await pool.query('SELECT likes FROM prompts WHERE id = ?', [id]);

      res.json({
        success: true,
        message: '点赞成功',
        data: {
          isLiked: true,
          likesCount: updatedPrompts[0].likes
        }
      });
    }
  } catch (error) {
    console.error('点赞操作失败:', error);
    res.status(500).json({
      success: false,
      message: '点赞操作失败'
    });
  }
});




// 管理员权限验证中间件
const requireAdmin = async (req, res, next) => {
  try {
    let token = null

    // 1. 首先尝试从Authorization头获取token (向后兼容)
    const authHeader = req.headers['authorization']
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1]
    }

    // 2. 如果没有Authorization头，尝试从Cookie获取
    if (!token && req.cookies && req.cookies.prompthub_token) {
      token = req.cookies.prompthub_token
    }

    if (!token) {
      return res.status(401).json({ success: false, message: '需要管理员权限' });
    }

    const decoded = jwt.verify(token, config.JWT.SECRET);
    const [users] = await pool.query('SELECT * FROM users WHERE id = ? AND isActive = 1', [decoded.userId]);

    if (!users.length) {
      return res.status(401).json({ success: false, message: '用户不存在' });
    }

    const user = users[0];
    // 检查是否是管理员（可以根据实际需求调整）
    if (user.email !== '<EMAIL>' && user.role !== 'admin') {
      return res.status(403).json({ success: false, message: '需要管理员权限' });
    }

    req.user = user;
    req.userId = user.id;
    next();
  } catch (error) {
    console.error('管理员权限验证失败:', error);
    res.status(401).json({ success: false, message: '权限验证失败' });
  }
};

// 管理员统计数据
app.get('/api/admin/stats', requireAdmin, async (req, res) => {
  try {
    // 获取用户总数
    const [userCount] = await pool.query('SELECT COUNT(*) as count FROM users');

    // 获取提示词总数
    const [promptCount] = await pool.query('SELECT COUNT(*) as count FROM prompts');

    // 获取总浏览量
    const [viewsSum] = await pool.query('SELECT SUM(views) as total FROM prompts');

    // 获取总点赞数
    const [likesSum] = await pool.query('SELECT SUM(likes) as total FROM prompts');

    // 获取总下载数
    const [downloadsSum] = await pool.query('SELECT SUM(downloads) as total FROM prompts');

    // 获取本月新增提示词数
    const [recentPrompts] = await pool.query(
      'SELECT COUNT(*) as count FROM prompts WHERE createdAt >= DATE_SUB(NOW(), INTERVAL 30 DAY)'
    );

    res.json({
      success: true,
      data: {
        totalUsers: userCount[0].count,
        totalPrompts: promptCount[0].count,
        totalViews: viewsSum[0].total || 0,
        totalLikes: likesSum[0].total || 0,
        totalDownloads: downloadsSum[0].total || 0,
        recentPrompts: recentPrompts[0].count
      }
    });
  } catch (error) {
    console.error('获取管理员统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error.message
    });
  }
});

// 管理员获取所有提示词
app.get('/api/admin/prompts', requireAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const category = req.query.category || '';

    let whereClause = '';
    let queryParams = [];

    if (search) {
      whereClause += ' WHERE (p.title LIKE ? OR p.description LIKE ? OR p.content LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    if (category) {
      whereClause += search ? ' AND p.category = ?' : ' WHERE p.category = ?';
      queryParams.push(category);
    }

    // 获取提示词列表
    const [prompts] = await pool.query(`
      SELECT
        p.*,
        u.username as authorName
      FROM prompts p
      LEFT JOIN users u ON p.userId = u.id
      ${whereClause}
      ORDER BY p.createdAt DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset]);

    // 获取总数
    const [totalCount] = await pool.query(`
      SELECT COUNT(*) as count
      FROM prompts p
      ${whereClause}
    `, queryParams);

    const total = totalCount[0].count;
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: prompts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('获取管理员提示词列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取提示词列表失败',
      error: error.message
    });
  }
});

// 管理员获取所有用户
app.get('/api/admin/users', requireAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';

    let whereClause = '';
    let queryParams = [];

    if (search) {
      whereClause += ' WHERE (u.username LIKE ? OR u.email LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // 获取用户列表，包含提示词统计
    const [users] = await pool.query(`
      SELECT
        u.id,
        u.username,
        u.email,
        u.phone,
        u.role,
        u.isActive,
        u.createdAt,
        u.updatedAt,
        COUNT(p.id) as promptCount
      FROM users u
      LEFT JOIN prompts p ON u.id = p.userId
      ${whereClause}
      GROUP BY u.id
      ORDER BY u.createdAt DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset]);

    // 获取总数
    const [totalCount] = await pool.query(`
      SELECT COUNT(*) as count
      FROM users u
      ${whereClause}
    `, queryParams);

    const total = totalCount[0].count;
    const totalPages = Math.ceil(total / limit);

    // 格式化用户数据
    const formattedUsers = users.map(user => ({
      ...user,
      status: user.isActive ? 'active' : 'inactive', // 根据isActive字段设置状态
      lastLogin: user.updatedAt // 使用updatedAt作为最后登录时间的近似值
    }));

    res.json({
      success: true,
      data: formattedUsers,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('获取管理员用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      error: error.message
    });
  }
});

// 管理员更新用户状态
app.put('/api/admin/users/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { status, role } = req.body;

    // 检查用户是否存在
    const [existingUser] = await pool.query('SELECT * FROM users WHERE id = ?', [id]);

    if (existingUser.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新用户信息
    const updates = {};
    if (status !== undefined) {
      // 将status转换为isActive字段
      updates.isActive = status === 'active' ? 1 : 0;
    }
    if (role !== undefined) {
      updates.role = role;
    }

    if (Object.keys(updates).length > 0) {
      const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
      const values = Object.values(updates);

      await pool.query(`
        UPDATE users SET ${setClause}, updatedAt = NOW() WHERE id = ?
      `, [...values, id]);
    }

    res.json({
      success: true,
      message: '用户信息更新成功'
    });
  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '更新用户信息失败',
      error: error.message
    });
  }
});

// 管理员获取分析数据
app.get('/api/admin/analytics', requireAdmin, async (req, res) => {
  try {
    const range = req.query.range || '7d'; // 7d, 30d, 90d

    // 根据时间范围计算日期
    let days = 7;
    if (range === '30d') days = 30;
    if (range === '90d') days = 90;

    // 获取基础统计数据
    const [userCount] = await pool.query('SELECT COUNT(*) as count FROM users');
    const [promptCount] = await pool.query('SELECT COUNT(*) as count FROM prompts');
    const [viewsSum] = await pool.query('SELECT SUM(views) as total FROM prompts');
    const [likesSum] = await pool.query('SELECT SUM(likes) as total FROM prompts');
    const [downloadsSum] = await pool.query('SELECT SUM(downloads) as total FROM prompts');

    // 获取增长数据（与上一个周期对比）
    const [prevUserCount] = await pool.query(`
      SELECT COUNT(*) as count FROM users
      WHERE createdAt < DATE_SUB(NOW(), INTERVAL ? DAY)
    `, [days]);

    const [prevPromptCount] = await pool.query(`
      SELECT COUNT(*) as count FROM prompts
      WHERE createdAt < DATE_SUB(NOW(), INTERVAL ? DAY)
    `, [days]);

    // 计算增长率
    const userGrowth = prevUserCount[0].count > 0
      ? ((userCount[0].count - prevUserCount[0].count) / prevUserCount[0].count * 100)
      : 0;

    const promptGrowth = prevPromptCount[0].count > 0
      ? ((promptCount[0].count - prevPromptCount[0].count) / prevPromptCount[0].count * 100)
      : 0;

    // 计算参与度（点赞数/总浏览数）
    const engagementRate = viewsSum[0].total > 0
      ? (likesSum[0].total / viewsSum[0].total * 100)
      : 0;

    // 获取分类分布
    const [categories] = await pool.query(`
      SELECT
        c.name,
        c.description,
        COUNT(p.id) as count,
        ROUND(COUNT(p.id) * 100.0 / (SELECT COUNT(*) FROM prompts WHERE status = 'published'), 1) as percentage
      FROM categories c
      LEFT JOIN prompts p ON p.category = c.name AND p.status = 'published'
      WHERE c.isActive = TRUE
      GROUP BY c.name, c.description
      ORDER BY count DESC
    `);

    // 获取热门提示词
    const [topPrompts] = await pool.query(`
      SELECT
        id,
        title,
        views,
        likes,
        downloads
      FROM prompts
      WHERE status = 'published'
      ORDER BY (views + likes * 2 + downloads * 3) DESC
      LIMIT 5
    `);

    // 获取趋势数据（最近几天的数据）
    const [trends] = await pool.query(`
      SELECT
        DATE(createdAt) as date,
        COUNT(CASE WHEN table_name = 'users' THEN 1 END) as users,
        COUNT(CASE WHEN table_name = 'prompts' THEN 1 END) as prompts
      FROM (
        SELECT createdAt, 'users' as table_name FROM users WHERE createdAt >= DATE_SUB(NOW(), INTERVAL ? DAY)
        UNION ALL
        SELECT createdAt, 'prompts' as table_name FROM prompts WHERE createdAt >= DATE_SUB(NOW(), INTERVAL ? DAY)
      ) combined
      GROUP BY DATE(createdAt)
      ORDER BY date DESC
      LIMIT ?
    `, [days, days, days]);

    res.json({
      success: true,
      data: {
        overview: {
          totalUsers: userCount[0].count,
          totalPrompts: promptCount[0].count,
          totalViews: viewsSum[0].total || 0,
          totalLikes: likesSum[0].total || 0,
          totalDownloads: downloadsSum[0].total || 0,
          userGrowth: Math.round(userGrowth * 10) / 10,
          promptGrowth: Math.round(promptGrowth * 10) / 10,
          engagementRate: Math.round(engagementRate * 10) / 10
        },
        categories: categories.map(cat => ({
          name: cat.description || cat.name,
          count: cat.count,
          percentage: cat.percentage || 0
        })),
        topPrompts: topPrompts,
        trends: trends.map(trend => ({
          date: trend.date,
          users: trend.users || 0,
          prompts: trend.prompts || 0,
          views: 0, // 可以后续添加每日浏览量统计
          likes: 0  // 可以后续添加每日点赞量统计
        }))
      }
    });
  } catch (error) {
    console.error('获取分析数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取分析数据失败',
      error: error.message
    });
  }
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'API服务正常',
    timestamp: new Date().toISOString(),
    config: {
      host: config.DATABASE.HOST,
      port: config.DATABASE.PORT,
      database: config.DATABASE.NAME
    }
  });
});

// 导出app对象而不是直接启动服务器
module.exports = app;
const mysql = require('mysql2/promise');
const config = require('../../config');

// 创建MySQL连接池
const pool = mysql.createPool({
  host: config.DATABASE.HOST,
  port: config.DATABASE.PORT,
  user: config.DATABASE.USER,
  password: config.DATABASE.PASSWORD,
  database: config.DATABASE.NAME,
  charset: config.DATABASE.CHARSET
});

// 数据库操作适配器，将SQLite风格的API转换为MySQL
const db = {
  async get(sql, params = []) {
    const [rows] = await pool.execute(sql, params);
    return rows[0] || null;
  },

  async all(sql, params = []) {
    const [rows] = await pool.execute(sql, params);
    return rows;
  },

  async run(sql, params = []) {
    const [result] = await pool.execute(sql, params);
    return {
      lastID: result.insertId,
      changes: result.affectedRows
    };
  }
};

// 获取所有分类
const getCategories = async (req, res) => {
  try {
    const categories = await db.all(`
      SELECT 
        id,
        name,
        description,
        icon,
        color,
        prompt_count,
        sort_order,
        isActive
      FROM categories 
      WHERE isActive = TRUE
      ORDER BY sort_order ASC
    `);
    
    // 直接使用数据库中的description字段作为显示名称
    // 不再使用硬编码的映射表
    const formattedCategories = categories.map(category => ({
      ...category,
      value: category.name, // 英文标识符，用于数据库存储
      label: category.description || category.name, // 使用description作为中文显示名称
      displayName: category.description || category.name // 统一的显示名称字段
    }));
    
    res.json({
      success: true,
      data: formattedCategories
    });
  } catch (error) {
    console.error('获取分类失败:', error);
    res.status(500).json({
      success: false,
      message: '获取分类失败'
    });
  }
};

// 获取分类统计
const getCategoryStats = async (req, res) => {
  try {
    const stats = await db.all(`
      SELECT 
        c.id,
        c.name,
        c.description,
        c.icon,
        c.color,
        COUNT(p.id) as prompt_count
      FROM categories c
      LEFT JOIN prompts p ON p.category = c.name AND p.status = 'published'
      WHERE c.isActive = TRUE
      GROUP BY c.id, c.name, c.description, c.icon, c.color
      ORDER BY c.sort_order ASC
    `);
    
    // 格式化统计数据，使用description作为显示名称
    const formattedStats = stats.map(stat => ({
      ...stat,
      displayName: stat.description || stat.name
    }));
    
    res.json({
      success: true,
      data: formattedStats
    });
  } catch (error) {
    console.error('获取分类统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取分类统计失败'
    });
  }
};

module.exports = {
  getCategories,
  getCategoryStats
}; 
const bcrypt = require('bcryptjs')
const mysql = require('mysql2/promise')
const config = require('../../config')
const { generateToken } = require('../middleware/auth')

// 创建MySQL连接池
const pool = mysql.createPool({
  host: config.DATABASE.HOST,
  port: config.DATABASE.PORT,
  user: config.DATABASE.USER,
  password: config.DATABASE.PASSWORD,
  database: config.DATABASE.NAME,
  charset: config.DATABASE.CHARSET
})

// 数据库操作适配器，将SQLite风格的API转换为MySQL
const db = {
  async get(sql, params = []) {
    const [rows] = await pool.execute(sql, params);
    return rows[0] || null;
  },

  async all(sql, params = []) {
    const [rows] = await pool.execute(sql, params);
    return rows;
  },

  async run(sql, params = []) {
    const [result] = await pool.execute(sql, params);
    return {
      lastID: result.insertId,
      changes: result.affectedRows
    };
  }
}

// 用户注册
const register = async (req, res) => {
  try {
    const { username, email, phone, password } = req.body

    // 验证必需字段
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码为必填项'
      })
    }

    if (!email && !phone) {
      return res.status(400).json({
        success: false,
        message: '邮箱或手机号至少填写一个'
      })
    }

    // 检查用户是否已存在
    let existingUser
    if (email) {
      existingUser = await db.get('SELECT * FROM users WHERE email = ?', [email])
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '该邮箱已注册'
        })
      }
    }

    if (phone) {
      existingUser = await db.get('SELECT * FROM users WHERE phone = ?', [phone])
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '该手机号已注册'
        })
      }
    }

    existingUser = await db.get('SELECT * FROM users WHERE username = ?', [username])
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '该用户名已被使用'
      })
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10)

    // 创建用户
    const result = await db.run(`
      INSERT INTO users (username, email, phone, password)
      VALUES (?, ?, ?, ?)
    `, [username, email || null, phone || null, hashedPassword])

    const user = await db.get('SELECT id, username, email, phone FROM users WHERE id = ?', [result.lastID])

    // 生成token
    const token = generateToken(user)

    // 设置安全Cookie
    const cookieOptions = {
      httpOnly: config.COOKIE.HTTP_ONLY,
      secure: config.COOKIE.SECURE,
      sameSite: config.COOKIE.SAME_SITE,
      maxAge: config.COOKIE.MAX_AGE,
      domain: config.COOKIE.DOMAIN,
      path: '/'
    }

    res.cookie('prompthub_token', token, cookieOptions)

    // 检查是否为插件请求
    const isExtension = req.headers['x-requested-with'] === 'extension' || req.query.extension === 'true'
    
    if (isExtension) {
      // 插件请求返回token（用于立即访问）
      res.status(201).json({
        success: true,
        message: '注册成功',
        data: {
          user,
          token, // 插件可以立即使用
          cookieSet: true
        }
      })
    } else {
      // 网站请求不返回token（使用Cookie）
      res.status(201).json({
        success: true,
        message: '注册成功',
        data: {
          user
        }
      })
    }
  } catch (error) {
    console.error('注册失败:', error)
    res.status(500).json({
      success: false,
      message: '注册失败'
    })
  }
}

// 用户登录
const login = async (req, res) => {
  try {
    const { identifier, password } = req.body // identifier 可以是用户名、邮箱或手机号

    if (!identifier || !password) {
      return res.status(400).json({
        success: false,
        message: '请填写登录信息'
      })
    }

    // 查找用户
    const user = await db.get(`
      SELECT * FROM users 
      WHERE (username = ? OR email = ? OR phone = ?) AND isActive = 1
    `, [identifier, identifier, identifier])

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在或账号已被禁用'
      })
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '密码错误'
      })
    }

    // 生成token
    const token = generateToken(user)

    // 设置安全Cookie
    const cookieOptions = {
      httpOnly: config.COOKIE.HTTP_ONLY,
      secure: config.COOKIE.SECURE,
      sameSite: config.COOKIE.SAME_SITE,
      maxAge: config.COOKIE.MAX_AGE,
      domain: config.COOKIE.DOMAIN,
      path: '/'
    }

    res.cookie('prompthub_token', token, cookieOptions)

    // 返回用户信息（不包含密码）
    const { password: _, ...userWithoutPassword } = user

    // 检查是否为插件请求
    const isExtension = req.headers['x-requested-with'] === 'extension' || req.query.extension === 'true'
    
    if (isExtension) {
      // 插件请求返回token（用于立即访问）
      res.json({
        success: true,
        message: '登录成功',
        data: {
          user: userWithoutPassword,
          token, // 插件可以立即使用
          cookieSet: true
        }
      })
    } else {
      // 网站请求不返回token（使用Cookie）
      res.json({
        success: true,
        message: '登录成功',
        data: {
          user: userWithoutPassword
        }
      })
    }
  } catch (error) {
    console.error('登录失败:', error)
    res.status(500).json({
      success: false,
      message: '登录失败'
    })
  }
}

// 获取当前用户信息
const getCurrentUser = async (req, res) => {
  try {
    const user = await db.get(`
      SELECT id, username, email, phone, avatar, bio, role, createdAt
      FROM users WHERE id = ?
    `, [req.userId])

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      })
    }

    res.json({
      success: true,
      data: user
    })
  } catch (error) {
    console.error('获取用户信息失败:', error)
    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    })
  }
}

// 更新用户资料
const updateProfile = async (req, res) => {
  try {
    const { username, email, phone, avatar, bio } = req.body
    const userId = req.userId

    // 检查用户名、邮箱、手机号是否被其他用户使用
    if (username) {
      const existingUser = await db.get('SELECT * FROM users WHERE username = ? AND id != ?', [username, userId])
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '该用户名已被使用'
        })
      }
    }

    if (email) {
      const existingUser = await db.get('SELECT * FROM users WHERE email = ? AND id != ?', [email, userId])
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '该邮箱已被使用'
        })
      }
    }

    if (phone) {
      const existingUser = await db.get('SELECT * FROM users WHERE phone = ? AND id != ?', [phone, userId])
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '该手机号已被使用'
        })
      }
    }

    // 更新用户信息
    await db.run(`
      UPDATE users SET 
        username = COALESCE(?, username),
        email = COALESCE(?, email),
        phone = COALESCE(?, phone),
        avatar = COALESCE(?, avatar),
        bio = COALESCE(?, bio),
        updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [username, email, phone, avatar, bio, userId])

    res.json({
      success: true,
      message: '资料更新成功'
    })
  } catch (error) {
    console.error('更新资料失败:', error)
    res.status(500).json({
      success: false,
      message: '更新资料失败'
    })
  }
}

// 修改密码
const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body
    const userId = req.userId

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: '请填写当前密码和新密码'
      })
    }

    // 获取用户当前密码
    const user = await db.get('SELECT password FROM users WHERE id = ?', [userId])
    
    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password)
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: '当前密码错误'
      })
    }

    // 加密新密码
    const hashedNewPassword = await bcrypt.hash(newPassword, 10)

    // 更新密码
    await db.run(`
      UPDATE users SET password = ?, updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [hashedNewPassword, userId])

    res.json({
      success: true,
      message: '密码修改成功'
    })
  } catch (error) {
    console.error('修改密码失败:', error)
    res.status(500).json({
      success: false,
      message: '修改密码失败'
    })
  }
}

// 用户登出
const logout = (req, res) => {
  // 清除Cookie - 尝试清除所有可能的域名配置
  const cookieOptions = {
    httpOnly: config.COOKIE.HTTP_ONLY,
    secure: config.COOKIE.SECURE,
    sameSite: config.COOKIE.SAME_SITE,
    path: '/'
  }

  // 清除当前配置的域名
  res.clearCookie('prompthub_token', {
    ...cookieOptions,
    domain: config.COOKIE.DOMAIN
  })

  // 清除可能的旧域名配置（兼容性处理）
  res.clearCookie('prompthub_token', {
    ...cookieOptions,
    domain: 'localhost'
  })

  res.clearCookie('prompthub_token', {
    ...cookieOptions,
    domain: '.prompthub.xin'
  })

  res.clearCookie('prompthub_token', {
    ...cookieOptions,
    domain: 'prompthub.xin'
  })

  // 也清除不带域名的Cookie
  res.clearCookie('prompthub_token', {
    httpOnly: config.COOKIE.HTTP_ONLY,
    secure: config.COOKIE.SECURE,
    sameSite: config.COOKIE.SAME_SITE,
    path: '/'
  })

  res.json({
    success: true,
    message: '登出成功'
  })
}

module.exports = {
  register,
  login,
  getCurrentUser,
  updateProfile,
  changePassword,
  logout
} 
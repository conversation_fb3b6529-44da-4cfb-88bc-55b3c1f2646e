const app = require('./app')
const config = require('../config')

const PORT = config.SERVER.PORT

// 启动服务器
async function startServer() {
  try {
    // 数据库连接在各个模块中独立管理
    
    // 启动HTTP服务器
    const server = app.listen(PORT, () => {
      console.log('🚀 ========================================')
      console.log(`📡 AI提示词平台API服务已启动`)
      console.log(`🌍 环境: ${config.SERVER.NODE_ENV}`)
      console.log(`🔗 本地地址: http://localhost:${PORT}`)
      console.log(`📚 API文档: http://localhost:${PORT}/`)
      console.log(`💚 健康检查: http://localhost:${PORT}/health`)
      console.log('🚀 ========================================')
    })

    // 优雅关闭
    process.on('SIGTERM', () => {
      console.log('🔄 收到SIGTERM信号，开始优雅关闭服务器...')
      server.close(() => {
        console.log('✅ 服务器已关闭')
        process.exit(0)
      })
    })

    process.on('SIGINT', () => {
      console.log('🔄 收到SIGINT信号，开始优雅关闭服务器...')
      server.close(() => {
        console.log('✅ 服务器已关闭')
        process.exit(0)
      })
    })

    // 未捕获的异常处理
    process.on('uncaughtException', (error) => {
      console.error('💥 未捕获的异常:', error)
      process.exit(1)
    })

    process.on('unhandledRejection', (reason, promise) => {
      console.error('💥 未处理的Promise拒绝:', reason)
      console.error('在Promise:', promise)
      process.exit(1)
    })

    return server
  } catch (error) {
    console.error('💥 服务器启动失败:', error)
    process.exit(1)
  }
}

// 启动服务器
startServer().then(server => {
  console.log('服务器启动成功')
}).catch(error => {
  console.error('启动服务器失败:', error)
  process.exit(1)
})

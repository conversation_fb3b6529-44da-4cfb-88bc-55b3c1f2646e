const jwt = require('jsonwebtoken')
const mysql = require('mysql2/promise')
const config = require('../../config')

// 创建MySQL连接池
const pool = mysql.createPool({
  host: config.DATABASE.HOST,
  port: config.DATABASE.PORT,
  user: config.DATABASE.USER,
  password: config.DATABASE.PASSWORD,
  database: config.DATABASE.NAME,
  charset: config.DATABASE.CHARSET
})

// 生成JWT token
const generateToken = (user) => {
  return jwt.sign(
    { 
      userId: user.id, 
      username: user.username 
    },
    config.JWT.SECRET,
    { 
      expiresIn: config.JWT.EXPIRES_IN,
      algorithm: config.JWT.ALGORITHM
    }
  )
}

// 验证token中间件
const authenticateToken = async (req, res, next) => {
  try {
    let token = null

    console.log('🔐 认证中间件 - 请求路径:', req.path)
    console.log('🔐 认证中间件 - Authorization头:', req.headers['authorization'] ? '存在' : '不存在')
    console.log('🔐 认证中间件 - Cookies:', req.cookies ? Object.keys(req.cookies) : '无Cookies')

    // 1. 首先尝试从Authorization头获取token (向后兼容)
    const authHeader = req.headers['authorization']
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1]
      console.log('🔐 从Authorization头获取token')
    }

    // 2. 如果没有Authorization头，尝试从Cookie获取
    if (!token && req.cookies && req.cookies.prompthub_token) {
      token = req.cookies.prompthub_token
      console.log('🔐 从Cookie获取token')
    }

    if (!token) {
      console.log('❌ 认证失败: 访问令牌缺失')
      return res.status(401).json({
        success: false,
        message: '访问令牌缺失'
      })
    }

    const decoded = jwt.verify(token, config.JWT.SECRET)
    
    // 从数据库中验证用户是否存在
    const [users] = await pool.execute('SELECT * FROM users WHERE id = ? AND isActive = 1', [decoded.userId])
    const user = users[0]
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '无效的令牌'
      })
    }

    req.userId = user.id
    req.user = user
    next()
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '令牌已过期'
      })
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '无效的令牌'
      })
    }

    console.error('Auth middleware error:', error)
    return res.status(500).json({
      success: false,
      message: '认证失败'
    })
  }
}

// 可选认证中间件（允许匿名访问）
const optionalAuth = async (req, res, next) => {
  let token = null

  // 1. 首先尝试从Authorization头获取token
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.split(' ')[1];
  }

  // 2. 如果没有Authorization头，尝试从Cookie获取
  if (!token && req.cookies && req.cookies.prompthub_token) {
    token = req.cookies.prompthub_token
  }

  if (!token) {
    return next(); // 没有token，继续执行，不附加用户信息
  }

  try {
    const decoded = jwt.verify(token, config.JWT.SECRET);
    const [users] = await pool.execute('SELECT id, username, role, isActive FROM users WHERE id = ?', [decoded.userId])
    const user = users[0];

    if (user && user.isActive) {
      req.userId = user.id; // 附加userId
      req.user = user;     // 附加完整用户信息
    }
  } catch (error) {
    // 令牌无效或过期，直接忽略，不中断请求
    console.warn('可选认证警告：', error.message);
  }
  
  next();
}

module.exports = {
  generateToken,
  authenticateToken,
  optionalAuth
} 
const Joi = require('joi')

// 用户注册验证规则
const registerSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .required()
    .messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少需要3个字符',
      'string.max': '用户名不能超过30个字符',
      'any.required': '用户名是必填项'
    }),
  
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱是必填项'
    }),
  
  password: Joi.string()
    .min(6)
    .max(128)
    .required()
    .messages({
      'string.min': '密码至少需要6个字符',
      'string.max': '密码不能超过128个字符',
      'any.required': '密码是必填项'
    }),
  
  avatar_url: Joi.string()
    .uri()
    .optional()
    .messages({
      'string.uri': '头像URL格式不正确'
    })
})

// 用户登录验证规则
const loginSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱是必填项'
    }),
  
  password: Joi.string()
    .required()
    .messages({
      'any.required': '密码是必填项'
    })
})

// 提示词创建验证规则
const promptCreateSchema = Joi.object({
  title: Joi.string()
    .min(1)
    .max(200)
    .required()
    .messages({
      'string.min': '标题不能为空',
      'string.max': '标题不能超过200个字符',
      'any.required': '标题是必填项'
    }),
  
  content: Joi.string()
    .min(1)
    .required()
    .messages({
      'string.min': '内容不能为空',
      'any.required': '内容是必填项'
    }),
  
  category: Joi.string()
    .max(50)
    .optional()
    .messages({
      'string.max': '分类不能超过50个字符'
    }),
  
  tags: Joi.array()
    .items(Joi.string().max(30))
    .max(10)
    .optional()
    .messages({
      'array.max': '标签不能超过10个',
      'string.max': '单个标签不能超过30个字符'
    }),
  
  is_public: Joi.boolean()
    .default(true)
    .optional()
})

// 提示词更新验证规则
const promptUpdateSchema = Joi.object({
  title: Joi.string()
    .min(1)
    .max(200)
    .optional()
    .messages({
      'string.min': '标题不能为空',
      'string.max': '标题不能超过200个字符'
    }),

  description: Joi.string()
    .max(500)
    .allow('')
    .optional()
    .messages({
      'string.max': '描述不能超过500个字符'
    }),

  content: Joi.string()
    .min(1)
    .optional()
    .messages({
      'string.min': '内容不能为空'
    }),

  category: Joi.string()
    .max(50)
    .optional()
    .messages({
      'string.max': '分类不能超过50个字符'
    }),

  tags: Joi.array()
    .items(Joi.string().max(30))
    .max(10)
    .optional()
    .messages({
      'array.max': '标签不能超过10个',
      'string.max': '单个标签不能超过30个字符'
    }),

  status: Joi.string()
    .valid('draft', 'published')
    .optional()
    .messages({
      'any.only': '状态只能是 draft 或 published'
    }),

  isPrivate: Joi.alternatives()
    .try(
      Joi.boolean(),
      Joi.number().valid(0, 1).custom((value) => Boolean(value))
    )
    .optional(),

  is_public: Joi.boolean()
    .optional()
})

// 创建验证中间件
const createValidationMiddleware = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // 返回所有错误
      stripUnknown: true // 移除未知字段
    })

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))

      return res.status(400).json({
        error: '数据验证失败',
        details: errors
      })
    }

    // 将验证后的数据替换原始数据
    req.body = value
    next()
  }
}

// 分页参数验证
const paginationSchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .optional(),
  
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .optional(),
  
  sort: Joi.string()
    .valid('created_at', 'updated_at', 'title', 'likes_count', 'views_count')
    .default('created_at')
    .optional(),
  
  order: Joi.string()
    .valid('asc', 'desc')
    .default('desc')
    .optional()
})

const validatePagination = (req, res, next) => {
  const { error, value } = paginationSchema.validate(req.query, {
    stripUnknown: true
  })

  if (error) {
    return res.status(400).json({
      error: '分页参数验证失败',
      details: error.details.map(detail => detail.message)
    })
  }

  req.pagination = value
  next()
}

module.exports = {
  validateRegister: createValidationMiddleware(registerSchema),
  validateLogin: createValidationMiddleware(loginSchema),
  validatePromptCreate: createValidationMiddleware(promptCreateSchema),
  validatePromptUpdate: createValidationMiddleware(promptUpdateSchema),
  validatePagination
} 
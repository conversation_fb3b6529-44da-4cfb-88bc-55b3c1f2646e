const database = require('../config/database')

class User {
  constructor(data) {
    this.id = data.id
    this.username = data.username
    this.email = data.email
    this.password = data.password
    this.avatar_url = data.avatar_url
    this.created_at = data.created_at
    this.updated_at = data.updated_at
  }

  // 创建新用户
  static create(userData) {
    return new Promise((resolve, reject) => {
      const { username, email, password, avatar_url } = userData
      const sql = `
        INSERT INTO users (username, email, password, avatar_url)
        VALUES (?, ?, ?, ?)
      `
      
      database.getDB().run(sql, [username, email, password, avatar_url], function(err) {
        if (err) {
          reject(err)
        } else {
          // 获取新创建的用户
          User.findById(this.lastID)
            .then(user => resolve(user))
            .catch(reject)
        }
      })
    })
  }

  // 根据ID查找用户
  static findById(id) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM users WHERE id = ?'
      
      database.getDB().get(sql, [id], (err, row) => {
        if (err) {
          reject(err)
        } else if (row) {
          resolve(new User(row))
        } else {
          resolve(null)
        }
      })
    })
  }

  // 根据邮箱查找用户
  static findByEmail(email) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM users WHERE email = ?'
      
      database.getDB().get(sql, [email], (err, row) => {
        if (err) {
          reject(err)
        } else if (row) {
          resolve(new User(row))
        } else {
          resolve(null)
        }
      })
    })
  }

  // 根据用户名查找用户
  static findByUsername(username) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM users WHERE username = ?'
      
      database.getDB().get(sql, [username], (err, row) => {
        if (err) {
          reject(err)
        } else if (row) {
          resolve(new User(row))
        } else {
          resolve(null)
        }
      })
    })
  }

  // 更新用户信息
  static updateById(id, updates) {
    return new Promise((resolve, reject) => {
      const fields = Object.keys(updates)
      const values = Object.values(updates)
      const setClause = fields.map(field => `${field} = ?`).join(', ')
      
      const sql = `
        UPDATE users 
        SET ${setClause}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `
      
      database.getDB().run(sql, [...values, id], function(err) {
        if (err) {
          reject(err)
        } else {
          User.findById(id)
            .then(user => resolve(user))
            .catch(reject)
        }
      })
    })
  }

  // 删除用户
  static deleteById(id) {
    return new Promise((resolve, reject) => {
      const sql = 'DELETE FROM users WHERE id = ?'
      
      database.getDB().run(sql, [id], function(err) {
        if (err) {
          reject(err)
        } else {
          resolve(this.changes > 0)
        }
      })
    })
  }

  // 获取所有用户（分页）
  static findAll(limit = 10, offset = 0) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT id, username, email, avatar_url, created_at, updated_at
        FROM users 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
      `
      
      database.getDB().all(sql, [limit, offset], (err, rows) => {
        if (err) {
          reject(err)
        } else {
          const users = rows.map(row => new User(row))
          resolve(users)
        }
      })
    })
  }

  // 获取用户总数
  static count() {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT COUNT(*) as count FROM users'
      
      database.getDB().get(sql, [], (err, row) => {
        if (err) {
          reject(err)
        } else {
          resolve(row.count)
        }
      })
    })
  }

  // 转换为JSON（不包含敏感信息）
  toJSON() {
    return {
      id: this.id,
      username: this.username,
      email: this.email,
      avatar_url: this.avatar_url,
      created_at: this.created_at,
      updated_at: this.updated_at
    }
  }
}

module.exports = User 
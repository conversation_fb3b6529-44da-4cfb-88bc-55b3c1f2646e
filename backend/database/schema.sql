-- PromptHub 数据库 Schema
-- 在 Supabase SQL 编辑器中执行此文件

-- 启用必要的扩展
create extension if not exists "uuid-ossp";

-- 创建提示词类型枚举
create type prompt_type as enum ('image', 'agent', 'mcp');
create type prompt_status as enum ('draft', 'published', 'archived');

-- 创建用户配置表
create table public.user_profiles (
  id uuid references auth.users(id) on delete cascade primary key,
  username text unique,
  full_name text,
  avatar_url text,
  bio text,
  website text,
  twitter text,
  github text,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- 创建提示词表
create table public.prompts (
  id uuid default gen_random_uuid() primary key,
  title text not null,
  content text not null,
  description text,
  type prompt_type not null,
  category text not null,
  tags text[] default '{}',
  user_id uuid references auth.users(id) on delete cascade not null,
  status prompt_status default 'draft',
  usage_count integer default 0,
  rating decimal(3,2) default 0,
  view_count integer default 0,
  like_count integer default 0,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- 创建收藏表
create table public.favorites (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id) on delete cascade not null,
  prompt_id uuid references prompts(id) on delete cascade not null,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  unique(user_id, prompt_id)
);

-- 创建点赞表
create table public.likes (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id) on delete cascade not null,
  prompt_id uuid references prompts(id) on delete cascade not null,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  unique(user_id, prompt_id)
);

-- 创建评论表
create table public.comments (
  id uuid default gen_random_uuid() primary key,
  prompt_id uuid references prompts(id) on delete cascade not null,
  user_id uuid references auth.users(id) on delete cascade not null,
  content text not null,
  rating integer check (rating >= 1 and rating <= 5),
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- 创建使用历史表
create table public.usage_history (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id) on delete cascade not null,
  prompt_id uuid references prompts(id) on delete cascade not null,
  used_at timestamp with time zone default timezone('utc'::text, now())
);

-- 创建分类表
create table public.categories (
  id uuid default gen_random_uuid() primary key,
  name text not null unique,
  description text,
  icon text,
  color text,
  prompt_count integer default 0,
  created_at timestamp with time zone default timezone('utc'::text, now())
);

-- 插入默认分类
insert into public.categories (name, description, icon, color) values
  ('生产力', '提升工作效率的AI提示词', '⚡', '#10B981'),
  ('创意写作', '创意写作和内容创作', '✍️', '#8B5CF6'),
  ('编程开发', '编程和软件开发相关', '💻', '#3B82F6'),
  ('图像生成', 'AI图像生成和设计', '🎨', '#F59E0B'),
  ('数据分析', '数据处理和分析', '📊', '#EF4444'),
  ('教育学习', '教育和学习辅助', '📚', '#06B6D4'),
  ('商业营销', '商业和营销策略', '📈', '#EC4899'),
  ('生活娱乐', '日常生活和娱乐', '🎮', '#84CC16');

-- 设置行级安全策略 (RLS)
alter table public.user_profiles enable row level security;
alter table public.prompts enable row level security;
alter table public.favorites enable row level security;
alter table public.likes enable row level security;
alter table public.comments enable row level security;
alter table public.usage_history enable row level security;
alter table public.categories enable row level security;

-- 用户配置访问策略
create policy "Public profiles are viewable by everyone" on public.user_profiles
  for select using (true);

create policy "Users can update their own profile" on public.user_profiles
  for all using (auth.uid() = id);

-- 提示词访问策略
create policy "Public prompts are viewable by everyone" on public.prompts
  for select using (status = 'published' or auth.uid() = user_id);

create policy "Users can create their own prompts" on public.prompts
  for insert with check (auth.uid() = user_id);

create policy "Users can update their own prompts" on public.prompts
  for update using (auth.uid() = user_id);

create policy "Users can delete their own prompts" on public.prompts
  for delete using (auth.uid() = user_id);

-- 收藏访问策略
create policy "Users can view their own favorites" on public.favorites
  for select using (auth.uid() = user_id);

create policy "Users can manage their own favorites" on public.favorites
  for all using (auth.uid() = user_id);

-- 点赞访问策略
create policy "Likes are viewable by everyone" on public.likes
  for select using (true);

create policy "Users can manage their own likes" on public.likes
  for all using (auth.uid() = user_id);

-- 评论访问策略
create policy "Comments are viewable by everyone" on public.comments
  for select using (true);

create policy "Users can create comments" on public.comments
  for insert with check (auth.uid() = user_id);

create policy "Users can update their own comments" on public.comments
  for update using (auth.uid() = user_id);

create policy "Users can delete their own comments" on public.comments
  for delete using (auth.uid() = user_id);

-- 使用历史访问策略
create policy "Users can view their own usage history" on public.usage_history
  for select using (auth.uid() = user_id);

create policy "Users can create usage history" on public.usage_history
  for insert with check (auth.uid() = user_id);

-- 分类访问策略
create policy "Categories are viewable by everyone" on public.categories
  for select using (true);

-- 创建索引优化查询性能
create index idx_prompts_status on public.prompts(status);
create index idx_prompts_type on public.prompts(type);
create index idx_prompts_category on public.prompts(category);
create index idx_prompts_user_id on public.prompts(user_id);
create index idx_prompts_created_at on public.prompts(created_at desc);
create index idx_prompts_rating on public.prompts(rating desc);
create index idx_prompts_usage_count on public.prompts(usage_count desc);

-- 创建全文搜索索引
create index idx_prompts_search on public.prompts using gin(to_tsvector('chinese', title || ' ' || description || ' ' || content));

-- 创建触发器更新时间戳
create or replace function update_updated_at_column()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

create trigger update_prompts_updated_at before update on public.prompts
  for each row execute function update_updated_at_column();

create trigger update_user_profiles_updated_at before update on public.user_profiles
  for each row execute function update_updated_at_column();

create trigger update_comments_updated_at before update on public.comments
  for each row execute function update_updated_at_column();

-- 创建函数更新提示词统计数据
create or replace function update_prompt_stats()
returns trigger as $$
begin
  if tg_table_name = 'likes' then
    if tg_op = 'INSERT' then
      update public.prompts set like_count = like_count + 1 where id = new.prompt_id;
    elsif tg_op = 'DELETE' then
      update public.prompts set like_count = like_count - 1 where id = old.prompt_id;
    end if;
  elsif tg_table_name = 'usage_history' then
    if tg_op = 'INSERT' then
      update public.prompts set usage_count = usage_count + 1 where id = new.prompt_id;
    end if;
  end if;
  return coalesce(new, old);
end;
$$ language plpgsql;

create trigger update_prompt_like_count after insert or delete on public.likes
  for each row execute function update_prompt_stats();

create trigger update_prompt_usage_count after insert on public.usage_history
  for each row execute function update_prompt_stats();

-- 创建用户注册时自动创建配置的函数
create or replace function public.handle_new_user()
returns trigger as $$
begin
  insert into public.user_profiles (id, username, full_name, avatar_url)
  values (
    new.id,
    new.raw_user_meta_data->>'username',
    new.raw_user_meta_data->>'full_name',
    new.raw_user_meta_data->>'avatar_url'
  );
  return new;
end;
$$ language plpgsql security definer;

create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user(); 
-- PromptHub 初始数据填充
-- 种子数据

USE prompthub;

-- 插入默认分类
INSERT INTO categories (id, name, description, icon, color, prompt_count, sort_order, isActive) VALUES
(1, 'ai-assistant', '生产力', '⚡', '#10B981', 0, 1, 1),
(2, 'copywriting', '创意写作', '✍️', '#8B5CF6', 0, 2, 1),
(3, 'programming', '编程开发', '💻', '#3B82F6', 0, 3, 1),
(4, 'design', '图像生成', '🎨', '#F59E0B', 0, 4, 1),
(5, 'data-analysis', '数据分析', '📊', '#EF4444', 0, 5, 1),
(6, 'education', '教育学习', '📚', '#06B6D4', 0, 6, 1),
(7, 'business-analysis', '商业营销', '📈', '#EC4899', 0, 7, 1),
(8, 'lifestyle', '生活娱乐', '🎮', '#84CC16', 0, 8, 1)
ON DUPLICATE KEY UPDATE
  description = VALUES(description),
  icon = VALUES(icon),
  color = VALUES(color),
  sort_order = VALUES(sort_order);

-- 创建管理员用户
-- admin密码: admin123 (已加密)
-- demo_user密码: demo123 (已加密)
INSERT INTO users (id, username, email, password, role, isActive) VALUES
(1, 'admin', '<EMAIL>', '$2a$10$uAYRBpIVVeolkk7.4N0odeOWIxSujJl3qh8NdeaYlrQlvL9KE3zkm', 'admin', 1),
(2, 'demo_user', '<EMAIL>', '$2a$10$wyXezt83QrNmgPP.fyz26uhE6ntn5rg2ZbMdOMXg.IzxxXoqkEvNi', 'user', 1)
ON DUPLICATE KEY UPDATE
  username = VALUES(username),
  email = VALUES(email),
  role = VALUES(role);

-- 插入示例提示词
INSERT INTO prompts (
  id, userId, title, description, content, category, tags, 
  isPrivate, status, likes, downloads, views, 
  createdAt, updatedAt
) VALUES
(1, 1, '项目管理助手', '帮助您高效管理项目进度和团队协作的AI助手', 
'你是一个专业的项目管理助手，具备以下能力：\n1. 制定项目计划\n2. 跟踪项目进度\n3. 团队协作指导\n4. 风险管理建议\n\n请根据用户的项目需求提供专业的管理建议。', 
'ai-assistant', '["项目管理", "团队协作", "效率提升"]', 
0, 'published', 5, 12, 25, 
DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),

(2, 1, '代码审查专家', '专业的代码审查助手，提供代码质量建议', 
'你是一个资深的代码审查专家，请从以下方面审查代码：\n1. 代码质量和最佳实践\n2. 性能优化建议\n3. 安全性检查\n4. 代码可读性\n5. 架构设计\n\n请提供具体的改进建议和示例代码。', 
'programming', '["代码审查", "质量保证", "编程规范"]', 
0, 'published', 8, 20, 45, 
DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 2 HOUR)),

(3, 2, '创意文案生成器', '帮助创作吸引人的广告文案和营销内容', 
'你是一个富有创意的文案写作专家，擅长：\n1. 广告文案创作\n2. 产品描述撰写\n3. 社交媒体内容\n4. 邮件营销文案\n5. SEO友好内容\n\n请根据品牌调性和目标受众创作引人注目的文案。', 
'copywriting', '["文案写作", "营销推广", "创意内容"]', 
0, 'published', 15, 30, 80, 
DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 1 HOUR)),

(4, 2, '数据分析顾问', '专业的业务数据分析和洞察提供者', 
'你是一个经验丰富的数据分析师，能够：\n1. 数据清洗和预处理\n2. 统计分析和建模\n3. 数据可视化建议\n4. 业务洞察挖掘\n5. 决策支持建议\n\n请基于数据提供客观的分析结论和业务建议。', 
'data-analysis', '["数据分析", "商业洞察", "决策支持"]', 
0, 'published', 12, 18, 60, 
DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 3 HOUR))
ON DUPLICATE KEY UPDATE
  title = VALUES(title),
  description = VALUES(description),
  content = VALUES(content),
  category = VALUES(category),
  tags = VALUES(tags),
  status = VALUES(status);

-- 更新分类的提示词数量
UPDATE categories SET prompt_count = (
  SELECT COUNT(*) FROM prompts WHERE prompts.category = categories.name AND prompts.status = 'published'
); 
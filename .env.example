# PromptHub 环境变量配置模板
# 复制此文件为 .env 并填入实际值

# ===========================================
# 数据库配置
# ===========================================
# MySQL数据库配置
MYSQL_ROOT_PASSWORD=your_strong_root_password
MYSQL_PASSWORD=your_strong_user_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=prompthub
DB_USER=prompthub_user
DB_PASSWORD=your_strong_user_password

# 生产环境数据库（腾讯云）
# DB_HOST=sh-cynosdbmysql-grp-knh8ui5y.sql.tencentcdb.com
# DB_PORT=27410
# DB_NAME=prompthub
# DB_USER=prompthub_user
# DB_PASSWORD=your_production_password

# ===========================================
# 应用配置
# ===========================================
# 后端配置
NODE_ENV=production
PORT=9000

# JWT密钥（请使用强密码）
JWT_SECRET=your_super_secret_jwt_key_change_in_production_minimum_32_characters

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:9000/api

# ===========================================
# 第三方服务配置
# ===========================================
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# 对象存储配置（可选）
OSS_ACCESS_KEY_ID=your_access_key
OSS_ACCESS_KEY_SECRET=your_secret_key
OSS_BUCKET=your_bucket_name
OSS_REGION=your_region

# ===========================================
# 安全配置
# ===========================================
# CORS允许的域名
CORS_ORIGIN=http://localhost:3000,https://your-domain.com

# 速率限制
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ===========================================
# 监控和日志
# ===========================================
# 日志级别
LOG_LEVEL=info

# 监控配置
ENABLE_MONITORING=true
SENTRY_DSN=your_sentry_dsn

# ===========================================
# 开发环境配置
# ===========================================
# 开发模式下的配置
DEBUG=false
ENABLE_SWAGGER=false

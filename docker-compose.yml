version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend/web
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://backend:9000/api
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - prompthub-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "9000:9000"
    environment:
      - NODE_ENV=production
      - PORT=9000
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=prompthub
      - DB_USER=prompthub_user
      - DB_PASSWORD=prompthub_password
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
    depends_on:
      mysql:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - prompthub-network
    volumes:
      - ./backend/logs:/app/logs

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: prompthub-mysql
    environment:
      MYSQL_DATABASE: prompthub
      MYSQL_USER: prompthub_user
      MYSQL_PASSWORD: prompthub_password
      MYSQL_ROOT_PASSWORD: root_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/database/schemas:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - prompthub-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
      interval: 10s

  # Redis 缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: prompthub-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - prompthub-network

  # Elasticsearch 搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: prompthub-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - prompthub-network

  # Kibana (可选，用于 Elasticsearch 可视化)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: prompthub-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - prompthub-network

  # MinIO (本地 S3 兼容存储)
  minio:
    image: minio/minio:latest
    container_name: prompthub-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - prompthub-network

  # Mailhog (邮件测试工具)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: prompthub-mailhog
    ports:
      - "8025:8025"  # Web UI
      - "1025:1025"  # SMTP
    networks:
      - prompthub-network

networks:
  prompthub-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
  elasticsearch_data:
  minio_data: 